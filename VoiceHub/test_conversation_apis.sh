#!/bin/bash

# ConversationController接口测试脚本
# 测试VoiceHub后端的所有对话相关API接口

BASE_URL="http://localhost:8080/api"
TOKEN="eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0ZXN0dXNlcjIwMjUiLCJpYXQiOjE3NTYzODgxMTUsImV4cCI6MTc1NjQ3NDUxNX0.JA_2VtOrR96uZEJRjsxtXRZ_rxZ0z0CjhRw7h-zpqR7zKxg6bcW3GdAembHFovjyA9R_kA86K8g2-Om7Kt3CMg"
CONVERSATION_ID=""
USER_ID="14"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印测试结果
print_result() {
    local test_name="$1"
    local status_code="$2"
    local expected_status="$3"
    local response="$4"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} $test_name"
    else
        echo -e "${RED}❌ FAIL${NC} $test_name"
    fi
    echo -e "   Status: $status_code (Expected: $expected_status)"
    echo "   Response: $(echo "$response" | head -c 200)..."
    echo "------------------------------------------------------------"
    
    if [ "$status_code" = "$expected_status" ]; then
        return 0
    else
        return 1
    fi
}

# 设置认证
setup_authentication() {
    echo -e "${BLUE}=== 设置认证 ===${NC}"
    
    # 使用已经获取的token
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ 使用已配置的token${NC}"
        echo "   Token: ${TOKEN:0:20}..."
        return 0
    fi
    
    local username="testuser_$(date +%s)"
    local password="testpass123"
    local email="test_$(date +%s)@example.com"
    
    # 1. 注册用户
    echo "🔄 注册用户..."
    local register_response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"password\":\"$password\",\"confirmPassword\":\"$password\",\"email\":\"$email\"}" \
        "$BASE_URL/auth/register")
    
    local register_status_code="${register_response: -3}"
    local register_body="${register_response%???}"
    
    if [[ "$register_status_code" != "200" && "$register_status_code" != "201" ]]; then
        echo -e "${YELLOW}⚠️ 注册失败，尝试使用已有用户...${NC}"
        username="admin"
        password="admin123"
        email="<EMAIL>"
    else
        echo -e "${GREEN}✅ 用户注册成功${NC}"
    fi
    
    # 2. 登录获取token - 使用正确的字段名usernameOrEmail
    echo "🔄 用户登录..."
    local login_response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "{\"usernameOrEmail\":\"$username\",\"password\":\"$password\"}" \
        "$BASE_URL/auth/login")
    
    local login_status_code="${login_response: -3}"
    local login_body="${login_response%???}"
    
    if [ "$login_status_code" = "200" ]; then
        TOKEN=$(echo "$login_body" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
        USER_ID=$(echo "$login_body" | grep -o '"id":[^,}]*' | head -n1 | cut -d':' -f2)
        
        if [ -n "$TOKEN" ]; then
            echo -e "${GREEN}✅ 登录成功，获取到token${NC}"
            echo "   Token: ${TOKEN:0:20}..."
            return 0
        fi
    fi
    
    echo -e "${RED}❌ 登录失败: $login_status_code - $login_body${NC}"
    return 1
}

# 测试创建对话
test_create_conversation() {
    local response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"title":"测试对话","type":"GENERAL"}' \
        "$BASE_URL/conversations")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if print_result "创建对话" "$status_code" "200" "$body"; then
        CONVERSATION_ID=$(echo "$body" | grep -o '"id":[^,}]*' | head -n1 | cut -d':' -f2)
        if [ -n "$CONVERSATION_ID" ]; then
            echo "   保存对话ID: $CONVERSATION_ID"
        fi
        return 0
    fi
    return 1
}

# 测试发送消息
test_send_message() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过发送消息测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"content":"Hello, this is a test message","isVoice":false}' \
        "$BASE_URL/conversations/$CONVERSATION_ID/messages")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "发送消息" "$status_code" "200" "$body"
}

# 测试获取对话列表
test_get_conversations() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations?page=0&size=20")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取对话列表" "$status_code" "200" "$body"
}

# 测试获取对话详情
test_get_conversation_detail() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过对话详情测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取对话详情" "$status_code" "200" "$body"
}

# 测试获取消息历史
test_get_messages() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过消息历史测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID/messages")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取消息历史" "$status_code" "200" "$body"
}

# 测试更新对话
test_update_conversation() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过更新对话测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"title":"更新后的测试对话","type":"ASSISTANT"}' \
        "$BASE_URL/conversations/$CONVERSATION_ID")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "更新对话" "$status_code" "200" "$body"
}

# 测试搜索对话
test_search_conversations() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/search?keyword=test")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "搜索对话" "$status_code" "200" "$body"
}

# 测试根据类型获取对话
test_get_conversations_by_type() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/type/GENERAL")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "根据类型获取对话" "$status_code" "200" "$body"
}

# 测试获取收藏对话
test_get_favorite_conversations() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/favorites")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取收藏对话" "$status_code" "200" "$body"
}

# 测试获取归档对话
test_get_archived_conversations() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/archived")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取归档对话" "$status_code" "200" "$body"
}

# 测试获取最近对话
test_get_recent_conversations() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/recent?days=7")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取最近对话" "$status_code" "200" "$body"
}

# 测试切换收藏状态
test_toggle_favorite() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过切换收藏测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID/favorite")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "切换收藏状态" "$status_code" "200" "$body"
}

# 测试切换归档状态
test_toggle_archive() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过切换归档测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID/archive")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "切换归档状态" "$status_code" "200" "$body"
}

# 测试更新对话状态
test_update_status() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过更新状态测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X PUT \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"status":"ACTIVE"}' \
        "$BASE_URL/conversations/$CONVERSATION_ID/status")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "更新对话状态" "$status_code" "200" "$body"
}

# 测试生成对话摘要
test_generate_summary() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过生成摘要测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X POST \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID/summary")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "生成对话摘要" "$status_code" "200" "$body"
}

# 测试获取对话统计
test_get_conversation_stats() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/stats")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取对话统计" "$status_code" "200" "$body"
}

# 测试搜索消息
test_search_messages() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/messages/search?keyword=test")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "搜索消息" "$status_code" "200" "$body"
}

# 测试根据情感获取消息
test_get_messages_by_emotion() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/messages/emotion/neutral")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "根据情感获取消息" "$status_code" "200" "$body"
}

# 测试获取情感统计
test_get_emotion_stats() {
    local response=$(curl -s -w "%{http_code}" -X GET \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/emotions/stats")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "获取情感统计" "$status_code" "200" "$body"
}

# 测试删除对话
test_delete_conversation() {
    if [ -z "$CONVERSATION_ID" ]; then
        echo -e "${RED}❌ 跳过删除对话测试 - 无对话ID${NC}"
        return 0
    fi
    
    local response=$(curl -s -w "%{http_code}" -X DELETE \
        -H "Authorization: Bearer $TOKEN" \
        "$BASE_URL/conversations/$CONVERSATION_ID")
    
    local status_code="${response: -3}"
    local body="${response%???}"
    
    print_result "删除对话" "$status_code" "200" "$body"
}

# 主测试函数
run_all_tests() {
    echo -e "${BLUE}🚀 开始ConversationController接口测试...${NC}"
    echo "================================================================================"
    
    # 设置认证
    if ! setup_authentication; then
        echo -e "${RED}❌ 认证设置失败，无法继续测试${NC}"
        return 1
    fi
    
    echo
    echo -e "${BLUE}📋 执行 20 个测试用例:${NC}"
    echo
    
    # 测试用例列表
    local passed=0
    local total=20
    
    # 执行所有测试
    echo "🔄 执行: 1. 创建对话"
    test_create_conversation && ((passed++))
    echo
    
    echo "🔄 执行: 2. 发送消息"
    test_send_message && ((passed++))
    echo
    
    echo "🔄 执行: 3. 获取对话列表"
    test_get_conversations && ((passed++))
    echo
    
    echo "🔄 执行: 4. 获取对话详情"
    test_get_conversation_detail && ((passed++))
    echo
    
    echo "🔄 执行: 5. 获取消息历史"
    test_get_messages && ((passed++))
    echo
    
    echo "🔄 执行: 6. 更新对话"
    test_update_conversation && ((passed++))
    echo
    
    echo "🔄 执行: 7. 搜索对话"
    test_search_conversations && ((passed++))
    echo
    
    echo "🔄 执行: 8. 根据类型获取对话"
    test_get_conversations_by_type && ((passed++))
    echo
    
    echo "🔄 执行: 9. 获取收藏对话"
    test_get_favorite_conversations && ((passed++))
    echo
    
    echo "🔄 执行: 10. 获取归档对话"
    test_get_archived_conversations && ((passed++))
    echo
    
    echo "🔄 执行: 11. 获取最近对话"
    test_get_recent_conversations && ((passed++))
    echo
    
    echo "🔄 执行: 12. 切换收藏状态"
    test_toggle_favorite && ((passed++))
    echo
    
    echo "🔄 执行: 13. 切换归档状态"
    test_toggle_archive && ((passed++))
    echo
    
    echo "🔄 执行: 14. 更新对话状态"
    test_update_status && ((passed++))
    echo
    
    echo "🔄 执行: 15. 生成对话摘要"
    test_generate_summary && ((passed++))
    echo
    
    echo "🔄 执行: 16. 获取对话统计"
    test_get_conversation_stats && ((passed++))
    echo
    
    echo "🔄 执行: 17. 搜索消息"
    test_search_messages && ((passed++))
    echo
    
    echo "🔄 执行: 18. 根据情感获取消息"
    test_get_messages_by_emotion && ((passed++))
    echo
    
    echo "🔄 执行: 19. 获取情感统计"
    test_get_emotion_stats && ((passed++))
    echo
    
    echo "🔄 执行: 20. 删除对话"
    test_delete_conversation && ((passed++))
    echo
    
    # 测试总结
    echo "================================================================================"
    local percentage=$((passed * 100 / total))
    echo -e "${BLUE}📊 测试完成! 通过: $passed/$total ($percentage%)${NC}"
    
    if [ $passed -eq $total ]; then
        echo -e "${GREEN}🎉 所有测试通过!${NC}"
        return 0
    else
        local failed=$((total - passed))
        echo -e "${YELLOW}⚠️  $failed 个测试失败，需要修复${NC}"
        return 1
    fi
}

# 执行测试
run_all_tests