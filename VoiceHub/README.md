# 🎙️ VoiceHub - 轻量级语音笔记和助手平台

<div align="center">

![VoiceHub Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=VoiceHub)

**实用的语音转文字工具 + 智能AI助手 - 让语音记录更高效**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Java](https://img.shields.io/badge/Java-17+-orange.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.0+-green.svg)](https://spring.io/projects/spring-boot)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

[🚀 快速开始](#快速开始) • [✨ 核心功能](#核心功能) • [🏗️ 技术架构](#技术架构) • [💰 成本说明](#成本说明) • [📚 开发指南](#开发指南)

</div>

---

## 📋 目录

- [项目简介](#项目简介)
- [核心功能](#核心功能)
- [创意AI功能](#创意ai功能)
- [技术架构](#技术架构)
- [快速开始](#快速开始)
- [成本说明](#成本说明)
- [配置指南](#配置指南)
- [开发指南](#开发指南)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 🌟 项目简介

VoiceHub 是一个轻量级的语音笔记和智能助手平台，专注于提供实用的语音转文字功能和创意AI交互体验。项目采用现代化技术栈，支持多种大模型接入，为个人用户和开发者提供高效的语音处理解决方案。

### 🎯 设计理念

- **实用优先**: 专注于真正有用的核心功能
- **成本友好**: 提供多种成本选择，包括本地化方案
- **易于部署**: 一键启动，快速上手
- **开源透明**: 代码开源，功能透明，无隐藏成本
- **可扩展**: 模块化设计，支持自定义扩展

### 🏆 核心优势

- ✅ **多种语音识别方案** - 支持在线API和本地模型
- ✅ **智能语音笔记** - 自动转写、分类、搜索
- ✅ **多模型AI对话** - 支持ChatGPT、通义千问、智谱AI等
- ✅ **创意AI功能** - 语音摘要、智能分析、内容生成
- ✅ **隐私保护** - 支持本地部署，数据自主控制
- ✅ **轻量级部署** - Docker一键启动，资源消耗低

## ✨ 核心功能

### 🎤 语音处理核心

#### 语音转文字
- **多种识别方案**: 支持百度API、本地Whisper模型等
- **实时转写**: 边说边转写，实时显示结果
- **多语言支持**: 中文、英文等主流语言
- **格式灵活**: 支持多种音频格式输入

#### 智能语音笔记
- **一键录制**: 简单易用的录音界面
- **自动转写**: 录音完成后自动生成文字
- **智能分类**: 基于内容自动分类和标签
- **全文搜索**: 快速查找历史笔记内容

### 🤖 AI智能助手

#### 基础对话功能
- **一问一答**: 类似ChatGPT的基础对话体验
- **多轮对话**: 保持上下文的连续对话
- **快速回复**: 智能建议和快捷回复
- **对话历史**: 保存和管理对话记录

#### 多模型支持
- **ChatGPT集成**: 支持GPT-3.5/4.0模型
- **通义千问**: 阿里云大模型支持
- **智谱AI**: 国产大模型选择
- **模型切换**: 根据需求灵活选择模型

#### 基础日程管理
- **语音创建**: 通过语音快速创建日程
- **智能解析**: 自然语言时间识别
- **提醒功能**: 简单的提醒系统

## 🎨 创意AI功能

### 📝 智能内容处理
- **语音摘要**: 自动生成录音内容摘要
- **关键词提取**: 智能提取重要信息点
- **内容分析**: 分析语音内容的主题和情感
- **格式转换**: 将语音内容转换为不同格式（报告、邮件等）

### 🧠 智能助理功能
- **会议纪要**: 自动生成会议记录和行动项
- **学习笔记**: 将语音内容整理为结构化笔记
- **创意写作**: 基于语音输入进行创意扩展
- **多语言翻译**: 语音内容的实时翻译

### 🎯 个性化服务
- **语音风格分析**: 分析用户说话习惯和偏好
- **智能建议**: 基于历史数据提供个性化建议
- **内容推荐**: 推荐相关的学习或工作内容
- **效率优化**: 分析并优化用户的语音使用习惯

## 🏗️ 技术架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[React Web App] --> B[语音录制组件]
        A --> C[AI对话界面]
        A --> D[笔记管理]
        A --> E[日程管理]
    end

    subgraph "后端服务层"
        F[Spring Boot API] --> G[用户认证]
        F --> H[语音处理服务]
        F --> I[AI对话服务]
        F --> J[笔记管理服务]
    end

    subgraph "AI服务层"
        K[多模型支持]
        K --> L[ChatGPT]
        K --> M[通义千问]
        K --> N[智谱AI]
        O[语音识别]
        O --> P[百度API]
        O --> Q[本地Whisper]
    end

    subgraph "数据存储层"
        R[PostgreSQL] --> S[用户数据]
        R --> T[对话记录]
        R --> U[语音笔记]
        V[Redis缓存] --> W[会话数据]
        X[文件存储] --> Y[音频文件]
    end

    A --> F
    F --> K
    F --> O
    F --> R
    F --> V
```

### 🛠️ 技术栈

#### 后端技术栈
- **核心框架**: Spring Boot 3.0+ (Java 17+)
- **数据库**: PostgreSQL 14+ + Redis 6.0+ (缓存)
- **安全认证**: Spring Security + JWT
- **API文档**: OpenAPI 3.0 + Swagger UI
- **测试**: JUnit 5 + Mockito

#### 前端技术栈
- **核心框架**: React 18+ + TypeScript 5.0+
- **UI组件库**: Shadcn/UI + Tailwind CSS
- **状态管理**: React Hooks + Context API
- **HTTP客户端**: Axios
- **音频处理**: Web Audio API + MediaRecorder API
- **构建工具**: Vite + ESBuild

#### AI与语音服务
- **大语言模型**: ChatGPT、通义千问、智谱AI等
- **语音识别**: 百度API、本地Whisper模型
- **语音合成**: 百度TTS API
- **音频处理**: Web Audio API

#### 部署方案
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx (可选)
- **监控**: Spring Boot Actuator

### 🔧 核心组件

#### 语音处理服务
<augment_code_snippet path="VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/SpeechToTextService.java" mode="EXCERPT">
````java
@Service
public class SpeechToTextService {
    // 语音转文字主方法
    public String convertSpeechToText(byte[] audioData, String format, int rate);

    // 支持多种识别引擎
    private String recognizeSpeech(byte[] audioData, String format, int rate, String token);
}
````
</augment_code_snippet>

#### AI对话服务
<augment_code_snippet path="VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/service/OpenAIService.java" mode="EXCERPT">
````java
@Service
public class OpenAIService {
    // 生成AI回复
    public String generateResponse(Conversation conversation, String userMessage);

    // 情感分析
    public String analyzeEmotion(String text);

    // 内容摘要
    public String generateSummary(String content);
}
````
</augment_code_snippet>

## 🚀 快速开始

### 前置要求

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+

### 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-org/voicehub.git
cd voicehub

# 2. 配置环境变量
cp docker/.env.example docker/.env
# 编辑 docker/.env 文件，设置API密钥

# 3. 启动服务
docker-compose up -d

# 4. 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:8080
```

### 基础配置

```bash
# 必需的API密钥 (至少配置一个AI模型)
OPENAI_API_KEY=your_openai_api_key          # ChatGPT
QIANWEN_API_KEY=your_qianwen_api_key        # 通义千问
ZHIPU_API_KEY=your_zhipu_api_key            # 智谱AI

# 语音识别配置 (可选，不配置则使用本地Whisper)
BAIDU_APP_ID=your_baidu_app_id
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key

# 数据库配置 (使用默认值即可)
DB_PASSWORD=voicehub_password
REDIS_PASSWORD=redis_password
JWT_SECRET=your_jwt_secret_minimum_32_characters
```

### 验证安装

```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 预期响应
{
  "status": "UP",
  "components": {
    "db": {"status": "UP"},
    "redis": {"status": "UP"}
  }
}
```

## � 成本说明

### API成本估算

VoiceHub支持多种AI服务，您可以根据预算选择合适的方案：

#### 大语言模型成本 (月使用量估算)
| 服务商 | 模型 | 每1K tokens成本 | 月成本估算* |
|--------|------|----------------|-------------|
| OpenAI | GPT-3.5-turbo | $0.002 | $20-60 |
| OpenAI | GPT-4 | $0.03 | $300-900 |
| 阿里云 | 通义千问 | ¥0.008 | ¥80-240 |
| 智谱AI | GLM-4 | ¥0.1 | ¥1000-3000 |

*基于每天50次对话，每次平均1000 tokens

#### 语音识别成本
| 服务商 | 成本 | 月成本估算* |
|--------|------|-------------|
| 百度API | ¥0.0048/分钟 | ¥15-45 |
| 本地Whisper | 免费 | ¥0 |

*基于每天30分钟语音转写

### 成本优化建议

#### 💡 低成本方案
- 使用本地Whisper进行语音识别 (免费)
- 选择GPT-3.5-turbo或通义千问 (成本较低)
- 限制每日API调用次数
- **月成本预估**: ¥20-100

#### 🚀 标准方案
- 百度API语音识别 + GPT-3.5-turbo
- 适合个人和小团队使用
- **月成本预估**: ¥100-300

#### 🏢 高级方案
- 百度API + GPT-4 或多模型切换
- 适合商业使用或高频用户
- **月成本预估**: ¥300-1000+

### 本地化部署优势

- **隐私保护**: 数据不离开本地环境
- **成本控制**: 避免API调用费用
- **自主可控**: 不依赖外部服务稳定性
- **定制化**: 可根据需求调整模型参数

## 📱 配置指南

### 环境变量详细配置

#### AI模型配置
```bash
# OpenAI配置
OPENAI_API_KEY=sk-your-openai-key
OPENAI_API_URL=https://api.openai.com/v1  # 可选，默认官方API
OPENAI_MODEL=gpt-3.5-turbo                # 可选，默认gpt-3.5-turbo

# 通义千问配置
QIANWEN_API_KEY=your-qianwen-key
QIANWEN_MODEL=qwen-turbo                  # 可选

# 智谱AI配置
ZHIPU_API_KEY=your-zhipu-key
ZHIPU_MODEL=glm-4                         # 可选
```

#### 语音识别配置
```bash
# 百度语音识别 (推荐)
BAIDU_APP_ID=your-app-id
BAIDU_API_KEY=your-api-key
BAIDU_SECRET_KEY=your-secret-key

# 本地Whisper配置 (免费但需要GPU)
WHISPER_MODEL=base                        # tiny, base, small, medium, large
WHISPER_DEVICE=cpu                        # cpu 或 cuda
```

#### 功能开关配置
```bash
# 启用/禁用功能模块
ENABLE_AI_CHAT=true                       # AI对话功能
ENABLE_VOICE_NOTES=true                   # 语音笔记功能
ENABLE_SCHEDULE=true                      # 日程管理功能
ENABLE_ANALYTICS=false                    # 数据分析功能 (可选)

# 限制配置
MAX_AUDIO_DURATION=300                    # 最大录音时长(秒)
MAX_DAILY_API_CALLS=1000                  # 每日API调用限制
```

### 使用场景示例

#### 📝 会议记录场景
1. 点击录音按钮开始记录会议
2. 系统自动转写语音为文字
3. AI自动生成会议摘要和行动项
4. 一键分享会议纪要

#### 📚 学习笔记场景
1. 录制课程或讲座音频
2. 自动转写并智能分段
3. AI提取关键知识点
4. 生成结构化学习笔记

#### 💬 语音助手场景
1. 语音提问或下达指令
2. AI理解意图并执行任务
3. 语音播报执行结果
4. 支持多轮对话交互

## 📚 API文档

### 核心API接口

#### 认证相关
```http
POST /api/auth/register     # 用户注册
POST /api/auth/login        # 用户登录
POST /api/auth/logout       # 用户登出
```

#### 语音处理
```http
POST /api/speech/recognize  # 语音识别
POST /api/speech/synthesize # 语音合成 (可选)
```

#### AI对话
```http
GET    /api/conversations           # 获取对话列表
POST   /api/conversations           # 创建新对话
POST   /api/conversations/{id}/messages  # 发送消息
```

#### 语音笔记
```http
GET    /api/voice-notes             # 获取笔记列表
POST   /api/voice-notes             # 创建新笔记
GET    /api/voice-notes/search      # 搜索笔记
DELETE /api/voice-notes/{id}        # 删除笔记
```

#### 日程管理
```http
GET    /api/schedules               # 获取日程列表
POST   /api/schedules               # 创建新日程
PUT    /api/schedules/{id}          # 更新日程
```

### API认证

所有API请求需要包含JWT令牌：

```http
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

### 在线文档

启动服务后访问：http://localhost:8080/swagger-ui.html

## 🛠️ 开发指南

### 开发环境搭建

#### 方式一：Docker开发 (推荐)
```bash
# 克隆项目
git clone https://github.com/your-org/voicehub.git
cd voicehub

# 配置环境变量
cp docker/.env.example docker/.env
# 编辑 .env 文件，添加必要的API密钥

# 启动开发环境
docker-compose -f docker/docker-compose.dev.yml up -d
```

#### 方式二：本地开发
```bash
# 后端开发
cd backend/voicehub-backend
./mvnw spring-boot:run

# 前端开发 (新终端)
cd frontend/voicehub-ui
npm install && npm run dev
```

### 项目结构

```
VoiceHub/
├── backend/voicehub-backend/          # Spring Boot后端
│   ├── src/main/java/com/voicehub/
│   │   ├── controller/                # REST API控制器
│   │   ├── service/                   # 核心业务服务
│   │   ├── entity/                    # 数据实体
│   │   ├── repository/                # 数据访问层
│   │   └── config/                    # 配置类
│   └── src/main/resources/
│       └── application.yml            # 应用配置
├── frontend/voicehub-ui/              # React前端
│   ├── src/
│   │   ├── components/                # UI组件
│   │   ├── services/                  # API服务
│   │   └── types/                     # TypeScript类型
│   └── package.json                   # 前端依赖
├── docker/                            # Docker配置
│   ├── docker-compose.yml             # 开发环境
│   └── .env.example                   # 环境变量模板
├── database/                          # 数据库脚本
└── README.md                          # 项目文档
```

### 开发规范

- **代码风格**: Java遵循Google Style Guide，TypeScript使用ESLint + Prettier
- **提交规范**: 使用conventional commits格式
- **测试要求**: 新功能需要包含单元测试

### 运行测试

```bash
# 后端测试
./mvnw test

# 前端测试
npm run test
```



## 🤝 贡献指南

欢迎社区贡献！我们特别欢迎以下类型的贡献：

### 🎯 优先贡献方向

- **新的AI模型集成** - 支持更多大语言模型
- **语音识别优化** - 提升识别准确率和速度
- **创意AI功能** - 基于语音的创新应用
- **本地化支持** - 多语言界面和文档
- **性能优化** - 降低资源消耗和响应延迟

### 📝 贡献流程

1. Fork项目并创建功能分支
2. 实现功能并添加测试
3. 提交PR并描述变更内容
4. 等待代码审查和合并

### 💡 功能建议

如果您有好的想法，欢迎创建Issue讨论：
- 新的AI应用场景
- 用户体验改进建议
- 技术架构优化方案

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

## 🙏 致谢

感谢以下开源项目：
- [Spring Boot](https://spring.io/projects/spring-boot) - Java应用框架
- [React](https://reactjs.org/) - 前端框架
- [PostgreSQL](https://www.postgresql.org/) - 数据库
- [Shadcn/UI](https://ui.shadcn.com/) - UI组件库

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个Star！**

**🎙️ 让语音交互更简单、更实用！**

Made with ❤️ by VoiceHub Team

</div>
