import React, { useState, useEffect } from 'react';
import Dashboard from './components/layout/Dashboard';
import LoginForm from './components/auth/LoginForm';
import RegisterForm from './components/auth/RegisterForm';
import { ThemeProvider } from './components/theme-provider';
import authService, { User as AuthUser } from './services/authService';

// 适配器接口，将AuthUser转换为组件需要的User格式
interface User {
  name: string;
  email: string;
  avatar?: string;
}

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [authView, setAuthView] = useState<'login' | 'register'>('login');
  const [isLoading, setIsLoading] = useState(false);

  // 检查本地存储的登录状态
  useEffect(() => {
    if (authService.isAuthenticated()) {
      const authUser = authService.getCurrentUserFromStorage();
      if (authUser) {
        // 转换AuthUser到User格式
        const user: User = {
          name: authUser.fullName || authUser.username,
          email: authUser.email,
          avatar: authUser.avatarUrl,
        };
        setCurrentUser(user);
        setIsAuthenticated(true);
      }
    }
  }, []);

  const handleLogin = async (email: string, password: string) => {
    setIsLoading(true);

    try {
      // 检查是否是演示账户
      if (email === '<EMAIL>' && password === 'demo123') {
        // 演示账户直接登录，不调用API
        const demoUser: User = {
          name: 'VoiceHub演示用户',
          email: '<EMAIL>',
          avatar: ''
        };

        setCurrentUser(demoUser);
        setIsAuthenticated(true);
        console.log('✅ Demo login successful');
        return;
      }

      // 真实账户调用API
      const response = await authService.login({
        usernameOrEmail: email,
        password: password,
      });

      if (response.success && response.user) {
        // 转换AuthUser到User格式
        const user: User = {
          name: response.user.fullName || response.user.username,
          email: response.user.email,
          avatar: response.user.avatarUrl,
        };

        setCurrentUser(user);
        setIsAuthenticated(true);
        console.log('✅ Login successful');
      } else {
        console.error('❌ Login failed:', response.error);
        alert(response.error || '登录失败，请检查用户名和密码');
      }
    } catch (error: any) {
      console.error('❌ Login error:', error);

      // 如果是演示账户，即使API失败也允许登录
      if (email === '<EMAIL>') {
        const demoUser: User = {
          name: 'VoiceHub演示用户',
          email: '<EMAIL>',
          avatar: ''
        };

        setCurrentUser(demoUser);
        setIsAuthenticated(true);
        console.log('✅ Demo login successful (fallback)');
      } else {
        alert(error.message || '登录失败，请稍后重试');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (name: string, email: string, password: string) => {
    setIsLoading(true);

    try {
      const response = await authService.register({
        username: email.split('@')[0], // 使用邮箱前缀作为用户名
        email: email,
        password: password,
        confirmPassword: password, // 前端已经验证过密码一致性
        fullName: name,
      });

      if (response.success && response.user) {
        // 转换AuthUser到User格式
        const user: User = {
          name: response.user.fullName || response.user.username,
          email: response.user.email,
          avatar: response.user.avatarUrl,
        };

        setCurrentUser(user);
        setIsAuthenticated(true);
        console.log('✅ Registration successful');
      } else {
        console.error('❌ Registration failed:', response.error);
        alert(response.error || '注册失败，请检查输入信息');
      }
    } catch (error: any) {
      console.error('❌ Registration error:', error);
      alert(error.message || '注册失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setCurrentUser(null);
      setIsAuthenticated(false);
      console.log('✅ Logout successful');
    } catch (error: any) {
      console.error('❌ Logout error:', error);
      // 即使API调用失败，也要清除本地状态
      setCurrentUser(null);
      setIsAuthenticated(false);
    }
  };

  return (
    <ThemeProvider defaultTheme="dark" storageKey="voicehub-ui-theme">
      {!isAuthenticated ? (
        authView === 'login' ? (
          <LoginForm
            onLogin={handleLogin}
            onSwitchToRegister={() => setAuthView('register')}
            isLoading={isLoading}
          />
        ) : (
          <RegisterForm
            onRegister={handleRegister}
            onSwitchToLogin={() => setAuthView('login')}
            isLoading={isLoading}
          />
        )
      ) : (
        <Dashboard
          user={currentUser || undefined}
          onLogout={handleLogout}
        />
      )}
    </ThemeProvider>
  );
}

export default App;