import React, { useState } from 'react';
import {
  Sparkles,
  FileText,
  Languages,
  Mail,
  BookOpen,
  Lightbulb,
  Mic,
  Upload,
  Download,
  Copy,
  RefreshCw
} from 'lucide-react';
import { creativeAIService, voiceService } from '@/services/apiService';
import type { AITask } from '@/services/mockDataService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

// 移除重复的类型定义，使用从服务导入的类型

// 格式转换选项
const FORMAT_OPTIONS = [
  { value: 'email', label: '📧 邮件格式', description: '转换为正式邮件格式' },
  { value: 'report', label: '📊 报告格式', description: '生成结构化报告' },
  { value: 'summary', label: '📝 摘要格式', description: '提取核心要点' },
  { value: 'outline', label: '📋 大纲格式', description: '生成内容大纲' },
  { value: 'qa', label: '❓ 问答格式', description: '转换为问答形式' },
];

// 翻译语言选项
const LANGUAGE_OPTIONS = [
  { value: 'en', label: '🇺🇸 英语' },
  { value: 'ja', label: '🇯🇵 日语' },
  { value: 'ko', label: '🇰🇷 韩语' },
  { value: 'fr', label: '🇫🇷 法语' },
  { value: 'de', label: '🇩🇪 德语' },
  { value: 'es', label: '🇪🇸 西班牙语' },
];

interface CreativeAIWidgetProps {
  expanded?: boolean;
  className?: string;
}

export default function CreativeAIWidget({ expanded = false, className = '' }: CreativeAIWidgetProps) {
  const [inputText, setInputText] = useState('');
  const [tasks, setTasks] = useState<AITask[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('summary');
  const [selectedLanguage, setSelectedLanguage] = useState('en');

  // 执行AI任务
  const executeAITask = async (type: AITask['type'], additionalParams?: any) => {
    if (!inputText.trim()) return;

    const newTask: AITask = {
      id: Date.now().toString(),
      type,
      input: inputText,
      output: '',
      status: 'processing',
      createdAt: new Date(),
      model: 'gpt-3.5-turbo'
    };

    setTasks(prev => [newTask, ...prev]);
    setIsProcessing(true);

    try {
      // 使用API服务处理内容
      const result = await creativeAIService.processContent(inputText, type, additionalParams);

      setTasks(prev => prev.map(task =>
        task.id === newTask.id
          ? { ...task, output: result, status: 'completed' }
          : task
      ));
    } catch (error) {
      console.error('AI processing failed:', error);

      setTasks(prev => prev.map(task =>
        task.id === newTask.id
          ? { ...task, output: 'AI处理失败，请重试', status: 'error' }
          : task
      ));
    } finally {
      setIsProcessing(false);
    }
  };

  // 删除generateMockOutput函数，现在使用API服务

  // 复制结果
  const handleCopyResult = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: 添加toast提示
  };

  // 语音输入
  const handleVoiceInput = async () => {
    try {
      // 模拟录音过程
      const mockAudioBlob = new Blob([], { type: 'audio/wav' });
      const transcription = await voiceService.recognizeSpeech(mockAudioBlob);
      setInputText(transcription);
    } catch (error) {
      console.error('Voice input failed:', error);
      setInputText('语音输入失败，请重试');
    }
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full h-[700px]' : 'h-[500px]'
    } ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <span>创意AI工具</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        <div className="px-6 mb-4">
          {/* 输入区域 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="输入要处理的文本内容，或点击语音输入..."
                className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[80px]"
                rows={3}
              />
              <Button
                variant="ghost"
                onClick={handleVoiceInput}
                className="text-blue-200 hover:bg-white/10"
              >
                <Mic className="w-4 h-4" />
              </Button>
            </div>
            
            {/* AI功能按钮 */}
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => executeAITask('summary')}
                disabled={!inputText.trim() || isProcessing}
                className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-200 border border-blue-400/30"
                size="sm"
              >
                <FileText className="w-3 h-3 mr-1" />
                智能摘要
              </Button>
              
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LANGUAGE_OPTIONS.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={() => executeAITask('translate', { language: selectedLanguage })}
                disabled={!inputText.trim() || isProcessing}
                className="bg-green-500/20 hover:bg-green-500/30 text-green-200 border border-green-400/30"
                size="sm"
              >
                <Languages className="w-3 h-3 mr-1" />
                翻译
              </Button>
              
              <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {FORMAT_OPTIONS.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={() => executeAITask('format', { format: selectedFormat })}
                disabled={!inputText.trim() || isProcessing}
                className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 border border-purple-400/30"
                size="sm"
              >
                <Mail className="w-3 h-3 mr-1" />
                格式转换
              </Button>
              
              <Button
                onClick={() => executeAITask('analyze')}
                disabled={!inputText.trim() || isProcessing}
                className="bg-orange-500/20 hover:bg-orange-500/30 text-orange-200 border border-orange-400/30"
                size="sm"
              >
                <BookOpen className="w-3 h-3 mr-1" />
                内容分析
              </Button>
              
              <Button
                onClick={() => executeAITask('creative')}
                disabled={!inputText.trim() || isProcessing}
                className="bg-pink-500/20 hover:bg-pink-500/30 text-pink-200 border border-pink-400/30"
                size="sm"
              >
                <Lightbulb className="w-3 h-3 mr-1" />
                创意扩展
              </Button>
            </div>
          </div>
        </div>

        {/* 结果区域 */}
        <ScrollArea className="flex-1 px-6">
          <div className="space-y-3">
            {tasks.map((task) => (
              <Card key={task.id} className="bg-white/5 border-white/10">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="capitalize">
                        {task.type}
                      </Badge>
                      <span className="text-xs text-blue-300">
                        {task.createdAt.toLocaleTimeString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      {task.status === 'completed' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyResult(task.output)}
                            className="text-blue-200 hover:bg-white/10"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-blue-200 hover:bg-white/10"
                          >
                            <Download className="w-3 h-3" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  
                  {task.status === 'processing' ? (
                    <div className="flex items-center space-x-2 text-blue-200">
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      <span>AI正在处理中...</span>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div>
                        <h5 className="text-white text-sm font-medium mb-1">输入内容：</h5>
                        <p className="text-blue-200 text-sm bg-white/5 p-2 rounded border border-white/10 line-clamp-2">
                          {task.input}
                        </p>
                      </div>
                      
                      {task.output && (
                        <div>
                          <h5 className="text-white text-sm font-medium mb-1">AI处理结果：</h5>
                          <div className="text-blue-200 text-sm bg-white/5 p-3 rounded border border-white/10 whitespace-pre-wrap">
                            {task.output}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
            
            {tasks.length === 0 && (
              <div className="text-center py-8">
                <Sparkles className="w-12 h-12 text-purple-300 mx-auto mb-4 opacity-50" />
                <p className="text-blue-200 mb-2">还没有AI处理任务</p>
                <p className="text-blue-300 text-sm">输入内容并选择AI功能开始体验</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
