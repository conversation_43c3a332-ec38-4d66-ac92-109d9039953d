import React, { useState, useRef } from 'react';
import {
  Sparkles,
  FileText,
  Languages,
  Mail,
  BookOpen,
  Lightbulb,
  Mic,
  Upload,
  Download,
  Copy,
  RefreshCw,
  Image,
  Video,
  Music,
  Code,
  PenTool,
  Zap,
  Brain,
  Target,
  Palette,
  MessageSquare,
  FileImage,
  Settings,
  History,
  Star,
  Trash2,
  MoreVertical,
  Play,
  Pause,
  Volume2
} from 'lucide-react';
import { creativeAIService, voiceService } from '@/services/apiService';
import type { AITask } from '@/services/mockDataService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// 扩展的AI任务类型
interface EnhancedAITask extends AITask {
  category: 'text' | 'creative' | 'analysis' | 'media' | 'code';
  quality: 'draft' | 'standard' | 'premium';
  tokens?: number;
  processingTime?: number;
  isStarred?: boolean;
  tags?: string[];
}

// AI工具分类
const AI_CATEGORIES = [
  {
    id: 'text',
    name: '文本处理',
    icon: FileText,
    color: 'blue',
    tools: [
      { id: 'summary', name: '智能摘要', icon: FileText, description: '提取核心要点' },
      { id: 'translate', name: '多语言翻译', icon: Languages, description: '支持50+语言' },
      { id: 'format', name: '格式转换', icon: Mail, description: '邮件、报告等格式' },
      { id: 'proofread', name: '语法校对', icon: PenTool, description: '检查语法错误' },
      { id: 'rewrite', name: '内容重写', icon: RefreshCw, description: '改写优化文本' },
      { id: 'expand', name: '内容扩展', icon: Zap, description: '丰富内容细节' }
    ]
  },
  {
    id: 'creative',
    name: '创意写作',
    icon: Lightbulb,
    color: 'purple',
    tools: [
      { id: 'story', name: '故事创作', icon: BookOpen, description: '生成创意故事' },
      { id: 'poem', name: '诗歌创作', icon: PenTool, description: '创作诗歌作品' },
      { id: 'slogan', name: '广告文案', icon: Target, description: '营销文案生成' },
      { id: 'naming', name: '创意命名', icon: Lightbulb, description: '产品品牌命名' },
      { id: 'dialogue', name: '对话生成', icon: MessageSquare, description: '角色对话创作' },
      { id: 'brainstorm', name: '头脑风暴', icon: Brain, description: '创意点子生成' }
    ]
  },
  {
    id: 'analysis',
    name: '内容分析',
    icon: BookOpen,
    color: 'green',
    tools: [
      { id: 'sentiment', name: '情感分析', icon: Brain, description: '分析文本情感' },
      { id: 'keywords', name: '关键词提取', icon: Target, description: '提取核心关键词' },
      { id: 'topics', name: '主题分析', icon: BookOpen, description: '识别主要话题' },
      { id: 'readability', name: '可读性分析', icon: FileText, description: '评估阅读难度' },
      { id: 'structure', name: '结构分析', icon: Settings, description: '分析文本结构' },
      { id: 'compare', name: '内容对比', icon: RefreshCw, description: '对比多个文本' }
    ]
  },
  {
    id: 'media',
    name: '多媒体',
    icon: Image,
    color: 'pink',
    tools: [
      { id: 'image_desc', name: '图片描述', icon: FileImage, description: '生成图片描述' },
      { id: 'image_gen', name: '图片生成', icon: Image, description: 'AI图片生成' },
      { id: 'video_script', name: '视频脚本', icon: Video, description: '视频内容脚本' },
      { id: 'audio_gen', name: '音频生成', icon: Music, description: 'AI音频合成' },
      { id: 'subtitle', name: '字幕生成', icon: FileText, description: '视频字幕生成' },
      { id: 'voice_clone', name: '语音克隆', icon: Volume2, description: '个性化语音' }
    ]
  },
  {
    id: 'code',
    name: '代码助手',
    icon: Code,
    color: 'orange',
    tools: [
      { id: 'code_gen', name: '代码生成', icon: Code, description: '生成代码片段' },
      { id: 'code_review', name: '代码审查', icon: Settings, description: '代码质量检查' },
      { id: 'code_explain', name: '代码解释', icon: BookOpen, description: '解释代码逻辑' },
      { id: 'code_optimize', name: '代码优化', icon: Zap, description: '性能优化建议' },
      { id: 'bug_fix', name: '错误修复', icon: RefreshCw, description: '自动修复bug' },
      { id: 'test_gen', name: '测试生成', icon: Target, description: '生成测试用例' }
    ]
  }
];

// 质量等级选项
const QUALITY_OPTIONS = [
  { value: 'draft', label: '草稿模式', description: '快速生成，适合初稿', tokens: 100 },
  { value: 'standard', label: '标准模式', description: '平衡质量与速度', tokens: 300 },
  { value: 'premium', label: '精品模式', description: '最高质量输出', tokens: 500 }
];

// 语言选项（扩展）
const LANGUAGE_OPTIONS = [
  { value: 'zh', label: '🇨🇳 中文', flag: '🇨🇳' },
  { value: 'en', label: '🇺🇸 英语', flag: '🇺🇸' },
  { value: 'ja', label: '🇯🇵 日语', flag: '🇯🇵' },
  { value: 'ko', label: '🇰🇷 韩语', flag: '🇰🇷' },
  { value: 'fr', label: '🇫🇷 法语', flag: '🇫🇷' },
  { value: 'de', label: '🇩🇪 德语', flag: '🇩🇪' },
  { value: 'es', label: '🇪🇸 西班牙语', flag: '🇪🇸' },
  { value: 'it', label: '🇮🇹 意大利语', flag: '🇮🇹' },
  { value: 'pt', label: '🇵🇹 葡萄牙语', flag: '🇵🇹' },
  { value: 'ru', label: '🇷🇺 俄语', flag: '🇷🇺' },
  { value: 'ar', label: '🇸🇦 阿拉伯语', flag: '🇸🇦' },
  { value: 'hi', label: '🇮🇳 印地语', flag: '🇮🇳' }
];

interface EnhancedCreativeAIWidgetProps {
  expanded?: boolean;
  className?: string;
}

export default function EnhancedCreativeAIWidget({ expanded = false, className = '' }: EnhancedCreativeAIWidgetProps) {
  const [inputText, setInputText] = useState('');
  const [tasks, setTasks] = useState<EnhancedAITask[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('text');
  const [selectedTool, setSelectedTool] = useState('summary');
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [selectedQuality, setSelectedQuality] = useState<'draft' | 'standard' | 'premium'>('standard');
  const [isRecording, setIsRecording] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showStarredOnly, setShowStarredOnly] = useState(false);
  const [creativityLevel, setCreativityLevel] = useState([0.7]);
  const [maxLength, setMaxLength] = useState([500]);
  const [enableAdvanced, setEnableAdvanced] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取当前分类的工具
  const getCurrentCategoryTools = () => {
    return AI_CATEGORIES.find(cat => cat.id === selectedCategory)?.tools || [];
  };

  // 获取当前工具信息
  const getCurrentTool = () => {
    const tools = getCurrentCategoryTools();
    return tools.find(tool => tool.id === selectedTool) || tools[0];
  };

  // 执行AI任务
  const executeAITask = async (toolId?: string) => {
    if (!inputText.trim()) return;

    const tool = toolId ? getCurrentCategoryTools().find(t => t.id === toolId) : getCurrentTool();
    if (!tool) return;

    const newTask: EnhancedAITask = {
      id: Date.now().toString(),
      type: tool.id as any,
      input: inputText,
      output: '',
      status: 'processing',
      createdAt: new Date(),
      model: 'gpt-4',
      category: selectedCategory as any,
      quality: selectedQuality,
      tokens: QUALITY_OPTIONS.find(q => q.value === selectedQuality)?.tokens || 300,
      isStarred: false,
      tags: [tool.name, selectedCategory]
    };

    setTasks(prev => [newTask, ...prev]);
    setIsProcessing(true);

    try {
      const startTime = Date.now();
      
      // 构建处理参数
      const params = {
        language: selectedLanguage,
        quality: selectedQuality,
        creativity: creativityLevel[0],
        maxLength: maxLength[0],
        advanced: enableAdvanced
      };

      // 使用API服务处理内容
      const result = await creativeAIService.processContent(inputText, tool.id as any, params);
      const processingTime = Date.now() - startTime;

      setTasks(prev => prev.map(task =>
        task.id === newTask.id
          ? { ...task, output: result, status: 'completed', processingTime }
          : task
      ));
    } catch (error) {
      console.error('AI processing failed:', error);

      setTasks(prev => prev.map(task =>
        task.id === newTask.id
          ? { ...task, output: 'AI处理失败，请重试', status: 'error' }
          : task
      ));
    } finally {
      setIsProcessing(false);
    }
  };

  // 语音输入
  const handleVoiceInput = async () => {
    if (isRecording) {
      setIsRecording(false);
      return;
    }

    setIsRecording(true);
    try {
      // 模拟录音过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      const mockAudioBlob = new Blob([], { type: 'audio/wav' });
      const transcription = await voiceService.recognizeSpeech(mockAudioBlob);
      setInputText(transcription);
    } catch (error) {
      console.error('Voice input failed:', error);
      setInputText('语音输入失败，请重试');
    } finally {
      setIsRecording(false);
    }
  };

  // 文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInputText(content);
    };
    reader.readAsText(file);
  };

  // 复制结果
  const handleCopyResult = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: 添加toast提示
  };

  // 收藏任务
  const toggleTaskStar = (taskId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId ? { ...task, isStarred: !task.isStarred } : task
    ));
  };

  // 删除任务
  const deleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  // 导出任务结果
  const exportTask = (task: EnhancedAITask, format: 'txt' | 'md' | 'json') => {
    let content = '';
    const timestamp = task.createdAt.toLocaleString('zh-CN');
    
    switch (format) {
      case 'txt':
        content = `AI处理结果\n创建时间: ${timestamp}\n工具: ${task.type}\n质量: ${task.quality}\n\n输入:\n${task.input}\n\n输出:\n${task.output}`;
        break;
      case 'md':
        content = `# AI处理结果\n\n**创建时间**: ${timestamp}\n**工具**: ${task.type}\n**质量**: ${task.quality}\n\n## 输入\n${task.input}\n\n## 输出\n${task.output}`;
        break;
      case 'json':
        content = JSON.stringify(task, null, 2);
        break;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-task-${task.id}.${format}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.input.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.output.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.type.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStarred = !showStarredOnly || task.isStarred;
    
    return matchesSearch && matchesStarred;
  });

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full h-[700px]' : 'h-[500px]'
    } ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            <span>增强创意AI工具</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-blue-200 border-blue-400/30">
              {tasks.length} 个任务
            </Badge>
            <Badge variant="outline" className="text-purple-200 border-purple-400/30">
              {selectedQuality} 模式
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        <Tabs defaultValue="create" className="flex flex-col h-full">
          <TabsList className="grid w-full grid-cols-3 bg-white/5 border-b border-white/10 mx-6">
            <TabsTrigger value="create" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Sparkles className="w-4 h-4 mr-2" />
              创建任务
            </TabsTrigger>
            <TabsTrigger value="history" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <History className="w-4 h-4 mr-2" />
              任务历史
            </TabsTrigger>
            <TabsTrigger value="settings" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              高级设置
            </TabsTrigger>
          </TabsList>

          {/* 创建任务标签页 */}
          <TabsContent value="create" className="flex flex-col flex-1 mt-0">
            <div className="p-6 space-y-4">
              {/* 输入区域 */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Textarea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="输入要处理的文本内容，或点击语音输入、文件上传..."
                    className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[100px]"
                    rows={4}
                  />
                  <div className="flex flex-col space-y-2">
                    <Button
                      variant="ghost"
                      onClick={handleVoiceInput}
                      className={`text-blue-200 hover:bg-white/10 ${isRecording ? 'bg-red-500/20 text-red-300' : ''}`}
                    >
                      {isRecording ? <Volume2 className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-200 hover:bg-white/10"
                    >
                      <Upload className="w-4 h-4" />
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".txt,.md,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </div>
                </div>
                
                {/* 工具分类选择 */}
                <div className="grid grid-cols-5 gap-2">
                  {AI_CATEGORIES.map((category) => {
                    const IconComponent = category.icon;
                    return (
                      <Button
                        key={category.id}
                        variant={selectedCategory === category.id ? "default" : "ghost"}
                        onClick={() => {
                          setSelectedCategory(category.id);
                          setSelectedTool(category.tools[0].id);
                        }}
                        className={`flex flex-col items-center p-3 h-auto ${
                          selectedCategory === category.id 
                            ? `bg-${category.color}-500/20 text-${category.color}-200 border border-${category.color}-400/30`
                            : 'text-blue-200 hover:bg-white/10'
                        }`}
                      >
                        <IconComponent className="w-5 h-5 mb-1" />
                        <span className="text-xs">{category.name}</span>
                      </Button>
                    );
                  })}
                </div>

                {/* 具体工具选择 */}
                <div className="grid grid-cols-3 gap-2">
                  {getCurrentCategoryTools().map((tool) => {
                    const IconComponent = tool.icon;
                    return (
                      <Button
                        key={tool.id}
                        variant={selectedTool === tool.id ? "default" : "ghost"}
                        onClick={() => setSelectedTool(tool.id)}
                        className={`flex items-center justify-start p-3 h-auto ${
                          selectedTool === tool.id 
                            ? 'bg-white/20 text-white border border-white/30'
                            : 'text-blue-200 hover:bg-white/10'
                        }`}
                      >
                        <IconComponent className="w-4 h-4 mr-2" />
                        <div className="text-left">
                          <div className="text-sm font-medium">{tool.name}</div>
                          <div className="text-xs opacity-70">{tool.description}</div>
                        </div>
                      </Button>
                    );
                  })}
                </div>

                {/* 快速设置 */}
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Label className="text-white text-sm">质量:</Label>
                    <Select value={selectedQuality} onValueChange={(value: any) => setSelectedQuality(value)}>
                      <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {QUALITY_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex flex-col">
                              <span>{option.label}</span>
                              <span className="text-xs text-gray-500">{option.tokens} tokens</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Label className="text-white text-sm">语言:</Label>
                    <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                      <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {LANGUAGE_OPTIONS.map((lang) => (
                          <SelectItem key={lang.value} value={lang.value}>
                            {lang.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={() => executeAITask()}
                    disabled={!inputText.trim() || isProcessing}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  >
                    {isProcessing ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        处理中...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        开始处理
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* 任务历史标签页 */}
          <TabsContent value="history" className="flex flex-col flex-1 mt-0">
            <div className="p-6 space-y-4">
              {/* 搜索和筛选 */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索任务..."
                    className="bg-white/10 border-white/20 text-white placeholder:text-blue-200"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showStarredOnly}
                    onCheckedChange={setShowStarredOnly}
                  />
                  <Label className="text-white text-sm">仅显示收藏</Label>
                </div>
              </div>

              {/* 任务列表 */}
              <ScrollArea className="flex-1">
                <div className="space-y-3">
                  {filteredTasks.length === 0 ? (
                    <div className="text-center py-8">
                      <History className="w-12 h-12 text-purple-300 mx-auto mb-4 opacity-50" />
                      <p className="text-blue-200 mb-2">暂无任务历史</p>
                      <p className="text-blue-300 text-sm">创建第一个AI任务开始体验</p>
                    </div>
                  ) : (
                    filteredTasks.map((task) => (
                      <Card key={task.id} className="bg-white/5 border-white/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <Badge variant="secondary" className="capitalize">
                                {task.type}
                              </Badge>
                              <Badge variant="outline" className={`text-xs ${
                                task.quality === 'premium' ? 'text-yellow-200 border-yellow-400/30' :
                                task.quality === 'standard' ? 'text-blue-200 border-blue-400/30' :
                                'text-gray-200 border-gray-400/30'
                              }`}>
                                {task.quality}
                              </Badge>
                              <span className="text-xs text-blue-300">
                                {task.createdAt.toLocaleTimeString()}
                              </span>
                              {task.processingTime && (
                                <span className="text-xs text-green-300">
                                  {task.processingTime}ms
                                </span>
                              )}
                            </div>
                            
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleTaskStar(task.id)}
                                className={`${task.isStarred ? 'text-yellow-400' : 'text-gray-400'} hover:text-yellow-300`}
                              >
                                <Star className={`w-3 h-3 ${task.isStarred ? 'fill-current' : ''}`} />
                              </Button>
                              
                              {task.status === 'completed' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleCopyResult(task.output)}
                                    className="text-blue-200 hover:bg-white/10"
                                  >
                                    <Copy className="w-3 h-3" />
                                  </Button>
                                  
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                                        <MoreVertical className="w-3 h-3" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="bg-gray-800 border-gray-700">
                                      <DropdownMenuItem onClick={() => exportTask(task, 'txt')} className="text-white hover:bg-gray-700">
                                        <Download className="w-4 h-4 mr-2" />
                                        导出为TXT
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => exportTask(task, 'md')} className="text-white hover:bg-gray-700">
                                        <Download className="w-4 h-4 mr-2" />
                                        导出为Markdown
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => exportTask(task, 'json')} className="text-white hover:bg-gray-700">
                                        <Download className="w-4 h-4 mr-2" />
                                        导出为JSON
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => deleteTask(task.id)} className="text-red-400 hover:bg-red-500/20">
                                        <Trash2 className="w-4 h-4 mr-2" />
                                        删除任务
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </>
                              )}
                            </div>
                          </div>
                          
                          {task.status === 'processing' ? (
                            <div className="flex items-center space-x-2 text-blue-200">
                              <RefreshCw className="w-4 h-4 animate-spin" />
                              <span>AI正在处理中...</span>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              <div>
                                <h5 className="text-white text-sm font-medium mb-1">输入内容：</h5>
                                <p className="text-blue-200 text-sm bg-white/5 p-2 rounded border border-white/10 line-clamp-2">
                                  {task.input}
                                </p>
                              </div>
                              
                              {task.output && (
                                <div>
                                  <h5 className="text-white text-sm font-medium mb-1">AI处理结果：</h5>
                                  <div className="text-blue-200 text-sm bg-white/5 p-3 rounded border border-white/10 whitespace-pre-wrap max-h-40 overflow-y-auto">
                                    {task.output}
                                  </div>
                                </div>
                              )}

                              {/* 标签 */}
                              {task.tags && task.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {task.tags.map((tag, index) => (
                                    <Badge key={index} variant="outline" className="text-xs text-blue-200 border-blue-400/30">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* 高级设置标签页 */}
          <TabsContent value="settings" className="flex flex-col flex-1 mt-0">
            <div className="p-6 space-y-6">
              <div className="space-y-4">
                <h3 className="text-white text-lg font-semibold">AI处理参数</h3>
                
                {/* 创意度设置 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-white">创意度</Label>
                    <span className="text-blue-200 text-sm">{creativityLevel[0].toFixed(1)}</span>
                  </div>
                  <Slider
                    value={creativityLevel}
                    onValueChange={setCreativityLevel}
                    max={1}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-blue-300 text-xs">较低值产生更保守的结果，较高值产生更有创意的结果</p>
                </div>

                {/* 最大长度设置 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-white">最大输出长度</Label>
                    <span className="text-blue-200 text-sm">{maxLength[0]} 字符</span>
                  </div>
                  <Slider
                    value={maxLength}
                    onValueChange={setMaxLength}
                    max={2000}
                    min={100}
                    step={50}
                    className="w-full"
                  />
                  <p className="text-blue-300 text-xs">控制AI生成内容的最大长度</p>
                </div>

                {/* 高级功能开关 */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-white">启用高级功能</Label>
                    <p className="text-blue-300 text-xs">包括上下文理解、多轮对话等</p>
                  </div>
                  <Switch
                    checked={enableAdvanced}
                    onCheckedChange={setEnableAdvanced}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-white text-lg font-semibold">使用统计</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">{tasks.length}</div>
                    <div className="text-blue-200 text-sm">总任务数</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">{tasks.filter(t => t.status === 'completed').length}</div>
                    <div className="text-blue-200 text-sm">已完成</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">{tasks.filter(t => t.isStarred).length}</div>
                    <div className="text-blue-200 text-sm">已收藏</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">
                      {tasks.reduce((sum, task) => sum + (task.tokens || 0), 0)}
                    </div>
                    <div className="text-blue-200 text-sm">总Tokens</div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-white text-lg font-semibold">数据管理</h3>
                
                <div className="flex space-x-4">
                  <Button
                    variant="outline"
                    className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    导出所有任务
                  </Button>
                  <Button
                    variant="outline"
                    className="border-red-400/30 text-red-200 hover:bg-red-500/20"
                    onClick={() => {
                      if (confirm('确定要清空所有任务历史吗？此操作不可撤销。')) {
                        setTasks([]);
                      }
                    }}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    清空历史
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
