import React, { useState, useRef } from 'react';
import {
  Database,
  Download,
  Upload,
  FileText,
  MessageSquare,
  Calendar,
  Sparkles,
  Archive,
  Trash2,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info,
  Settings,
  Filter,
  Search,
  Clock,
  HardDrive,
  Cloud,
  Shield,
  Zap,
  Package,
  FileJson,
  FileSpreadsheet,
  FileImage
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// 数据类型定义
interface DataItem {
  id: string;
  type: 'conversation' | 'note' | 'schedule' | 'ai-task' | 'settings';
  title: string;
  size: number; // bytes
  createdAt: Date;
  updatedAt: Date;
  selected?: boolean;
}

// 导出格式选项
const EXPORT_FORMATS = [
  { value: 'json', label: 'JSON', description: '结构化数据格式', icon: FileJson },
  { value: 'csv', label: 'CSV', description: '表格数据格式', icon: FileSpreadsheet },
  { value: 'txt', label: 'TXT', description: '纯文本格式', icon: FileText },
  { value: 'md', label: 'Markdown', description: 'Markdown格式', icon: FileText },
  { value: 'zip', label: 'ZIP压缩包', description: '包含所有文件', icon: Archive }
];

// 数据类型选项
const DATA_TYPES = [
  { value: 'all', label: '全部数据', icon: Database, color: 'text-blue-400' },
  { value: 'conversation', label: 'AI对话', icon: MessageSquare, color: 'text-green-400' },
  { value: 'note', label: '语音笔记', icon: FileText, color: 'text-purple-400' },
  { value: 'schedule', label: '日程安排', icon: Calendar, color: 'text-orange-400' },
  { value: 'ai-task', label: 'AI任务', icon: Sparkles, color: 'text-pink-400' },
  { value: 'settings', label: '用户设置', icon: Settings, color: 'text-gray-400' }
];

interface DataManagementDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function DataManagementDialog({ open, onClose }: DataManagementDialogProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState('json');
  const [exportType, setExportType] = useState('all');
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 模拟数据项
  const [dataItems] = useState<DataItem[]>([
    {
      id: '1',
      type: 'conversation',
      title: 'VoiceHub功能咨询对话',
      size: 15420,
      createdAt: new Date(Date.now() - 86400000),
      updatedAt: new Date(Date.now() - 86400000 + 30000)
    },
    {
      id: '2',
      type: 'note',
      title: '项目会议记录',
      size: 8960,
      createdAt: new Date(Date.now() - 172800000),
      updatedAt: new Date(Date.now() - 172800000 + 3600000)
    },
    {
      id: '3',
      type: 'ai-task',
      title: '智能摘要任务',
      size: 5230,
      createdAt: new Date(Date.now() - 259200000),
      updatedAt: new Date(Date.now() - 259200000)
    },
    {
      id: '4',
      type: 'schedule',
      title: '产品发布会议',
      size: 3450,
      createdAt: new Date(Date.now() - 345600000),
      updatedAt: new Date(Date.now() - 345600000)
    },
    {
      id: '5',
      type: 'settings',
      title: '用户偏好设置',
      size: 1280,
      createdAt: new Date(Date.now() - 432000000),
      updatedAt: new Date(Date.now() - 86400000)
    }
  ]);

  // 过滤数据项
  const filteredItems = dataItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || item.type === filterType;
    return matchesSearch && matchesType;
  });

  // 计算统计信息
  const stats = {
    totalItems: dataItems.length,
    totalSize: dataItems.reduce((sum, item) => sum + item.size, 0),
    selectedItems: selectedItems.length,
    selectedSize: dataItems
      .filter(item => selectedItems.includes(item.id))
      .reduce((sum, item) => sum + item.size, 0)
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取数据类型信息
  const getDataTypeInfo = (type: string) => {
    return DATA_TYPES.find(t => t.value === type) || DATA_TYPES[0];
  };

  // 选择/取消选择项目
  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  // 导出数据
  const handleExport = async () => {
    if (selectedItems.length === 0) {
      alert('请选择要导出的数据项');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // 模拟导出过程
      for (let i = 0; i <= 100; i += 10) {
        setExportProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 获取选中的数据项
      const selectedData = dataItems.filter(item => selectedItems.includes(item.id));
      
      // 根据格式生成导出内容
      let exportContent = '';
      let fileName = '';
      let mimeType = '';

      switch (exportFormat) {
        case 'json':
          exportContent = JSON.stringify(selectedData, null, 2);
          fileName = `voicehub-export-${Date.now()}.json`;
          mimeType = 'application/json';
          break;
        case 'csv':
          const csvHeaders = 'ID,Type,Title,Size,Created,Updated\n';
          const csvRows = selectedData.map(item => 
            `${item.id},${item.type},${item.title},${item.size},${item.createdAt.toISOString()},${item.updatedAt.toISOString()}`
          ).join('\n');
          exportContent = csvHeaders + csvRows;
          fileName = `voicehub-export-${Date.now()}.csv`;
          mimeType = 'text/csv';
          break;
        case 'txt':
          exportContent = selectedData.map(item => 
            `${item.title}\nType: ${item.type}\nSize: ${formatFileSize(item.size)}\nCreated: ${item.createdAt.toLocaleString()}\nUpdated: ${item.updatedAt.toLocaleString()}\n\n`
          ).join('');
          fileName = `voicehub-export-${Date.now()}.txt`;
          mimeType = 'text/plain';
          break;
        case 'md':
          exportContent = `# VoiceHub 数据导出\n\n导出时间: ${new Date().toLocaleString()}\n\n`;
          exportContent += selectedData.map(item => 
            `## ${item.title}\n\n- **类型**: ${item.type}\n- **大小**: ${formatFileSize(item.size)}\n- **创建时间**: ${item.createdAt.toLocaleString()}\n- **更新时间**: ${item.updatedAt.toLocaleString()}\n\n`
          ).join('');
          fileName = `voicehub-export-${Date.now()}.md`;
          mimeType = 'text/markdown';
          break;
        default:
          throw new Error('不支持的导出格式');
      }

      // 下载文件
      const blob = new Blob([exportContent], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);

      // 显示成功消息
      alert(`成功导出 ${selectedItems.length} 个数据项`);
      
    } catch (error) {
      console.error('Export failed:', error);
      alert('导出失败，请重试');
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // 导入数据
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportProgress(0);

    try {
      // 模拟导入过程
      for (let i = 0; i <= 100; i += 10) {
        setImportProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          
          // 根据文件类型解析内容
          if (file.name.endsWith('.json')) {
            const importedData = JSON.parse(content);
            console.log('Imported JSON data:', importedData);
          } else if (file.name.endsWith('.csv')) {
            // 解析CSV
            const lines = content.split('\n');
            const headers = lines[0].split(',');
            const data = lines.slice(1).map(line => {
              const values = line.split(',');
              return headers.reduce((obj, header, index) => {
                obj[header] = values[index];
                return obj;
              }, {} as any);
            });
            console.log('Imported CSV data:', data);
          }
          
          alert('数据导入成功！');
        } catch (error) {
          console.error('Failed to parse imported file:', error);
          alert('文件格式错误，导入失败');
        }
      };
      
      reader.readAsText(file);
      
    } catch (error) {
      console.error('Import failed:', error);
      alert('导入失败，请重试');
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 清理数据
  const handleCleanup = async () => {
    if (!confirm('确定要清理选中的数据吗？此操作不可撤销。')) {
      return;
    }

    try {
      // 模拟清理过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用API删除数据
      console.log('Cleaning up data items:', selectedItems);
      
      alert(`成功清理 ${selectedItems.length} 个数据项`);
      setSelectedItems([]);
      
    } catch (error) {
      console.error('Cleanup failed:', error);
      alert('清理失败，请重试');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-blue-400" />
            <span>数据管理</span>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="export" className="flex flex-col h-full">
          <TabsList className="grid w-full grid-cols-4 bg-white/5">
            <TabsTrigger value="export" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Download className="w-4 h-4 mr-2" />
              数据导出
            </TabsTrigger>
            <TabsTrigger value="import" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Upload className="w-4 h-4 mr-2" />
              数据导入
            </TabsTrigger>
            <TabsTrigger value="backup" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Archive className="w-4 h-4 mr-2" />
              备份管理
            </TabsTrigger>
            <TabsTrigger value="cleanup" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <Trash2 className="w-4 h-4 mr-2" />
              数据清理
            </TabsTrigger>
          </TabsList>

          {/* 数据导出标签页 */}
          <TabsContent value="export" className="flex flex-col flex-1 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
              {/* 左侧：数据选择 */}
              <div className="lg:col-span-2 space-y-4">
                <Card className="bg-white/5 border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center justify-between">
                      <span>选择数据</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-blue-200 border-blue-400/30">
                          {stats.selectedItems}/{stats.totalItems} 项
                        </Badge>
                        <Badge variant="outline" className="text-green-200 border-green-400/30">
                          {formatFileSize(stats.selectedSize)}
                        </Badge>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* 搜索和筛选 */}
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                        <Input
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          placeholder="搜索数据项..."
                          className="pl-10 bg-white/10 border-white/20 text-white"
                        />
                      </div>
                      <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {DATA_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 全选按钮 */}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                        onCheckedChange={toggleSelectAll}
                      />
                      <Label className="text-white">全选 ({filteredItems.length} 项)</Label>
                    </div>

                    {/* 数据项列表 */}
                    <ScrollArea className="h-64">
                      <div className="space-y-2">
                        {filteredItems.map((item) => {
                          const typeInfo = getDataTypeInfo(item.type);
                          const IconComponent = typeInfo.icon;
                          
                          return (
                            <div
                              key={item.id}
                              className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                            >
                              <Checkbox
                                checked={selectedItems.includes(item.id)}
                                onCheckedChange={() => toggleItemSelection(item.id)}
                              />
                              <div className={`w-8 h-8 rounded-lg flex items-center justify-center bg-white/10 ${typeInfo.color}`}>
                                <IconComponent className="w-4 h-4" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-white font-medium truncate">{item.title}</h4>
                                <div className="flex items-center space-x-4 text-xs text-blue-300">
                                  <span>{typeInfo.label}</span>
                                  <span>{formatFileSize(item.size)}</span>
                                  <span className="flex items-center">
                                    <Clock className="w-3 h-3 mr-1" />
                                    {item.updatedAt.toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>

              {/* 右侧：导出设置 */}
              <div className="space-y-4">
                <Card className="bg-white/5 border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white">导出设置</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* 导出格式 */}
                    <div>
                      <Label className="text-white mb-2 block">导出格式</Label>
                      <div className="space-y-2">
                        {EXPORT_FORMATS.map((format) => {
                          const IconComponent = format.icon;
                          return (
                            <Button
                              key={format.value}
                              variant={exportFormat === format.value ? "default" : "ghost"}
                              onClick={() => setExportFormat(format.value)}
                              className={`w-full justify-start ${
                                exportFormat === format.value 
                                  ? 'bg-white/20 text-white border border-white/30'
                                  : 'text-blue-200 hover:bg-white/10'
                              }`}
                            >
                              <IconComponent className="w-4 h-4 mr-2" />
                              <div className="text-left">
                                <div className="font-medium">{format.label}</div>
                                <div className="text-xs opacity-70">{format.description}</div>
                              </div>
                            </Button>
                          );
                        })}
                      </div>
                    </div>

                    {/* 导出进度 */}
                    {isExporting && (
                      <div>
                        <Label className="text-white mb-2 block">导出进度</Label>
                        <Progress value={exportProgress} className="w-full" />
                        <p className="text-blue-300 text-sm mt-1">{exportProgress}% 完成</p>
                      </div>
                    )}

                    {/* 导出按钮 */}
                    <Button
                      onClick={handleExport}
                      disabled={selectedItems.length === 0 || isExporting}
                      className="w-full bg-blue-500 hover:bg-blue-600"
                    >
                      {isExporting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          导出中...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          导出数据
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* 统计信息 */}
                <Card className="bg-white/5 border-white/10">
                  <CardHeader>
                    <CardTitle className="text-white">统计信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-blue-200">总数据项</span>
                      <span className="text-white">{stats.totalItems}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">总大小</span>
                      <span className="text-white">{formatFileSize(stats.totalSize)}</span>
                    </div>
                    <Separator className="bg-white/10" />
                    <div className="flex justify-between">
                      <span className="text-blue-200">已选择</span>
                      <span className="text-white">{stats.selectedItems}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">选择大小</span>
                      <span className="text-white">{formatFileSize(stats.selectedSize)}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* 数据导入标签页 */}
          <TabsContent value="import" className="flex flex-col flex-1 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Upload className="w-5 h-5 mr-2 text-green-400" />
                    导入数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center">
                    <Upload className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-white font-medium mb-2">选择要导入的文件</h3>
                    <p className="text-blue-300 text-sm mb-4">支持 JSON、CSV、TXT 格式</p>
                    
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isImporting}
                      className="bg-green-500 hover:bg-green-600"
                    >
                      {isImporting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          导入中...
                        </>
                      ) : (
                        <>
                          <Upload className="w-4 h-4 mr-2" />
                          选择文件
                        </>
                      )}
                    </Button>
                    
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".json,.csv,.txt,.md"
                      onChange={handleImport}
                      className="hidden"
                    />
                  </div>

                  {/* 导入进度 */}
                  {isImporting && (
                    <div>
                      <Label className="text-white mb-2 block">导入进度</Label>
                      <Progress value={importProgress} className="w-full" />
                      <p className="text-blue-300 text-sm mt-1">{importProgress}% 完成</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Info className="w-5 h-5 mr-2 text-blue-400" />
                    导入说明
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-white font-medium">支持的文件格式：</h4>
                      <ul className="text-blue-300 text-sm space-y-1 mt-2">
                        <li>• JSON - 结构化数据格式</li>
                        <li>• CSV - 表格数据格式</li>
                        <li>• TXT - 纯文本格式</li>
                        <li>• Markdown - 标记语言格式</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="text-white font-medium">注意事项：</h4>
                      <ul className="text-blue-300 text-sm space-y-1 mt-2">
                        <li>• 文件大小限制：50MB</li>
                        <li>• 重复数据将被自动跳过</li>
                        <li>• 导入前会进行数据验证</li>
                        <li>• 支持增量导入和覆盖导入</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 备份管理标签页 */}
          <TabsContent value="backup" className="flex flex-col flex-1 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Archive className="w-5 h-5 mr-2 text-purple-400" />
                    自动备份
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                    <div>
                      <h4 className="text-white font-medium">每日自动备份</h4>
                      <p className="text-blue-300 text-sm">每天凌晨2点自动备份所有数据</p>
                    </div>
                    <Badge variant="outline" className="text-green-200 border-green-400/30">
                      已启用
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-blue-200">上次备份</span>
                      <span className="text-white">2024-08-15 02:00</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">备份大小</span>
                      <span className="text-white">2.3 MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">保留期限</span>
                      <span className="text-white">30天</span>
                    </div>
                  </div>

                  <Button className="w-full bg-purple-500 hover:bg-purple-600">
                    <Archive className="w-4 h-4 mr-2" />
                    立即备份
                  </Button>
                </CardContent>
              </Card>

              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Cloud className="w-5 h-5 mr-2 text-blue-400" />
                    云端备份
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center p-6">
                    <Cloud className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-white font-medium mb-2">云端同步</h3>
                    <p className="text-blue-300 text-sm mb-4">将数据同步到云端，确保数据安全</p>

                    <Button variant="outline" className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20">
                      <Settings className="w-4 h-4 mr-2" />
                      配置云端备份
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 备份历史 */}
            <Card className="bg-white/5 border-white/10 mt-6">
              <CardHeader>
                <CardTitle className="text-white">备份历史</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-2">
                    {[
                      { date: '2024-08-15 02:00', size: '2.3 MB', type: '自动备份', status: '成功' },
                      { date: '2024-08-14 02:00', size: '2.1 MB', type: '自动备份', status: '成功' },
                      { date: '2024-08-13 15:30', size: '1.8 MB', type: '手动备份', status: '成功' },
                      { date: '2024-08-13 02:00', size: '2.0 MB', type: '自动备份', status: '成功' },
                      { date: '2024-08-12 02:00', size: '1.9 MB', type: '自动备份', status: '失败' }
                    ].map((backup, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full ${
                            backup.status === '成功' ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <div>
                            <div className="text-white font-medium">{backup.date}</div>
                            <div className="text-blue-300 text-sm">{backup.type} • {backup.size}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={
                            backup.status === '成功'
                              ? 'text-green-200 border-green-400/30'
                              : 'text-red-200 border-red-400/30'
                          }>
                            {backup.status}
                          </Badge>
                          {backup.status === '成功' && (
                            <Button variant="ghost" size="sm" className="text-blue-200 hover:text-white">
                              <Download className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据清理标签页 */}
          <TabsContent value="cleanup" className="flex flex-col flex-1 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Trash2 className="w-5 h-5 mr-2 text-red-400" />
                    数据清理
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">临时文件</h4>
                        <p className="text-blue-300 text-sm">清理缓存和临时文件</p>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">156 MB</div>
                        <Button variant="ghost" size="sm" className="text-red-200 hover:text-red-100">
                          <Trash2 className="w-3 h-3 mr-1" />
                          清理
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">过期对话</h4>
                        <p className="text-blue-300 text-sm">30天前的对话记录</p>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">23 项</div>
                        <Button variant="ghost" size="sm" className="text-red-200 hover:text-red-100">
                          <Trash2 className="w-3 h-3 mr-1" />
                          清理
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">失败的AI任务</h4>
                        <p className="text-blue-300 text-sm">处理失败的任务记录</p>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">8 项</div>
                        <Button variant="ghost" size="sm" className="text-red-200 hover:text-red-100">
                          <Trash2 className="w-3 h-3 mr-1" />
                          清理
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={handleCleanup}
                    disabled={selectedItems.length === 0}
                    className="w-full bg-red-500 hover:bg-red-600"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    清理选中数据
                  </Button>
                </CardContent>
              </Card>

              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <HardDrive className="w-5 h-5 mr-2 text-yellow-400" />
                    存储空间
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-blue-200">已使用</span>
                      <span className="text-white">2.8 GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-200">可用空间</span>
                      <span className="text-white">7.2 GB</span>
                    </div>
                    <Progress value={28} className="w-full" />
                    <p className="text-blue-300 text-xs">使用了 28% 的存储空间</p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-white font-medium">存储分布</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-200">对话记录</span>
                        <span className="text-white">1.2 GB</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-200">语音文件</span>
                        <span className="text-white">800 MB</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-200">AI任务</span>
                        <span className="text-white">600 MB</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-blue-200">其他</span>
                        <span className="text-white">200 MB</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
