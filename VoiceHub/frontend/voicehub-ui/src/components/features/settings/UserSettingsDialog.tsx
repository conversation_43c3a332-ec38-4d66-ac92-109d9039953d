import React, { useState, useEffect } from 'react';
import {
  Settings,
  User,
  Palette,
  Volume2,
  Globe,
  Shield,
  Bell,
  Keyboard,
  Download,
  Upload,
  Trash2,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Mic,
  MessageSquare,
  Sparkles,
  Calendar,
  FileText,
  Monitor,
  Moon,
  Sun,
  Zap,
  Database,
  Key,
  Lock,
  Mail,
  Phone,
  MapPin,
  Camera,
  Edit3
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// 用户设置类型定义
interface UserSettings {
  // 个人信息
  profile: {
    name: string;
    email: string;
    phone: string;
    avatar: string;
    bio: string;
    location: string;
  };
  
  // 界面设置
  appearance: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    fontSize: number;
    compactMode: boolean;
    animations: boolean;
    colorScheme: string;
  };
  
  // 语音设置
  voice: {
    inputDevice: string;
    outputDevice: string;
    inputVolume: number;
    outputVolume: number;
    noiseReduction: boolean;
    autoRecord: boolean;
    voiceActivation: boolean;
    activationThreshold: number;
  };
  
  // AI设置
  ai: {
    defaultModel: string;
    creativity: number;
    responseLength: 'short' | 'medium' | 'long';
    autoSave: boolean;
    contextMemory: boolean;
    personalizedResponses: boolean;
  };
  
  // 通知设置
  notifications: {
    desktop: boolean;
    sound: boolean;
    email: boolean;
    taskReminders: boolean;
    scheduleAlerts: boolean;
    aiUpdates: boolean;
    soundVolume: number;
  };
  
  // 隐私设置
  privacy: {
    dataCollection: boolean;
    analytics: boolean;
    crashReports: boolean;
    voiceDataRetention: number; // days
    conversationHistory: boolean;
    shareUsageStats: boolean;
  };
  
  // 快捷键设置
  shortcuts: {
    globalSearch: string;
    quickRecord: string;
    newConversation: string;
    toggleSidebar: string;
    focusInput: string;
  };
}

interface UserSettingsDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (settings: UserSettings) => void;
  currentSettings?: UserSettings;
}

// 默认设置
const DEFAULT_SETTINGS: UserSettings = {
  profile: {
    name: '演示用户',
    email: '<EMAIL>',
    phone: '',
    avatar: '',
    bio: '',
    location: ''
  },
  appearance: {
    theme: 'dark',
    language: 'zh-CN',
    fontSize: 14,
    compactMode: false,
    animations: true,
    colorScheme: 'blue'
  },
  voice: {
    inputDevice: 'default',
    outputDevice: 'default',
    inputVolume: 80,
    outputVolume: 70,
    noiseReduction: true,
    autoRecord: false,
    voiceActivation: false,
    activationThreshold: 50
  },
  ai: {
    defaultModel: 'gpt-4',
    creativity: 0.7,
    responseLength: 'medium',
    autoSave: true,
    contextMemory: true,
    personalizedResponses: true
  },
  notifications: {
    desktop: true,
    sound: true,
    email: false,
    taskReminders: true,
    scheduleAlerts: true,
    aiUpdates: false,
    soundVolume: 60
  },
  privacy: {
    dataCollection: true,
    analytics: false,
    crashReports: true,
    voiceDataRetention: 30,
    conversationHistory: true,
    shareUsageStats: false
  },
  shortcuts: {
    globalSearch: 'Ctrl+K',
    quickRecord: 'Ctrl+R',
    newConversation: 'Ctrl+N',
    toggleSidebar: 'Ctrl+B',
    focusInput: 'Ctrl+/'
  }
};

// 主题选项
const THEME_OPTIONS = [
  { value: 'light', label: '浅色主题', icon: Sun },
  { value: 'dark', label: '深色主题', icon: Moon },
  { value: 'auto', label: '跟随系统', icon: Monitor }
];

// 语言选项
const LANGUAGE_OPTIONS = [
  { value: 'zh-CN', label: '简体中文', flag: '🇨🇳' },
  { value: 'en-US', label: 'English', flag: '🇺🇸' },
  { value: 'ja-JP', label: '日本語', flag: '🇯🇵' },
  { value: 'ko-KR', label: '한국어', flag: '🇰🇷' }
];

// AI模型选项
const AI_MODEL_OPTIONS = [
  { value: 'gpt-4', label: 'GPT-4', description: '最强大的模型' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '快速且经济' },
  { value: 'claude-3', label: 'Claude 3', description: '擅长分析推理' },
  { value: 'gemini-pro', label: 'Gemini Pro', description: '多模态支持' }
];

// 颜色方案选项
const COLOR_SCHEMES = [
  { value: 'blue', label: '蓝色', color: 'bg-blue-500' },
  { value: 'purple', label: '紫色', color: 'bg-purple-500' },
  { value: 'green', label: '绿色', color: 'bg-green-500' },
  { value: 'orange', label: '橙色', color: 'bg-orange-500' },
  { value: 'pink', label: '粉色', color: 'bg-pink-500' },
  { value: 'teal', label: '青色', color: 'bg-teal-500' }
];

export default function UserSettingsDialog({ 
  open, 
  onClose, 
  onSave, 
  currentSettings 
}: UserSettingsDialogProps) {
  const [settings, setSettings] = useState<UserSettings>(DEFAULT_SETTINGS);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // 初始化设置
  useEffect(() => {
    if (currentSettings) {
      setSettings(currentSettings);
    } else {
      // 从localStorage加载设置
      const savedSettings = localStorage.getItem('voicehub-user-settings');
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...DEFAULT_SETTINGS, ...parsed });
        } catch (error) {
          console.error('Failed to parse saved settings:', error);
        }
      }
    }
  }, [currentSettings, open]);

  // 更新设置
  const updateSettings = (section: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  // 保存设置
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // 保存到localStorage
      localStorage.setItem('voicehub-user-settings', JSON.stringify(settings));
      
      // 调用父组件的保存回调
      await onSave(settings);
      
      setHasChanges(false);
      
      // 显示成功提示
      // TODO: 添加toast提示
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      // TODO: 显示错误提示
    } finally {
      setIsSaving(false);
    }
  };

  // 重置设置
  const handleReset = () => {
    if (confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
      setSettings(DEFAULT_SETTINGS);
      setHasChanges(true);
    }
  };

  // 导出设置
  const handleExport = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'voicehub-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  // 导入设置
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);
        setSettings({ ...DEFAULT_SETTINGS, ...importedSettings });
        setHasChanges(true);
        // TODO: 显示成功提示
      } catch (error) {
        console.error('Failed to import settings:', error);
        // TODO: 显示错误提示
      }
    };
    reader.readAsText(file);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-400" />
            <span>用户设置</span>
            {hasChanges && (
              <Badge variant="outline" className="text-orange-200 border-orange-400/30">
                有未保存的更改
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="profile" className="flex flex-col h-full">
          <TabsList className="grid w-full grid-cols-6 bg-white/5">
            <TabsTrigger value="profile" className="text-xs">
              <User className="w-3 h-3 mr-1" />
              个人
            </TabsTrigger>
            <TabsTrigger value="appearance" className="text-xs">
              <Palette className="w-3 h-3 mr-1" />
              外观
            </TabsTrigger>
            <TabsTrigger value="voice" className="text-xs">
              <Volume2 className="w-3 h-3 mr-1" />
              语音
            </TabsTrigger>
            <TabsTrigger value="ai" className="text-xs">
              <Sparkles className="w-3 h-3 mr-1" />
              AI
            </TabsTrigger>
            <TabsTrigger value="notifications" className="text-xs">
              <Bell className="w-3 h-3 mr-1" />
              通知
            </TabsTrigger>
            <TabsTrigger value="privacy" className="text-xs">
              <Shield className="w-3 h-3 mr-1" />
              隐私
            </TabsTrigger>
          </TabsList>

          <ScrollArea className="flex-1 mt-4">
            {/* 个人信息标签页 */}
            <TabsContent value="profile" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <User className="w-5 h-5 mr-2 text-blue-400" />
                    个人信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      {settings.profile.avatar ? (
                        <img src={settings.profile.avatar} alt="Avatar" className="w-full h-full rounded-full object-cover" />
                      ) : (
                        <User className="w-8 h-8 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <Button variant="outline" className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20">
                        <Camera className="w-4 h-4 mr-2" />
                        更换头像
                      </Button>
                      <p className="text-blue-300 text-sm mt-1">支持 JPG、PNG 格式，建议尺寸 200x200</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-white">姓名</Label>
                      <Input
                        value={settings.profile.name}
                        onChange={(e) => updateSettings('profile', 'name', e.target.value)}
                        className="bg-white/10 border-white/20 text-white mt-1"
                      />
                    </div>
                    <div>
                      <Label className="text-white">邮箱</Label>
                      <Input
                        type="email"
                        value={settings.profile.email}
                        onChange={(e) => updateSettings('profile', 'email', e.target.value)}
                        className="bg-white/10 border-white/20 text-white mt-1"
                      />
                    </div>
                    <div>
                      <Label className="text-white">电话</Label>
                      <Input
                        value={settings.profile.phone}
                        onChange={(e) => updateSettings('profile', 'phone', e.target.value)}
                        className="bg-white/10 border-white/20 text-white mt-1"
                      />
                    </div>
                    <div>
                      <Label className="text-white">位置</Label>
                      <Input
                        value={settings.profile.location}
                        onChange={(e) => updateSettings('profile', 'location', e.target.value)}
                        className="bg-white/10 border-white/20 text-white mt-1"
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-white">个人简介</Label>
                    <Textarea
                      value={settings.profile.bio}
                      onChange={(e) => updateSettings('profile', 'bio', e.target.value)}
                      placeholder="介绍一下自己..."
                      className="bg-white/10 border-white/20 text-white mt-1"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 外观设置标签页 */}
            <TabsContent value="appearance" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Palette className="w-5 h-5 mr-2 text-purple-400" />
                    外观设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 主题选择 */}
                  <div>
                    <Label className="text-white mb-3 block">主题</Label>
                    <div className="grid grid-cols-3 gap-3">
                      {THEME_OPTIONS.map((theme) => {
                        const IconComponent = theme.icon;
                        return (
                          <Button
                            key={theme.value}
                            variant={settings.appearance.theme === theme.value ? "default" : "ghost"}
                            onClick={() => updateSettings('appearance', 'theme', theme.value)}
                            className={`flex flex-col items-center p-4 h-auto ${
                              settings.appearance.theme === theme.value 
                                ? 'bg-white/20 text-white border border-white/30'
                                : 'text-blue-200 hover:bg-white/10'
                            }`}
                          >
                            <IconComponent className="w-6 h-6 mb-2" />
                            <span className="text-sm">{theme.label}</span>
                          </Button>
                        );
                      })}
                    </div>
                  </div>

                  {/* 语言选择 */}
                  <div>
                    <Label className="text-white">语言</Label>
                    <Select 
                      value={settings.appearance.language} 
                      onValueChange={(value) => updateSettings('appearance', 'language', value)}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {LANGUAGE_OPTIONS.map((lang) => (
                          <SelectItem key={lang.value} value={lang.value}>
                            <span className="flex items-center">
                              <span className="mr-2">{lang.flag}</span>
                              {lang.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 颜色方案 */}
                  <div>
                    <Label className="text-white mb-3 block">颜色方案</Label>
                    <div className="grid grid-cols-6 gap-2">
                      {COLOR_SCHEMES.map((scheme) => (
                        <Button
                          key={scheme.value}
                          variant="ghost"
                          onClick={() => updateSettings('appearance', 'colorScheme', scheme.value)}
                          className={`flex flex-col items-center p-3 h-auto ${
                            settings.appearance.colorScheme === scheme.value 
                              ? 'ring-2 ring-white/50'
                              : ''
                          }`}
                        >
                          <div className={`w-8 h-8 rounded-full ${scheme.color} mb-1`}></div>
                          <span className="text-xs text-blue-200">{scheme.label}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* 字体大小 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label className="text-white">字体大小</Label>
                      <span className="text-blue-200 text-sm">{settings.appearance.fontSize}px</span>
                    </div>
                    <Slider
                      value={[settings.appearance.fontSize]}
                      onValueChange={(value) => updateSettings('appearance', 'fontSize', value[0])}
                      max={20}
                      min={12}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  {/* 其他选项 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">紧凑模式</Label>
                        <p className="text-blue-300 text-sm">减少界面间距，显示更多内容</p>
                      </div>
                      <Switch
                        checked={settings.appearance.compactMode}
                        onCheckedChange={(checked) => updateSettings('appearance', 'compactMode', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">动画效果</Label>
                        <p className="text-blue-300 text-sm">启用界面过渡动画</p>
                      </div>
                      <Switch
                        checked={settings.appearance.animations}
                        onCheckedChange={(checked) => updateSettings('appearance', 'animations', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 语音设置标签页 */}
            <TabsContent value="voice" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Volume2 className="w-5 h-5 mr-2 text-green-400" />
                    语音设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 音量设置 */}
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-white">输入音量</Label>
                        <span className="text-blue-200 text-sm">{settings.voice.inputVolume}%</span>
                      </div>
                      <Slider
                        value={[settings.voice.inputVolume]}
                        onValueChange={(value) => updateSettings('voice', 'inputVolume', value[0])}
                        max={100}
                        min={0}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-white">输出音量</Label>
                        <span className="text-blue-200 text-sm">{settings.voice.outputVolume}%</span>
                      </div>
                      <Slider
                        value={[settings.voice.outputVolume]}
                        onValueChange={(value) => updateSettings('voice', 'outputVolume', value[0])}
                        max={100}
                        min={0}
                        step={5}
                        className="w-full"
                      />
                    </div>
                  </div>

                  {/* 语音功能开关 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">噪音抑制</Label>
                        <p className="text-blue-300 text-sm">自动过滤背景噪音</p>
                      </div>
                      <Switch
                        checked={settings.voice.noiseReduction}
                        onCheckedChange={(checked) => updateSettings('voice', 'noiseReduction', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">语音激活</Label>
                        <p className="text-blue-300 text-sm">检测到语音时自动开始录制</p>
                      </div>
                      <Switch
                        checked={settings.voice.voiceActivation}
                        onCheckedChange={(checked) => updateSettings('voice', 'voiceActivation', checked)}
                      />
                    </div>

                    {settings.voice.voiceActivation && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-white">激活阈值</Label>
                          <span className="text-blue-200 text-sm">{settings.voice.activationThreshold}%</span>
                        </div>
                        <Slider
                          value={[settings.voice.activationThreshold]}
                          onValueChange={(value) => updateSettings('voice', 'activationThreshold', value[0])}
                          max={100}
                          min={10}
                          step={5}
                          className="w-full"
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* AI设置标签页 */}
            <TabsContent value="ai" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Sparkles className="w-5 h-5 mr-2 text-purple-400" />
                    AI设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 默认模型 */}
                  <div>
                    <Label className="text-white">默认AI模型</Label>
                    <Select
                      value={settings.ai.defaultModel}
                      onValueChange={(value) => updateSettings('ai', 'defaultModel', value)}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {AI_MODEL_OPTIONS.map((model) => (
                          <SelectItem key={model.value} value={model.value}>
                            <div className="flex flex-col">
                              <span>{model.label}</span>
                              <span className="text-xs text-gray-500">{model.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 创意度设置 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label className="text-white">创意度</Label>
                      <span className="text-blue-200 text-sm">{settings.ai.creativity.toFixed(1)}</span>
                    </div>
                    <Slider
                      value={[settings.ai.creativity]}
                      onValueChange={(value) => updateSettings('ai', 'creativity', value[0])}
                      max={1}
                      min={0}
                      step={0.1}
                      className="w-full"
                    />
                    <p className="text-blue-300 text-xs mt-1">较低值产生更保守的结果，较高值产生更有创意的结果</p>
                  </div>

                  {/* 响应长度 */}
                  <div>
                    <Label className="text-white">默认响应长度</Label>
                    <Select
                      value={settings.ai.responseLength}
                      onValueChange={(value: any) => updateSettings('ai', 'responseLength', value)}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="short">简短</SelectItem>
                        <SelectItem value="medium">中等</SelectItem>
                        <SelectItem value="long">详细</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* AI功能开关 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">自动保存</Label>
                        <p className="text-blue-300 text-sm">自动保存对话和AI任务</p>
                      </div>
                      <Switch
                        checked={settings.ai.autoSave}
                        onCheckedChange={(checked) => updateSettings('ai', 'autoSave', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">上下文记忆</Label>
                        <p className="text-blue-300 text-sm">AI记住之前的对话内容</p>
                      </div>
                      <Switch
                        checked={settings.ai.contextMemory}
                        onCheckedChange={(checked) => updateSettings('ai', 'contextMemory', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">个性化响应</Label>
                        <p className="text-blue-300 text-sm">根据使用习惯调整AI响应</p>
                      </div>
                      <Switch
                        checked={settings.ai.personalizedResponses}
                        onCheckedChange={(checked) => updateSettings('ai', 'personalizedResponses', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 通知设置标签页 */}
            <TabsContent value="notifications" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Bell className="w-5 h-5 mr-2 text-yellow-400" />
                    通知设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 通知类型 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">桌面通知</Label>
                        <p className="text-blue-300 text-sm">显示系统通知</p>
                      </div>
                      <Switch
                        checked={settings.notifications.desktop}
                        onCheckedChange={(checked) => updateSettings('notifications', 'desktop', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">声音提醒</Label>
                        <p className="text-blue-300 text-sm">播放提示音</p>
                      </div>
                      <Switch
                        checked={settings.notifications.sound}
                        onCheckedChange={(checked) => updateSettings('notifications', 'sound', checked)}
                      />
                    </div>

                    {settings.notifications.sound && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-white">提示音音量</Label>
                          <span className="text-blue-200 text-sm">{settings.notifications.soundVolume}%</span>
                        </div>
                        <Slider
                          value={[settings.notifications.soundVolume]}
                          onValueChange={(value) => updateSettings('notifications', 'soundVolume', value[0])}
                          max={100}
                          min={0}
                          step={5}
                          className="w-full"
                        />
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">邮件通知</Label>
                        <p className="text-blue-300 text-sm">发送邮件提醒</p>
                      </div>
                      <Switch
                        checked={settings.notifications.email}
                        onCheckedChange={(checked) => updateSettings('notifications', 'email', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">任务提醒</Label>
                        <p className="text-blue-300 text-sm">AI任务完成时提醒</p>
                      </div>
                      <Switch
                        checked={settings.notifications.taskReminders}
                        onCheckedChange={(checked) => updateSettings('notifications', 'taskReminders', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">日程提醒</Label>
                        <p className="text-blue-300 text-sm">日程事件提醒</p>
                      </div>
                      <Switch
                        checked={settings.notifications.scheduleAlerts}
                        onCheckedChange={(checked) => updateSettings('notifications', 'scheduleAlerts', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 隐私设置标签页 */}
            <TabsContent value="privacy" className="space-y-6 mt-0">
              <Card className="bg-white/5 border-white/10">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Shield className="w-5 h-5 mr-2 text-red-400" />
                    隐私设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 数据收集 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">数据收集</Label>
                        <p className="text-blue-300 text-sm">允许收集使用数据以改进服务</p>
                      </div>
                      <Switch
                        checked={settings.privacy.dataCollection}
                        onCheckedChange={(checked) => updateSettings('privacy', 'dataCollection', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">分析统计</Label>
                        <p className="text-blue-300 text-sm">发送匿名使用统计</p>
                      </div>
                      <Switch
                        checked={settings.privacy.analytics}
                        onCheckedChange={(checked) => updateSettings('privacy', 'analytics', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">崩溃报告</Label>
                        <p className="text-blue-300 text-sm">自动发送错误报告</p>
                      </div>
                      <Switch
                        checked={settings.privacy.crashReports}
                        onCheckedChange={(checked) => updateSettings('privacy', 'crashReports', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-white">对话历史</Label>
                        <p className="text-blue-300 text-sm">保存对话记录</p>
                      </div>
                      <Switch
                        checked={settings.privacy.conversationHistory}
                        onCheckedChange={(checked) => updateSettings('privacy', 'conversationHistory', checked)}
                      />
                    </div>
                  </div>

                  {/* 数据保留 */}
                  <div>
                    <Label className="text-white">语音数据保留期限</Label>
                    <Select
                      value={settings.privacy.voiceDataRetention.toString()}
                      onValueChange={(value) => updateSettings('privacy', 'voiceDataRetention', parseInt(value))}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7">7天</SelectItem>
                        <SelectItem value="30">30天</SelectItem>
                        <SelectItem value="90">90天</SelectItem>
                        <SelectItem value="365">1年</SelectItem>
                        <SelectItem value="0">永久保留</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-blue-300 text-xs mt-1">语音录音文件的保留时间</p>
                  </div>

                  {/* 数据管理 */}
                  <div className="space-y-3">
                    <h4 className="text-white font-medium">数据管理</h4>
                    <div className="flex space-x-3">
                      <Button
                        variant="outline"
                        className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        导出我的数据
                      </Button>
                      <Button
                        variant="outline"
                        className="border-red-400/30 text-red-200 hover:bg-red-500/20"
                        onClick={() => {
                          if (confirm('确定要删除所有个人数据吗？此操作不可撤销。')) {
                            // TODO: 实现数据删除逻辑
                          }
                        }}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        删除所有数据
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </ScrollArea>

          {/* 底部操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t border-white/10">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                onClick={handleExport}
                className="text-blue-200 hover:bg-white/10"
              >
                <Download className="w-4 h-4 mr-2" />
                导出设置
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => document.getElementById('import-settings')?.click()}
                className="text-blue-200 hover:bg-white/10"
              >
                <Upload className="w-4 h-4 mr-2" />
                导入设置
              </Button>
              <input
                id="import-settings"
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
              
              <Button
                variant="ghost"
                onClick={handleReset}
                className="text-red-200 hover:bg-red-500/20"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                重置
              </Button>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                取消
              </Button>
              <Button
                onClick={handleSave}
                disabled={!hasChanges || isSaving}
                className="bg-blue-500 hover:bg-blue-600"
              >
                {isSaving ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    保存设置
                  </>
                )}
              </Button>
            </div>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
