import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  <PERSON>, 
  Bell,
  Mic,
  <PERSON>c<PERSON><PERSON>,
  Save,
  X,
  AlertCircle
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { voiceService } from '@/services/apiService';

interface ScheduleEvent {
  id?: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  priority: 'low' | 'medium' | 'high';
  reminderMinutes: number;
  isRecurring: boolean;
  recurringType?: 'daily' | 'weekly' | 'monthly';
}

interface ScheduleFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (event: ScheduleEvent) => void;
  event?: ScheduleEvent | null;
  mode: 'create' | 'edit';
}

export default function ScheduleFormDialog({ 
  open, 
  onClose, 
  onSave, 
  event, 
  mode 
}: ScheduleFormDialogProps) {
  const [formData, setFormData] = useState<ScheduleEvent>({
    title: '',
    description: '',
    startTime: new Date(),
    endTime: new Date(Date.now() + 3600000), // 1小时后
    location: '',
    attendees: [],
    priority: 'medium',
    reminderMinutes: 15,
    isRecurring: false,
  });

  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (event && mode === 'edit') {
      setFormData({
        ...event,
        attendees: event.attendees || [],
      });
    } else {
      // 重置为默认值
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 3600000);
      
      setFormData({
        title: '',
        description: '',
        startTime: now,
        endTime: oneHourLater,
        location: '',
        attendees: [],
        priority: 'medium',
        reminderMinutes: 15,
        isRecurring: false,
      });
    }
    setErrors({});
  }, [event, mode, open]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入日程标题';
    }

    if (formData.startTime >= formData.endTime) {
      newErrors.endTime = '结束时间必须晚于开始时间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSaving(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('保存日程失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 语音输入处理
  const handleVoiceInput = async (field: 'title' | 'description' | 'location') => {
    if (isRecording) {
      setIsRecording(false);
      return;
    }

    setIsRecording(true);
    try {
      // 模拟录音过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 模拟语音识别
      const mockAudioBlob = new Blob([], { type: 'audio/wav' });
      const transcription = await voiceService.recognizeSpeech(mockAudioBlob);
      
      setFormData(prev => ({
        ...prev,
        [field]: transcription
      }));
    } catch (error) {
      console.error('语音输入失败:', error);
    } finally {
      setIsRecording(false);
    }
  };

  // 智能解析语音内容
  const handleSmartVoiceInput = async () => {
    if (isRecording) {
      setIsRecording(false);
      return;
    }

    setIsRecording(true);
    try {
      // 模拟录音过程
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      // 模拟智能解析
      const mockScheduleText = "明天下午2点到4点在会议室A开项目讨论会，邀请张三李四参加，高优先级，提前30分钟提醒";
      
      // 解析结果（实际应该调用AI服务）
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(14, 0, 0, 0);
      
      const endTime = new Date(tomorrow);
      endTime.setHours(16, 0, 0, 0);
      
      setFormData(prev => ({
        ...prev,
        title: '项目讨论会',
        description: '项目讨论会议',
        startTime: tomorrow,
        endTime: endTime,
        location: '会议室A',
        attendees: ['张三', '李四'],
        priority: 'high',
        reminderMinutes: 30,
      }));
      
    } catch (error) {
      console.error('智能语音解析失败:', error);
    } finally {
      setIsRecording(false);
    }
  };

  // 格式化日期时间为输入框格式
  const formatDateTimeLocal = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // 解析日期时间输入
  const parseDateTimeLocal = (value: string): Date => {
    return new Date(value);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-blue-400" />
            <span>{mode === 'create' ? '新增日程' : '编辑日程'}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 智能语音输入 */}
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Mic className="w-4 h-4 text-blue-400" />
                <span className="text-blue-200 text-sm font-medium">智能语音创建</span>
              </div>
              <Switch
                checked={isVoiceMode}
                onCheckedChange={setIsVoiceMode}
              />
            </div>
            
            {isVoiceMode && (
              <div className="space-y-3">
                <p className="text-blue-300 text-xs">
                  说出完整的日程信息，AI将自动解析并填充表单
                </p>
                <Button
                  onClick={handleSmartVoiceInput}
                  disabled={isRecording}
                  className={`w-full ${
                    isRecording 
                      ? 'bg-red-500 hover:bg-red-600' 
                      : 'bg-blue-500 hover:bg-blue-600'
                  }`}
                >
                  {isRecording ? (
                    <>
                      <MicOff className="w-4 h-4 mr-2" />
                      录音中... 点击停止
                    </>
                  ) : (
                    <>
                      <Mic className="w-4 h-4 mr-2" />
                      开始语音输入
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>

          {/* 基本信息 */}
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="title" className="text-white">日程标题 *</Label>
              <div className="flex space-x-2 mt-1">
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="输入日程标题..."
                  className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVoiceInput('title')}
                  disabled={isRecording}
                  className="text-blue-400 hover:bg-blue-500/20"
                >
                  <Mic className="w-4 h-4" />
                </Button>
              </div>
              {errors.title && (
                <p className="text-red-400 text-xs mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.title}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="description" className="text-white">描述</Label>
              <div className="flex space-x-2 mt-1">
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="输入日程描述..."
                  className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[80px]"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVoiceInput('description')}
                  disabled={isRecording}
                  className="text-blue-400 hover:bg-blue-500/20 self-start"
                >
                  <Mic className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* 时间设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startTime" className="text-white">开始时间 *</Label>
              <Input
                id="startTime"
                type="datetime-local"
                value={formatDateTimeLocal(formData.startTime)}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  startTime: parseDateTimeLocal(e.target.value) 
                }))}
                className="bg-white/10 border-white/20 text-white mt-1"
              />
            </div>

            <div>
              <Label htmlFor="endTime" className="text-white">结束时间 *</Label>
              <Input
                id="endTime"
                type="datetime-local"
                value={formatDateTimeLocal(formData.endTime)}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  endTime: parseDateTimeLocal(e.target.value) 
                }))}
                className="bg-white/10 border-white/20 text-white mt-1"
              />
              {errors.endTime && (
                <p className="text-red-400 text-xs mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.endTime}
                </p>
              )}
            </div>
          </div>

          {/* 地点和参与者 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="location" className="text-white">地点</Label>
              <div className="flex space-x-2 mt-1">
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="输入地点..."
                  className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVoiceInput('location')}
                  disabled={isRecording}
                  className="text-blue-400 hover:bg-blue-500/20"
                >
                  <Mic className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="attendees" className="text-white">参与者</Label>
              <Input
                id="attendees"
                value={formData.attendees?.join(', ') || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  attendees: e.target.value.split(',').map(s => s.trim()).filter(s => s) 
                }))}
                placeholder="输入参与者，用逗号分隔..."
                className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 mt-1"
              />
            </div>
          </div>

          {/* 优先级和提醒 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-white">优先级</Label>
              <Select 
                value={formData.priority} 
                onValueChange={(value: 'low' | 'medium' | 'high') => 
                  setFormData(prev => ({ ...prev, priority: value }))
                }
              >
                <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">低优先级</SelectItem>
                  <SelectItem value="medium">中优先级</SelectItem>
                  <SelectItem value="high">高优先级</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-white">提醒时间</Label>
              <Select 
                value={formData.reminderMinutes.toString()} 
                onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, reminderMinutes: parseInt(value) }))
                }
              >
                <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">不提醒</SelectItem>
                  <SelectItem value="5">5分钟前</SelectItem>
                  <SelectItem value="15">15分钟前</SelectItem>
                  <SelectItem value="30">30分钟前</SelectItem>
                  <SelectItem value="60">1小时前</SelectItem>
                  <SelectItem value="1440">1天前</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 重复设置 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.isRecurring}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRecurring: checked }))}
              />
              <Label className="text-white">重复日程</Label>
            </div>
            
            {formData.isRecurring && (
              <Select 
                value={formData.recurringType || 'weekly'} 
                onValueChange={(value: 'daily' | 'weekly' | 'monthly') => 
                  setFormData(prev => ({ ...prev, recurringType: value }))
                }
              >
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">每天</SelectItem>
                  <SelectItem value="weekly">每周</SelectItem>
                  <SelectItem value="monthly">每月</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
            <Button
              variant="ghost"
              onClick={onClose}
              disabled={isSaving}
              className="text-gray-400 hover:text-white"
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSaving}
              className="bg-blue-500 hover:bg-blue-600"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {mode === 'create' ? '创建日程' : '保存修改'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
