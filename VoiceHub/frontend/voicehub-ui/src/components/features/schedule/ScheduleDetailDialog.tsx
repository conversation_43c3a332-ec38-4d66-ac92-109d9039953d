import React, { useState } from 'react';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  <PERSON>, 
  <PERSON>,
  Mic,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  Share2,
  CheckCircle,
  AlertCircle,
  XCircle,
  MoreVertical
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface ScheduleEvent {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  priority: 'low' | 'medium' | 'high';
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  reminderMinutes: number;
  isRecurring: boolean;
  recurringType?: 'daily' | 'weekly' | 'monthly';
  isVoiceCreated?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ScheduleDetailDialogProps {
  open: boolean;
  onClose: () => void;
  event: ScheduleEvent | null;
  onEdit: (event: ScheduleEvent) => void;
  onDelete: (eventId: string) => void;
  onStatusChange: (eventId: string, status: ScheduleEvent['status']) => void;
}

export default function ScheduleDetailDialog({ 
  open, 
  onClose, 
  event, 
  onEdit, 
  onDelete,
  onStatusChange 
}: ScheduleDetailDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  if (!event) return null;

  // 获取状态图标和颜色
  const getStatusInfo = (status: ScheduleEvent['status']) => {
    switch (status) {
      case 'upcoming':
        return { 
          icon: Clock, 
          color: 'text-blue-400 bg-blue-500/20 border-blue-400/30',
          label: '即将开始'
        };
      case 'ongoing':
        return { 
          icon: AlertCircle, 
          color: 'text-orange-400 bg-orange-500/20 border-orange-400/30',
          label: '进行中'
        };
      case 'completed':
        return { 
          icon: CheckCircle, 
          color: 'text-green-400 bg-green-500/20 border-green-400/30',
          label: '已完成'
        };
      case 'cancelled':
        return { 
          icon: XCircle, 
          color: 'text-red-400 bg-red-500/20 border-red-400/30',
          label: '已取消'
        };
      default:
        return { 
          icon: Clock, 
          color: 'text-gray-400 bg-gray-500/20 border-gray-400/30',
          label: '未知'
        };
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      case 'medium':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'low':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 格式化日期时间
  const formatDateTime = (date: Date): string => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      weekday: 'long'
    });
  };

  // 计算持续时间
  const getDuration = (): string => {
    const diffMs = event.endTime.getTime() - event.startTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}小时${diffMinutes > 0 ? `${diffMinutes}分钟` : ''}`;
    } else {
      return `${diffMinutes}分钟`;
    }
  };

  // 获取提醒文本
  const getReminderText = (minutes: number): string => {
    if (minutes === 0) return '无提醒';
    if (minutes < 60) return `${minutes}分钟前`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`;
    return `${Math.floor(minutes / 1440)}天前`;
  };

  // 处理删除
  const handleDelete = async () => {
    if (!confirm('确定要删除这个日程吗？此操作不可撤销。')) return;
    
    setIsDeleting(true);
    try {
      await onDelete(event.id);
      onClose();
    } catch (error) {
      console.error('删除日程失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // 处理状态变更
  const handleStatusChange = (newStatus: ScheduleEvent['status']) => {
    onStatusChange(event.id, newStatus);
  };

  // 复制日程信息
  const handleCopy = () => {
    const text = `
日程：${event.title}
时间：${formatDateTime(event.startTime)} - ${formatDateTime(event.endTime)}
地点：${event.location || '无'}
描述：${event.description || '无'}
参与者：${event.attendees?.join(', ') || '无'}
    `.trim();
    
    navigator.clipboard.writeText(text);
    // TODO: 添加toast提示
  };

  const statusInfo = getStatusInfo(event.status);
  const StatusIcon = statusInfo.icon;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-2xl">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-blue-400" />
              <span>日程详情</span>
            </DialogTitle>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-gray-800 border-gray-700">
                <DropdownMenuItem onClick={() => onEdit(event)} className="text-white hover:bg-gray-700">
                  <Edit3 className="w-4 h-4 mr-2" />
                  编辑日程
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCopy} className="text-white hover:bg-gray-700">
                  <Copy className="w-4 h-4 mr-2" />
                  复制信息
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDelete} className="text-red-400 hover:bg-red-500/20">
                  <Trash2 className="w-4 h-4 mr-2" />
                  删除日程
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* 标题和状态 */}
          <div className="space-y-3">
            <div className="flex items-start justify-between">
              <h2 className="text-2xl font-bold text-white">{event.title}</h2>
              <div className="flex items-center space-x-2">
                {event.isVoiceCreated && (
                  <Badge variant="outline" className="border-purple-400/30 text-purple-200">
                    <Mic className="w-3 h-3 mr-1" />
                    语音创建
                  </Badge>
                )}
                <Badge className={getPriorityColor(event.priority)}>
                  {event.priority === 'high' ? '高优先级' : 
                   event.priority === 'medium' ? '中优先级' : '低优先级'}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <StatusIcon className="w-4 h-4 text-current" />
              <Badge className={statusInfo.color}>
                {statusInfo.label}
              </Badge>
            </div>
          </div>

          <Separator className="bg-white/10" />

          {/* 时间信息 */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <Clock className="w-5 h-5 mr-2 text-blue-400" />
              时间安排
            </h3>
            
            <div className="bg-white/5 rounded-lg p-4 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-blue-200">开始时间</span>
                <span className="text-white font-medium">{formatDateTime(event.startTime)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-blue-200">结束时间</span>
                <span className="text-white font-medium">{formatDateTime(event.endTime)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-blue-200">持续时间</span>
                <span className="text-white font-medium">{getDuration()}</span>
              </div>
              {event.isRecurring && (
                <div className="flex justify-between items-center">
                  <span className="text-blue-200">重复</span>
                  <span className="text-white font-medium">
                    {event.recurringType === 'daily' ? '每天' :
                     event.recurringType === 'weekly' ? '每周' : '每月'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-4">
            {event.description && (
              <div>
                <h4 className="text-white font-medium mb-2">描述</h4>
                <p className="text-blue-200 bg-white/5 rounded-lg p-3">{event.description}</p>
              </div>
            )}

            {event.location && (
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-green-400" />
                <div>
                  <span className="text-blue-200 text-sm">地点</span>
                  <p className="text-white">{event.location}</p>
                </div>
              </div>
            )}

            {event.attendees && event.attendees.length > 0 && (
              <div className="flex items-start space-x-3">
                <Users className="w-4 h-4 text-purple-400 mt-1" />
                <div>
                  <span className="text-blue-200 text-sm">参与者 ({event.attendees.length}人)</span>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {event.attendees.map((attendee, index) => (
                      <Badge key={index} variant="outline" className="text-white border-white/20">
                        {attendee}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3">
              <Bell className="w-4 h-4 text-yellow-400" />
              <div>
                <span className="text-blue-200 text-sm">提醒</span>
                <p className="text-white">{getReminderText(event.reminderMinutes)}</p>
              </div>
            </div>
          </div>

          <Separator className="bg-white/10" />

          {/* 状态操作 */}
          {event.status === 'upcoming' && (
            <div className="space-y-3">
              <h4 className="text-white font-medium">快速操作</h4>
              <div className="flex flex-wrap gap-2">
                <Button
                  size="sm"
                  onClick={() => handleStatusChange('ongoing')}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  <AlertCircle className="w-3 h-3 mr-1" />
                  标记为进行中
                </Button>
                <Button
                  size="sm"
                  onClick={() => handleStatusChange('completed')}
                  className="bg-green-500 hover:bg-green-600"
                >
                  <CheckCircle className="w-3 h-3 mr-1" />
                  标记为已完成
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleStatusChange('cancelled')}
                  className="border-red-400/30 text-red-400 hover:bg-red-500/20"
                >
                  <XCircle className="w-3 h-3 mr-1" />
                  取消日程
                </Button>
              </div>
            </div>
          )}

          {/* 元信息 */}
          <div className="text-xs text-gray-400 space-y-1">
            <p>创建时间：{event.createdAt.toLocaleString('zh-CN')}</p>
            <p>更新时间：{event.updatedAt.toLocaleString('zh-CN')}</p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
            <Button
              variant="ghost"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              关闭
            </Button>
            <Button
              onClick={() => onEdit(event)}
              className="bg-blue-500 hover:bg-blue-600"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              编辑日程
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
