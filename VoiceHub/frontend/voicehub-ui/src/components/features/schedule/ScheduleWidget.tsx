import React, { useState } from 'react';
import {
  Calendar,
  Clock,
  Plus,
  MapPin,
  Users,
  Bell,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  Mic,
  CheckCircle,
  AlertCircle,
  Edit3,
  Trash2,
  Eye
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import ScheduleFormDialog from './ScheduleFormDialog';
import ScheduleDetailDialog from './ScheduleDetailDialog';

interface ScheduleEvent {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees?: string[];
  priority: 'low' | 'medium' | 'high';
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  reminderMinutes: number;
  isRecurring: boolean;
  recurringType?: 'daily' | 'weekly' | 'monthly';
  isVoiceCreated?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ScheduleWidgetProps {
  expanded?: boolean;
}

const ScheduleWidget: React.FC<ScheduleWidgetProps> = ({ expanded = false }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [showFormDialog, setShowFormDialog] = useState(false);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<ScheduleEvent | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [events, setEvents] = useState<ScheduleEvent[]>([]);

  // 初始化Mock数据
  React.useEffect(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const mockEvents: ScheduleEvent[] = [
      {
        id: '1',
        title: 'Team Standup Meeting',
        description: '每日站会，讨论项目进展和问题',
        startTime: new Date(today.getTime() + 9 * 60 * 60 * 1000), // 9:00
        endTime: new Date(today.getTime() + 9.5 * 60 * 60 * 1000), // 9:30
        location: 'Conference Room A',
        attendees: ['张三', '李四', '王五', '赵六'],
        priority: 'high',
        status: 'upcoming',
        reminderMinutes: 15,
        isRecurring: true,
        recurringType: 'daily',
        isVoiceCreated: true,
        createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000),
        updatedAt: new Date(now.getTime() - 2 * 60 * 60 * 1000)
      },
      {
        id: '2',
        title: 'Client Presentation',
        description: '向客户展示项目最新进展和功能演示',
        startTime: new Date(today.getTime() + 14 * 60 * 60 * 1000), // 14:00
        endTime: new Date(today.getTime() + 15 * 60 * 60 * 1000), // 15:00
        location: 'Virtual Meeting',
        attendees: ['产品经理', '技术总监', '客户代表'],
        priority: 'high',
        status: 'upcoming',
        reminderMinutes: 30,
        isRecurring: false,
        isVoiceCreated: false,
        createdAt: new Date(now.getTime() - 48 * 60 * 60 * 1000),
        updatedAt: new Date(now.getTime() - 12 * 60 * 60 * 1000)
      },
      {
        id: '3',
        title: 'Code Review Session',
        description: '代码审查会议，检查最新提交的代码质量',
        startTime: new Date(today.getTime() + 16.5 * 60 * 60 * 1000), // 16:30
        endTime: new Date(today.getTime() + 17.25 * 60 * 60 * 1000), // 17:15
        location: '开发区',
        attendees: ['前端开发', '后端开发'],
        priority: 'medium',
        status: 'upcoming',
        reminderMinutes: 10,
        isRecurring: false,
        isVoiceCreated: true,
        createdAt: new Date(now.getTime() - 72 * 60 * 60 * 1000),
        updatedAt: new Date(now.getTime() - 1 * 60 * 60 * 1000)
      },
      {
        id: '4',
        title: 'Project Planning',
        description: '项目规划会议，讨论下一阶段的开发计划',
        startTime: new Date(today.getTime() + 11 * 60 * 60 * 1000), // 11:00
        endTime: new Date(today.getTime() + 13 * 60 * 60 * 1000), // 13:00
        location: 'Meeting Room B',
        attendees: ['项目经理', '架构师', '开发团队'],
        priority: 'medium',
        status: 'completed',
        reminderMinutes: 15,
        isRecurring: false,
        isVoiceCreated: false,
        createdAt: new Date(now.getTime() - 96 * 60 * 60 * 1000),
        updatedAt: new Date(now.getTime() - 6 * 60 * 60 * 1000)
      }
    ];

    setEvents(mockEvents);
  }, []);

  // 事件处理函数
  const handleCreateEvent = () => {
    setFormMode('create');
    setSelectedEvent(null);
    setShowFormDialog(true);
  };

  const handleEditEvent = (event: ScheduleEvent) => {
    setFormMode('edit');
    setSelectedEvent(event);
    setShowFormDialog(true);
  };

  const handleViewEvent = (event: ScheduleEvent) => {
    setSelectedEvent(event);
    setShowDetailDialog(true);
  };

  const handleSaveEvent = (eventData: Omit<ScheduleEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
    const now = new Date();

    if (formMode === 'create') {
      const newEvent: ScheduleEvent = {
        ...eventData,
        id: Date.now().toString(),
        createdAt: now,
        updatedAt: now
      };
      setEvents(prev => [...prev, newEvent]);
    } else if (selectedEvent) {
      setEvents(prev => prev.map(event =>
        event.id === selectedEvent.id
          ? { ...eventData, id: selectedEvent.id, createdAt: selectedEvent.createdAt, updatedAt: now }
          : event
      ));
    }

    setShowFormDialog(false);
  };

  const handleDeleteEvent = (eventId: string) => {
    setEvents(prev => prev.filter(event => event.id !== eventId));
  };

  const handleStatusChange = (eventId: string, status: ScheduleEvent['status']) => {
    setEvents(prev => prev.map(event =>
      event.id === eventId
        ? { ...event, status, updatedAt: new Date() }
        : event
    ));
  };

  const todayEvents = events.filter(event => {
    const eventDate = new Date(event.startTime);
    return eventDate.toDateString() === selectedDate.toDateString();
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-200 border-red-400/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-200 border-yellow-400/30';
      case 'low':
        return 'bg-green-500/20 text-green-200 border-green-400/30';
      default:
        return 'bg-blue-500/20 text-blue-200 border-blue-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'ongoing':
        return <AlertCircle className="w-4 h-4 text-orange-400" />;
      default:
        return <Clock className="w-4 h-4 text-blue-400" />;
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  return (
    <>
      <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
        expanded ? 'col-span-full' : ''
      }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <span>Schedule</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVoiceMode(!isVoiceMode)}
              className={`text-white hover:bg-white/10 ${
                isVoiceMode ? 'bg-blue-500/20 border border-blue-400/30' : ''
              }`}
            >
              <Mic className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCreateEvent}
              className="text-white hover:bg-white/10"
            >
              <Plus className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Date Navigation */}
        <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDate('prev')}
            className="text-blue-200 hover:bg-white/10"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <div className="text-center">
            <h3 className="text-white font-medium">{formatDate(selectedDate)}</h3>
            <p className="text-blue-200 text-sm">{todayEvents.length} events</p>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDate('next')}
            className="text-blue-200 hover:bg-white/10"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Voice Command Hint */}
        {isVoiceMode && (
          <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <Mic className="w-4 h-4 text-blue-400" />
              <span className="text-blue-200 text-sm font-medium">Voice Mode Active</span>
            </div>
            <p className="text-blue-300 text-xs">
              Try: "Schedule a meeting tomorrow at 2 PM" or "What's my schedule for today?"
            </p>
          </div>
        )}

        {/* Events List */}
        <ScrollArea className={expanded ? "h-[400px]" : "h-[250px]"}>
          <div className="space-y-3">
            {todayEvents.length > 0 ? (
              todayEvents.map((event) => (
                <div
                  key={event.id}
                  className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                  onClick={() => handleViewEvent(event)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(event.status)}
                      <h4 className="text-white font-medium text-sm">{event.title}</h4>
                      {event.isVoiceCreated && (
                        <Badge variant="outline" className="border-purple-400/30 text-purple-200 text-xs">
                          <Mic className="w-2 h-2 mr-1" />
                          Voice
                        </Badge>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-300 hover:text-white"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="bg-gray-800 border-gray-700">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewEvent(event);
                          }}
                          className="text-white hover:bg-gray-700"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditEvent(event);
                          }}
                          className="text-white hover:bg-gray-700"
                        >
                          <Edit3 className="w-4 h-4 mr-2" />
                          编辑日程
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEvent(event.id);
                          }}
                          className="text-red-400 hover:bg-red-500/20"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          删除日程
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="flex items-center space-x-2 text-blue-200 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>
                        {event.startTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })} -
                        {event.endTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                    {event.location && (
                      <div className="flex items-center space-x-2 text-blue-200 text-xs">
                        <MapPin className="w-3 h-3" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    {event.attendees && event.attendees.length > 0 && (
                      <div className="flex items-center space-x-2 text-blue-200 text-xs">
                        <Users className="w-3 h-3" />
                        <span>{event.attendees.length} attendees</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className={`text-xs ${getPriorityColor(event.priority)}`}>
                        {event.priority.toUpperCase()}
                      </Badge>
                    </div>
                  </div>

                  {event.status === 'upcoming' && (
                    <div className="flex items-center justify-between pt-3 border-t border-white/10">
                      <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white text-xs">
                        <Bell className="w-3 h-3 mr-1" />
                        Remind me
                      </Button>
                      <Button variant="ghost" size="sm" className="text-green-300 hover:text-white text-xs">
                        Join Meeting
                      </Button>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-blue-400 mx-auto mb-3 opacity-50" />
                <p className="text-blue-200 text-sm">No events scheduled for this day</p>
              </div>
            )}

            {/* 新增事件按钮 - 始终显示 */}
            <div className="text-center py-4 border-t border-white/10 mt-4">
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-300 hover:text-white"
                onClick={handleCreateEvent}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Event
              </Button>
            </div>
          </div>
        </ScrollArea>

        {/* Quick Stats */}
        {expanded && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-white/10">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.status === 'upcoming').length}</p>
              <p className="text-blue-200 text-xs">Upcoming</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.status === 'completed').length}</p>
              <p className="text-blue-200 text-xs">Completed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{events.filter(e => e.isVoiceCreated).length}</p>
              <p className="text-blue-200 text-xs">Voice Created</p>
            </div>
          </div>
        )}
      </CardContent>
      </Card>

      {/* 对话框组件 */}
      <ScheduleFormDialog
        open={showFormDialog}
        onClose={() => setShowFormDialog(false)}
        onSave={handleSaveEvent}
        event={selectedEvent}
        mode={formMode}
      />

      <ScheduleDetailDialog
        open={showDetailDialog}
        onClose={() => setShowDetailDialog(false)}
        event={selectedEvent}
        onEdit={handleEditEvent}
        onDelete={handleDeleteEvent}
        onStatusChange={handleStatusChange}
      />
    </>
  );
};

export default ScheduleWidget;