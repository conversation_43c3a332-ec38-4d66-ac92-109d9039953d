import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Mic,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  User,
  Setting<PERSON>,
  Trash2,
  Copy,
  Volume2,
  RotateCcw,
  MessageSquare,
  Eye,
  Edit3,
  Star,
  StarOff,
  MoreVertical,
  History,
  Plus
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { chatService, voiceService } from '@/services/apiService';
import type { ChatMessage } from '@/services/mockDataService';
import ConversationDetailDialog from './ConversationDetailDialog';
import ConversationEditDialog from './ConversationEditDialog';

// 移除重复的类型定义，使用从服务导入的类型

// AI模型选项
const AI_MODELS = [
  { value: 'gpt-3.5-turbo', label: 'ChatGPT 3.5', cost: '低成本' },
  { value: 'gpt-4', label: 'ChatGPT 4', cost: '高质量' },
  { value: 'qwen-turbo', label: '通义千问', cost: '中等' },
  { value: 'glm-4', label: '智谱AI', cost: '国产' },
];

// 对话历史类型定义
interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  tokens?: number;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  model: string;
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  totalTokens: number;
  isStarred: boolean;
  summary?: string;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    complexity: 'low' | 'medium' | 'high';
    satisfaction: number;
  };
}

interface BasicChatWidgetProps {
  expanded?: boolean;
  className?: string;
}

export default function BasicChatWidget({ expanded = false, className = '' }: BasicChatWidgetProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [conversationId, setConversationId] = useState<string>('');

  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gpt-3.5-turbo');
  const [isRecording, setIsRecording] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // 对话历史管理状态
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [editMode, setEditMode] = useState<'create' | 'edit'>('create');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // 初始化对话
  useEffect(() => {
    const initializeChat = async () => {
      try {
        const history = await chatService.getChatHistory();
        setMessages(history);

        if (history.length === 0) {
          // 添加欢迎消息
          const welcomeMessage: ChatMessage = {
            id: '1',
            content: '你好！我是VoiceHub AI助手，可以帮你处理各种问题。你可以直接输入文字或使用语音输入与我对话。',
            role: 'assistant',
            timestamp: new Date(),
            model: selectedModel
          };
          setMessages([welcomeMessage]);
        }
      } catch (error) {
        console.error('Failed to load chat history:', error);
      }
    };

    initializeChat();
  }, []);

  // 初始化对话历史数据
  useEffect(() => {
    const mockConversations: Conversation[] = [
      {
        id: '1',
        title: 'VoiceHub功能咨询',
        messages: [
          {
            id: '1-1',
            role: 'user',
            content: '请介绍一下VoiceHub的主要功能',
            timestamp: new Date(Date.now() - 86400000),
            tokens: 15
          },
          {
            id: '1-2',
            role: 'assistant',
            content: 'VoiceHub是一个智能语音助手平台，主要功能包括：\n1. AI对话 - 支持多种大模型对话\n2. 语音笔记 - 自动转写和AI分析\n3. 创意AI - 内容分析和格式转换\n4. 日程管理 - 语音创建和管理日程\n\n每个功能都支持语音输入，让您的工作更加高效便捷。',
            timestamp: new Date(Date.now() - 86400000 + 30000),
            tokens: 120
          }
        ],
        model: 'gpt-4',
        category: 'general',
        tags: ['功能介绍', '产品咨询'],
        createdAt: new Date(Date.now() - 86400000),
        updatedAt: new Date(Date.now() - 86400000 + 30000),
        totalTokens: 135,
        isStarred: true,
        summary: '用户咨询VoiceHub平台的主要功能，AI助手详细介绍了四大核心功能模块。',
        aiAnalysis: {
          sentiment: 'positive',
          topics: ['产品介绍', '功能咨询', '平台特性'],
          complexity: 'low',
          satisfaction: 5
        }
      },
      {
        id: '2',
        title: '技术实现讨论',
        messages: [
          {
            id: '2-1',
            role: 'user',
            content: '语音识别的准确率如何？支持哪些语言？',
            timestamp: new Date(Date.now() - 172800000),
            tokens: 20
          },
          {
            id: '2-2',
            role: 'assistant',
            content: '我们的语音识别系统具有以下特点：\n\n准确率：\n- 中文识别准确率达到95%以上\n- 英文识别准确率达到98%以上\n- 支持方言和口音适应\n\n支持语言：\n- 中文（普通话、粤语、四川话等）\n- 英语（美式、英式）\n- 日语、韩语\n- 其他主流语言正在扩展中\n\n技术优势：\n- 实时转写，低延迟\n- 噪音抑制和回声消除\n- 上下文理解和智能纠错',
            timestamp: new Date(Date.now() - 172800000 + 45000),
            tokens: 180
          }
        ],
        model: 'gpt-3.5-turbo',
        category: 'technical',
        tags: ['语音识别', '技术咨询', '多语言'],
        createdAt: new Date(Date.now() - 172800000),
        updatedAt: new Date(Date.now() - 172800000 + 45000),
        totalTokens: 200,
        isStarred: false,
        summary: '用户询问语音识别的准确率和语言支持情况，AI详细介绍了技术特点和优势。',
        aiAnalysis: {
          sentiment: 'neutral',
          topics: ['语音技术', '多语言支持', '技术规格'],
          complexity: 'medium',
          satisfaction: 4
        }
      }
    ];

    setConversations(mockConversations);
  }, []);

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputText.trim(),
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // 使用API服务发送消息
      const aiResponse = await chatService.sendMessage(
        userMessage.content,
        conversationId,
        selectedModel
      );

      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Failed to send message:', error);

      // 错误处理
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '抱歉，AI服务暂时不可用，请稍后重试。',
        role: 'assistant',
        timestamp: new Date(),
        model: selectedModel,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 删除generateMockAIResponse函数，现在使用API服务

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([{
      id: '1',
      content: '对话已清空，我们可以重新开始！',
      role: 'assistant',
      timestamp: new Date(),
      model: selectedModel
    }]);
  };

  // 复制消息
  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: 添加toast提示
  };

  // 语音录制和识别
  const handleVoiceRecord = async () => {
    if (isRecording) {
      // 停止录音
      setIsRecording(false);
      return;
    }

    setIsRecording(true);

    try {
      // 模拟录音过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 模拟语音识别
      const mockAudioBlob = new Blob([], { type: 'audio/wav' });
      const transcription = await voiceService.recognizeSpeech(mockAudioBlob);

      setInputText(transcription);
      inputRef.current?.focus();
    } catch (error) {
      console.error('Voice recognition failed:', error);
      setInputText('语音识别失败，请重试');
    } finally {
      setIsRecording(false);
    }
  };

  // 对话历史管理事件处理函数
  const handleViewConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setShowDetailDialog(true);
  };

  const handleEditConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setEditMode('edit');
    setShowEditDialog(true);
  };

  const handleCreateConversation = () => {
    setSelectedConversation(null);
    setEditMode('create');
    setShowEditDialog(true);
  };

  const handleSaveConversation = (conversationData: Conversation) => {
    if (editMode === 'create') {
      const newConversation: Conversation = {
        ...conversationData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setConversations(prev => [newConversation, ...prev]);
    } else if (selectedConversation) {
      setConversations(prev => prev.map(conv =>
        conv.id === selectedConversation.id
          ? { ...conversationData, id: selectedConversation.id, createdAt: selectedConversation.createdAt, updatedAt: new Date() }
          : conv
      ));
    }
    setShowEditDialog(false);
  };

  const handleDeleteConversation = (conversationId: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== conversationId));
  };

  const handleToggleStar = (conversationId: string) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? { ...conv, isStarred: !conv.isStarred }
        : conv
    ));
  };

  const handleExportConversation = (conversation: Conversation, format: 'txt' | 'md' | 'json') => {
    let content = '';

    switch (format) {
      case 'txt':
        content = `对话标题: ${conversation.title}\n创建时间: ${conversation.createdAt.toLocaleString()}\n\n`;
        content += conversation.messages.map(msg =>
          `${msg.role === 'user' ? '用户' : 'AI助手'}: ${msg.content}`
        ).join('\n\n');
        break;
      case 'md':
        content = `# ${conversation.title}\n\n**创建时间**: ${conversation.createdAt.toLocaleString()}\n\n`;
        content += conversation.messages.map(msg =>
          `**${msg.role === 'user' ? '用户' : 'AI助手'}**: ${msg.content}`
        ).join('\n\n');
        break;
      case 'json':
        content = JSON.stringify(conversation, null, 2);
        break;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${conversation.title}.${format}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 过滤对话历史
  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conv.messages.some(msg => msg.content.toLowerCase().includes(searchQuery.toLowerCase())) ||
                         conv.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || conv.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full h-[700px]' : 'h-[500px]'
    } ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <span>AI对话助手</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            {/* AI模型选择 */}
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {AI_MODELS.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    <div className="flex flex-col">
                      <span>{model.label}</span>
                      <span className="text-xs text-gray-500">{model.cost}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleClearChat}
              className="text-blue-200 hover:bg-white/10"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        <Tabs defaultValue="chat" className="flex flex-col h-full">
          <TabsList className="grid w-full grid-cols-2 bg-white/5 border-b border-white/10">
            <TabsTrigger value="chat" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <MessageSquare className="w-4 h-4 mr-2" />
              当前对话
            </TabsTrigger>
            <TabsTrigger value="history" className="text-blue-200 data-[state=active]:bg-white/10 data-[state=active]:text-white">
              <History className="w-4 h-4 mr-2" />
              对话历史 ({conversations.length})
            </TabsTrigger>
          </TabsList>

          {/* 当前对话标签页 */}
          <TabsContent value="chat" className="flex flex-col flex-1 mt-0">
            {/* 消息区域 */}
            <ScrollArea className="flex-1 px-6">
          <div className="space-y-4 pb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start space-x-3 ${
                  message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                {/* 头像 */}
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.role === 'user' 
                    ? 'bg-gradient-to-r from-blue-500 to-cyan-500' 
                    : 'bg-gradient-to-r from-purple-500 to-pink-500'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>

                {/* 消息内容 */}
                <div className={`flex-1 max-w-[80%] ${
                  message.role === 'user' ? 'text-right' : ''
                }`}>
                  <div className={`inline-block p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
                      : 'bg-white/10 text-white border border-white/20'
                  }`}>
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {message.content}
                    </p>
                    
                    {/* 消息元信息 */}
                    <div className="flex items-center justify-between mt-2 pt-2 border-t border-white/20">
                      <span className="text-xs opacity-70">
                        {message.timestamp.toLocaleTimeString()}
                        {message.model && ` • ${AI_MODELS.find(m => m.value === message.model)?.label}`}
                      </span>
                      
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyMessage(message.content)}
                          className="h-6 w-6 p-0 hover:bg-white/20"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        
                        {message.role === 'assistant' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-white/20"
                          >
                            <Volume2 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {/* 加载指示器 */}
            {isLoading && (
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div className="bg-white/10 border border-white/20 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <span className="text-white text-sm ml-2">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* 输入区域 */}
        <div className="p-6 border-t border-white/20">
          <div className="flex items-end space-x-2">
            {/* 语音录制按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVoiceRecord}
              className={`flex-shrink-0 ${
                isRecording 
                  ? 'text-red-400 hover:bg-red-500/20' 
                  : 'text-blue-200 hover:bg-white/10'
              }`}
            >
              {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            </Button>

            {/* 文本输入 */}
            <div className="flex-1">
              <Textarea
                ref={inputRef}
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={isRecording ? '正在录音...' : '输入消息或使用语音输入...'}
                disabled={isLoading || isRecording}
                className="min-h-[40px] max-h-[120px] bg-white/10 border-white/20 text-white placeholder:text-blue-200 resize-none"
                rows={1}
              />
            </div>

            {/* 发送按钮 */}
            <Button
              onClick={handleSendMessage}
              disabled={!inputText.trim() || isLoading}
              className="flex-shrink-0 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          
          {/* 状态信息 */}
          <div className="flex items-center justify-between mt-2 text-xs text-blue-200">
            <span>
              当前模型: {AI_MODELS.find(m => m.value === selectedModel)?.label}
            </span>
            <span>
              {messages.length - 1} 条对话 • Enter发送，Shift+Enter换行
            </span>
          </div>
            </div>
          </TabsContent>

          {/* 对话历史标签页 */}
          <TabsContent value="history" className="flex flex-col flex-1 mt-0">
            <div className="p-4 space-y-4">
              {/* 搜索和筛选 */}
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索对话..."
                    className="bg-white/10 border-white/20 text-white placeholder:text-blue-200"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    <SelectItem value="general">通用对话</SelectItem>
                    <SelectItem value="technical">技术咨询</SelectItem>
                    <SelectItem value="creative">创意写作</SelectItem>
                    <SelectItem value="learning">学习辅导</SelectItem>
                    <SelectItem value="business">商务咨询</SelectItem>
                    <SelectItem value="personal">个人助理</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleCreateConversation}
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  新建
                </Button>
              </div>

              {/* 对话历史列表 */}
              <ScrollArea className="flex-1">
                <div className="space-y-3">
                  {filteredConversations.length === 0 ? (
                    <div className="text-center py-8 text-blue-200">
                      <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>暂无对话历史</p>
                    </div>
                  ) : (
                    filteredConversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className="bg-white/5 border border-white/10 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                        onClick={() => handleViewConversation(conversation)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="text-white font-medium flex items-center space-x-2">
                                <span>{conversation.title}</span>
                                {conversation.isStarred && <Star className="w-4 h-4 text-yellow-400 fill-current" />}
                              </h4>
                              <Badge variant="outline" className="text-xs border-purple-400/30 text-purple-200">
                                {conversation.model}
                              </Badge>
                              <Badge variant="outline" className="text-xs border-blue-400/30 text-blue-200">
                                {conversation.category}
                              </Badge>
                            </div>

                            <p className="text-blue-200 text-sm mb-2 line-clamp-2">
                              {conversation.summary || conversation.messages[0]?.content || '暂无内容'}
                            </p>

                            <div className="flex items-center space-x-4 text-xs text-blue-300">
                              <span>{conversation.messages.length} 条消息</span>
                              <span>{conversation.totalTokens} tokens</span>
                              <span>{conversation.createdAt.toLocaleDateString()}</span>
                              <div className="flex items-center space-x-1">
                                {conversation.tags.slice(0, 2).map((tag, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                                {conversation.tags.length > 2 && (
                                  <span className="text-gray-400">+{conversation.tags.length - 2}</span>
                                )}
                              </div>
                            </div>
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-gray-400 hover:text-white"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreVertical className="w-3 h-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-gray-800 border-gray-700">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewConversation(conversation);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Eye className="w-4 h-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditConversation(conversation);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Edit3 className="w-4 h-4 mr-2" />
                                编辑对话
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleStar(conversation.id);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                {conversation.isStarred ? (
                                  <>
                                    <StarOff className="w-4 h-4 mr-2" />
                                    取消收藏
                                  </>
                                ) : (
                                  <>
                                    <Star className="w-4 h-4 mr-2" />
                                    添加收藏
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteConversation(conversation.id);
                                }}
                                className="text-red-400 hover:bg-red-500/20"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                删除对话
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* 对话框组件 */}
      <ConversationDetailDialog
        open={showDetailDialog}
        onClose={() => setShowDetailDialog(false)}
        conversation={selectedConversation}
        onEdit={handleEditConversation}
        onDelete={handleDeleteConversation}
        onToggleStar={handleToggleStar}
        onExport={handleExportConversation}
      />

      <ConversationEditDialog
        open={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        onSave={handleSaveConversation}
        conversation={selectedConversation}
        mode={editMode}
      />
    </Card>
  );
}
