import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Tag, 
  Save,
  X,
  AlertCircle,
  Sparkles,
  RefreshCw,
  Plus,
  Trash2
} from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { creativeAIService } from '@/services/apiService';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  tokens?: number;
}

interface Conversation {
  id?: string;
  title: string;
  messages: Message[];
  model: string;
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  totalTokens: number;
  isStarred: boolean;
  summary?: string;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    complexity: 'low' | 'medium' | 'high';
    satisfaction: number;
  };
}

interface ConversationEditDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (conversation: Conversation) => void;
  conversation?: Conversation | null;
  mode: 'create' | 'edit';
}

const CATEGORIES = [
  { value: 'general', label: '通用对话' },
  { value: 'technical', label: '技术咨询' },
  { value: 'creative', label: '创意写作' },
  { value: 'learning', label: '学习辅导' },
  { value: 'business', label: '商务咨询' },
  { value: 'personal', label: '个人助理' },
  { value: 'other', label: '其他' }
];

const MODELS = [
  { value: 'gpt-4', label: 'GPT-4' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
  { value: 'claude-3', label: 'Claude 3' },
  { value: 'gemini-pro', label: 'Gemini Pro' },
  { value: 'other', label: '其他模型' }
];

export default function ConversationEditDialog({ 
  open, 
  onClose, 
  onSave, 
  conversation, 
  mode 
}: ConversationEditDialogProps) {
  const [formData, setFormData] = useState<Conversation>({
    title: '',
    messages: [],
    model: 'gpt-4',
    category: 'general',
    tags: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    totalTokens: 0,
    isStarred: false,
    summary: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [tagInput, setTagInput] = useState('');

  // 初始化表单数据
  useEffect(() => {
    if (conversation && mode === 'edit') {
      setFormData({
        ...conversation,
        tags: conversation.tags || [],
      });
      setTagInput(conversation.tags?.join(', ') || '');
    } else {
      // 重置为默认值
      setFormData({
        title: '',
        messages: [],
        model: 'gpt-4',
        category: 'general',
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        totalTokens: 0,
        isStarred: false,
        summary: '',
      });
      setTagInput('');
    }
    setErrors({});
  }, [conversation, mode, open]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入对话标题';
    }

    if (formData.messages.length === 0) {
      newErrors.messages = '对话至少需要包含一条消息';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSaving(true);
    try {
      // 处理标签
      const tags = tagInput
        .split(',')
        .map(t => t.trim())
        .filter(t => t.length > 0);

      // 计算总tokens
      const totalTokens = formData.messages.reduce((sum, msg) => sum + (msg.tokens || 0), 0);

      const conversationData = {
        ...formData,
        tags,
        totalTokens,
        updatedAt: new Date(),
      };

      await onSave(conversationData);
      onClose();
    } catch (error) {
      console.error('保存对话失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // AI生成摘要
  const generateAISummary = async () => {
    if (formData.messages.length === 0) {
      alert('请先添加对话消息');
      return;
    }

    setIsGeneratingAI(true);
    try {
      const conversationText = formData.messages
        .map(msg => `${msg.role === 'user' ? '用户' : 'AI'}: ${msg.content}`)
        .join('\n');
      
      const summary = await creativeAIService.processContent(conversationText, 'summary');
      
      setFormData(prev => ({
        ...prev,
        summary: summary
      }));
    } catch (error) {
      console.error('AI摘要生成失败:', error);
      alert('AI摘要生成失败，请重试');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // 处理标签输入
  const handleTagChange = (value: string) => {
    setTagInput(value);
    const tags = value
      .split(',')
      .map(t => t.trim())
      .filter(t => t.length > 0);
    
    setFormData(prev => ({
      ...prev,
      tags
    }));
  };

  // 添加消息
  const addMessage = () => {
    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: '',
      timestamp: new Date(),
      tokens: 0
    };

    setFormData(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage]
    }));
  };

  // 删除消息
  const removeMessage = (messageId: string) => {
    setFormData(prev => ({
      ...prev,
      messages: prev.messages.filter(msg => msg.id !== messageId)
    }));
  };

  // 更新消息
  const updateMessage = (messageId: string, field: keyof Message, value: any) => {
    setFormData(prev => ({
      ...prev,
      messages: prev.messages.map(msg => 
        msg.id === messageId ? { ...msg, [field]: value } : msg
      )
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-blue-400" />
            <span>{mode === 'create' ? '新建对话' : '编辑对话'}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title" className="text-white">对话标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入对话标题..."
                className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 mt-1"
              />
              {errors.title && (
                <p className="text-red-400 text-xs mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.title}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="model" className="text-white">AI模型</Label>
              <Select 
                value={formData.model} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, model: value }))}
              >
                <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {MODELS.map(model => (
                    <SelectItem key={model.value} value={model.value}>
                      {model.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="category" className="text-white">分类</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map(cat => (
                    <SelectItem key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="starred"
                checked={formData.isStarred}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isStarred: !!checked }))}
              />
              <Label htmlFor="starred" className="text-white">收藏此对话</Label>
            </div>
          </div>

          {/* 对话消息 */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-white">对话消息</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={addMessage}
                className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20"
              >
                <Plus className="w-3 h-3 mr-1" />
                添加消息
              </Button>
            </div>

            <div className="space-y-3 max-h-60 overflow-y-auto">
              {formData.messages.map((message, index) => (
                <div key={message.id} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <Select
                      value={message.role}
                      onValueChange={(value: 'user' | 'assistant') => updateMessage(message.id, 'role', value)}
                    >
                      <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">用户</SelectItem>
                        <SelectItem value="assistant">AI助手</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMessage(message.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                  
                  <Textarea
                    value={message.content}
                    onChange={(e) => updateMessage(message.id, 'content', e.target.value)}
                    placeholder="输入消息内容..."
                    className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[80px]"
                  />
                  
                  <div className="flex items-center space-x-2 mt-2">
                    <Label className="text-xs text-gray-400">Tokens:</Label>
                    <Input
                      type="number"
                      value={message.tokens || 0}
                      onChange={(e) => updateMessage(message.id, 'tokens', parseInt(e.target.value) || 0)}
                      className="w-20 h-6 text-xs bg-white/10 border-white/20 text-white"
                    />
                  </div>
                </div>
              ))}
            </div>

            {errors.messages && (
              <p className="text-red-400 text-xs mt-1 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.messages}
              </p>
            )}
          </div>

          {/* AI摘要 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="summary" className="text-white">对话摘要</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={generateAISummary}
                disabled={isGeneratingAI}
                className="text-purple-400 hover:text-white"
              >
                {isGeneratingAI ? (
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                ) : (
                  <Sparkles className="w-3 h-3 mr-1" />
                )}
                生成摘要
              </Button>
            </div>
            <Textarea
              id="summary"
              value={formData.summary}
              onChange={(e) => setFormData(prev => ({ ...prev, summary: e.target.value }))}
              placeholder="AI生成的对话摘要..."
              className="bg-purple-500/10 border-purple-400/30 text-purple-100 placeholder:text-purple-300 min-h-[80px]"
            />
          </div>

          {/* 标签 */}
          <div>
            <Label htmlFor="tags" className="text-white">标签</Label>
            <Input
              id="tags"
              value={tagInput}
              onChange={(e) => handleTagChange(e.target.value)}
              placeholder="输入标签，用逗号分隔..."
              className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 mt-1"
            />
            
            {/* 标签预览 */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-blue-200 border-blue-400/30">
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* 元信息显示 */}
          {mode === 'edit' && conversation && (
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">对话信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm text-blue-200">
                <div>
                  <span className="text-gray-400">创建时间：</span>
                  {conversation.createdAt.toLocaleString('zh-CN')}
                </div>
                <div>
                  <span className="text-gray-400">消息数量：</span>
                  {formData.messages.length} 条
                </div>
                <div>
                  <span className="text-gray-400">总Tokens：</span>
                  {formData.messages.reduce((sum, msg) => sum + (msg.tokens || 0), 0)}
                </div>
                <div>
                  <span className="text-gray-400">对话ID：</span>
                  {conversation.id}
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
            <Button
              variant="ghost"
              onClick={onClose}
              disabled={isSaving}
              className="text-gray-400 hover:text-white"
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSaving}
              className="bg-blue-500 hover:bg-blue-600"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {mode === 'create' ? '创建对话' : '保存修改'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
