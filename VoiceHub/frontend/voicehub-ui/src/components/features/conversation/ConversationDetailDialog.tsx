import React, { useState } from 'react';
import { 
  MessageSquare, 
  Clock, 
  Tag, 
  Bot,
  User,
  Copy,
  Download,
  Edit3,
  Trash2,
  MoreVertical,
  <PERSON>rkles,
  FileText,
  Share2,
  Star,
  StarOff
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  tokens?: number;
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  model: string;
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  totalTokens: number;
  isStarred: boolean;
  summary?: string;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    complexity: 'low' | 'medium' | 'high';
    satisfaction: number; // 1-5
  };
}

interface ConversationDetailDialogProps {
  open: boolean;
  onClose: () => void;
  conversation: Conversation | null;
  onEdit: (conversation: Conversation) => void;
  onDelete: (conversationId: string) => void;
  onToggleStar: (conversationId: string) => void;
  onExport: (conversation: Conversation, format: 'txt' | 'md' | 'json') => void;
}

export default function ConversationDetailDialog({ 
  open, 
  onClose, 
  conversation, 
  onEdit, 
  onDelete,
  onToggleStar,
  onExport 
}: ConversationDetailDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  if (!conversation) return null;

  // 格式化时长
  const formatDuration = (start: Date, end: Date): string => {
    const diff = Math.floor((end.getTime() - start.getTime()) / 1000);
    const mins = Math.floor(diff / 60);
    const secs = diff % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取情感分析颜色
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'negative':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      case 'neutral':
        return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 获取复杂度颜色
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'high':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      case 'medium':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'low':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!confirm('确定要删除这个对话吗？此操作不可撤销。')) return;
    
    setIsDeleting(true);
    try {
      await onDelete(conversation.id);
      onClose();
    } catch (error) {
      console.error('删除对话失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // 复制内容
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: 添加toast提示
  };

  // 复制整个对话
  const handleCopyConversation = () => {
    const conversationText = conversation.messages
      .map(msg => `${msg.role === 'user' ? '用户' : 'AI助手'}: ${msg.content}`)
      .join('\n\n');
    handleCopy(conversationText);
  };

  // 导出对话
  const handleExport = (format: 'txt' | 'md' | 'json') => {
    onExport(conversation, format);
  };

  // 获取对话时长
  const conversationDuration = conversation.messages.length > 1 
    ? formatDuration(conversation.messages[0].timestamp, conversation.messages[conversation.messages.length - 1].timestamp)
    : '0:00';

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-blue-400" />
              <span>对话详情</span>
            </DialogTitle>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-gray-800 border-gray-700">
                <DropdownMenuItem onClick={() => onEdit(conversation)} className="text-white hover:bg-gray-700">
                  <Edit3 className="w-4 h-4 mr-2" />
                  编辑对话
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onToggleStar(conversation.id)} className="text-white hover:bg-gray-700">
                  {conversation.isStarred ? (
                    <>
                      <StarOff className="w-4 h-4 mr-2" />
                      取消收藏
                    </>
                  ) : (
                    <>
                      <Star className="w-4 h-4 mr-2" />
                      添加收藏
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCopyConversation} className="text-white hover:bg-gray-700">
                  <Copy className="w-4 h-4 mr-2" />
                  复制对话
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('txt')} className="text-white hover:bg-gray-700">
                  <Download className="w-4 h-4 mr-2" />
                  导出为TXT
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('md')} className="text-white hover:bg-gray-700">
                  <Download className="w-4 h-4 mr-2" />
                  导出为Markdown
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('json')} className="text-white hover:bg-gray-700">
                  <Download className="w-4 h-4 mr-2" />
                  导出为JSON
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDelete} className="text-red-400 hover:bg-red-500/20">
                  <Trash2 className="w-4 h-4 mr-2" />
                  删除对话
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {/* 对话基本信息 */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
                  <span>{conversation.title}</span>
                  {conversation.isStarred && <Star className="w-5 h-5 text-yellow-400 fill-current" />}
                </h2>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="border-purple-400/30 text-purple-200">
                    <Tag className="w-3 h-3 mr-1" />
                    {conversation.category}
                  </Badge>
                  <Badge variant="outline" className="border-blue-400/30 text-blue-200">
                    {conversation.model}
                  </Badge>
                </div>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-blue-200">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{conversationDuration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MessageSquare className="w-4 h-4" />
                  <span>{conversation.messages.length} 条消息</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Sparkles className="w-4 h-4" />
                  <span>{conversation.totalTokens} tokens</span>
                </div>
                <div>
                  <span>{conversation.createdAt.toLocaleDateString('zh-CN')}</span>
                </div>
                {conversation.aiAnalysis && (
                  <Badge className={getSentimentColor(conversation.aiAnalysis.sentiment)}>
                    {conversation.aiAnalysis.sentiment === 'positive' ? '积极' : 
                     conversation.aiAnalysis.sentiment === 'negative' ? '消极' : '中性'}
                  </Badge>
                )}
              </div>
            </div>

            <Separator className="bg-white/10" />

            {/* 对话消息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <MessageSquare className="w-5 h-5 mr-2 text-blue-400" />
                对话内容
              </h3>
              
              <div className="space-y-4">
                {conversation.messages.map((message, index) => (
                  <div key={message.id} className="flex space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' 
                        ? 'bg-blue-500/20 text-blue-400' 
                        : 'bg-green-500/20 text-green-400'
                    }`}>
                      {message.role === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-white">
                          {message.role === 'user' ? '用户' : 'AI助手'}
                        </span>
                        <span className="text-xs text-gray-400">
                          {message.timestamp.toLocaleTimeString('zh-CN')}
                        </span>
                        {message.tokens && (
                          <span className="text-xs text-gray-400">
                            {message.tokens} tokens
                          </span>
                        )}
                      </div>
                      
                      <div className={`p-3 rounded-lg ${
                        message.role === 'user' 
                          ? 'bg-blue-500/10 border border-blue-400/20' 
                          : 'bg-green-500/10 border border-green-400/20'
                      }`}>
                        <p className="text-white whitespace-pre-wrap">{message.content}</p>
                        <div className="flex justify-end mt-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopy(message.content)}
                            className="text-gray-400 hover:text-white"
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            复制
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* AI分析摘要 */}
            {conversation.summary && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-purple-400" />
                  对话摘要
                </h3>
                
                <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-4">
                  <p className="text-purple-100 leading-relaxed">
                    {conversation.summary}
                  </p>
                  <div className="flex justify-end mt-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(conversation.summary!)}
                      className="text-purple-400 hover:text-white"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      复制
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 标签 */}
            {conversation.tags && conversation.tags.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {conversation.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-blue-200 border-blue-400/30">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* AI分析详情 */}
            {conversation.aiAnalysis && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-green-400" />
                  AI分析报告
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 对话复杂度 */}
                  <div className="bg-white/5 rounded-lg p-4">
                    <h4 className="text-white font-medium mb-2">对话复杂度</h4>
                    <Badge className={getComplexityColor(conversation.aiAnalysis.complexity)}>
                      {conversation.aiAnalysis.complexity === 'high' ? '高复杂度' : 
                       conversation.aiAnalysis.complexity === 'medium' ? '中复杂度' : '低复杂度'}
                    </Badge>
                  </div>

                  {/* 满意度评分 */}
                  <div className="bg-white/5 rounded-lg p-4">
                    <h4 className="text-white font-medium mb-2">满意度评分</h4>
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`w-4 h-4 ${
                              star <= conversation.aiAnalysis!.satisfaction
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-400'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-blue-200 text-sm">
                        {conversation.aiAnalysis.satisfaction}/5
                      </span>
                    </div>
                  </div>

                  {/* 主要话题 */}
                  {conversation.aiAnalysis.topics && conversation.aiAnalysis.topics.length > 0 && (
                    <div className="bg-white/5 rounded-lg p-4 md:col-span-2">
                      <h4 className="text-white font-medium mb-2">主要话题</h4>
                      <div className="space-y-1">
                        {conversation.aiAnalysis.topics.map((topic, index) => (
                          <div key={index} className="text-blue-200 text-sm">• {topic}</div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 元信息 */}
            <div className="text-xs text-gray-400 space-y-1 pt-4 border-t border-white/10">
              <p>创建时间：{conversation.createdAt.toLocaleString('zh-CN')}</p>
              <p>更新时间：{conversation.updatedAt.toLocaleString('zh-CN')}</p>
              <p>对话ID：{conversation.id}</p>
            </div>
          </div>
        </ScrollArea>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            关闭
          </Button>
          <Button
            onClick={() => onEdit(conversation)}
            className="bg-blue-500 hover:bg-blue-600"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            编辑对话
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
