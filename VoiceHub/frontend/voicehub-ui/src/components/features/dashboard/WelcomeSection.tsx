import React, { useState, useEffect } from 'react';
import { 
  Mic, 
  MessageSquare, 
  FileText, 
  Sparkles,
  Calendar,
  TrendingUp,
  Clock,
  Zap,
  Users,
  Globe
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface WelcomeSectionProps {
  user?: {
    name: string;
    email: string;
  };
  onNavigate: (section: string) => void;
}

export default function WelcomeSection({ user, onNavigate }: WelcomeSectionProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [greeting, setGreeting] = useState('');

  // 更新时间和问候语
  useEffect(() => {
    const updateTimeAndGreeting = () => {
      const now = new Date();
      setCurrentTime(now);
      
      const hour = now.getHours();
      if (hour < 6) {
        setGreeting('深夜好');
      } else if (hour < 12) {
        setGreeting('早上好');
      } else if (hour < 18) {
        setGreeting('下午好');
      } else {
        setGreeting('晚上好');
      }
    };

    updateTimeAndGreeting();
    const timer = setInterval(updateTimeAndGreeting, 60000);
    return () => clearInterval(timer);
  }, []);

  // 快速统计数据
  const quickStats = [
    { label: '今日对话', value: '12', trend: '+3', color: 'text-blue-400', icon: MessageSquare },
    { label: '语音笔记', value: '48', trend: '+8', color: 'text-green-400', icon: FileText },
    { label: 'AI处理任务', value: '24', trend: '+12', color: 'text-purple-400', icon: Sparkles },
    { label: '日程安排', value: '6', trend: '+2', color: 'text-orange-400', icon: Calendar },
  ];

  // 快速操作
  const quickActions = [
    {
      title: '开始AI对话',
      description: '与智能助手进行对话',
      icon: MessageSquare,
      color: 'from-blue-500 to-cyan-500',
      action: () => onNavigate('ai-chat')
    },
    {
      title: '录制语音笔记',
      description: '快速记录想法和会议',
      icon: Mic,
      color: 'from-green-500 to-emerald-500',
      action: () => onNavigate('voice-notes')
    },
    {
      title: '创意AI工具',
      description: '内容分析和格式转换',
      icon: Sparkles,
      color: 'from-purple-500 to-pink-500',
      action: () => onNavigate('creative-ai')
    },
    {
      title: '管理日程',
      description: '语音创建和管理日程',
      icon: Calendar,
      color: 'from-orange-500 to-red-500',
      action: () => onNavigate('schedule')
    }
  ];

  // 最近活动
  const recentActivities = [
    {
      type: 'note',
      title: '项目会议记录',
      time: '2小时前',
      icon: FileText,
      color: 'text-green-400'
    },
    {
      type: 'chat',
      title: 'AI对话：技术咨询',
      time: '4小时前',
      icon: MessageSquare,
      color: 'text-blue-400'
    },
    {
      type: 'ai',
      title: 'AI分析：会议摘要',
      time: '6小时前',
      icon: Sparkles,
      color: 'text-purple-400'
    }
  ];

  return (
    <div className="space-y-6">
      {/* 欢迎横幅 */}
      <Card className="bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 backdrop-blur-xl border-white/20">
        <CardContent className="p-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                {greeting}，{user?.name || '用户'}！
              </h1>
              <p className="text-blue-200 text-lg">
                欢迎使用VoiceHub智能语音助手平台
              </p>
              <p className="text-blue-300 text-sm mt-1">
                {currentTime.toLocaleDateString('zh-CN', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })} • {currentTime.toLocaleTimeString('zh-CN', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
            
            <div className="hidden md:block">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Mic className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-200 text-sm font-medium">{stat.label}</p>
                    <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                  </div>
                  <div className="text-right">
                    <Icon className={`w-6 h-6 ${stat.color} mb-1`} />
                    <p className={`text-sm font-medium ${stat.color}`}>{stat.trend}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 快速操作 */}
      <div>
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
          <Zap className="w-5 h-5 text-yellow-400" />
          <span>快速开始</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Card 
                key={index} 
                className="bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 cursor-pointer group"
                onClick={action.action}
              >
                <CardContent className="p-6 text-center">
                  <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-white font-medium mb-1">{action.title}</h3>
                  <p className="text-blue-200 text-sm">{action.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 最近活动 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 bg-white/10 backdrop-blur-xl border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-400" />
              <span>最近活动</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivities.map((activity, index) => {
                const Icon = activity.icon;
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                    <Icon className={`w-4 h-4 ${activity.color}`} />
                    <div className="flex-1">
                      <p className="text-white text-sm font-medium">{activity.title}</p>
                      <p className="text-blue-200 text-xs">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* 使用提示 */}
        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Globe className="w-5 h-5 text-green-400" />
              <span>使用提示</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-400/20">
                <p className="text-blue-200">
                  💡 <strong>语音输入：</strong>点击麦克风图标开始语音输入，支持中英文识别
                </p>
              </div>
              
              <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-400/20">
                <p className="text-purple-200">
                  🤖 <strong>AI助手：</strong>支持多种大模型，可根据需求切换使用
                </p>
              </div>
              
              <div className="p-3 bg-green-500/10 rounded-lg border border-green-400/20">
                <p className="text-green-200">
                  📝 <strong>智能笔记：</strong>自动转写、分类和AI分析，提升记录效率
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
