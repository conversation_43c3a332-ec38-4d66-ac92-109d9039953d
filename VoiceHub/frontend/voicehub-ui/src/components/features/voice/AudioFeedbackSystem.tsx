import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Volume2, 
  VolumeX, 
  Headphones, 
  Mic, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap
} from 'lucide-react';

interface AudioFeedbackConfig {
  enableVoiceFeedback: boolean;
  enableVisualFeedback: boolean;
  enableHapticFeedback: boolean;
  volumeThreshold: number;
  noiseGateLevel: number;
  feedbackDelay: number;
  compressionRatio: number;
  equalizerSettings: {
    bass: number;
    mid: number;
    treble: number;
  };
}

interface AudioMetrics {
  inputLevel: number;
  outputLevel: number;
  latency: number;
  sampleRate: number;
  bitDepth: number;
  channelCount: number;
  bufferSize: number;
}

interface AudioFeedbackSystemProps {
  audioStream?: MediaStream | null;
  onConfigChange?: (config: AudioFeedbackConfig) => void;
  className?: string;
}

const AudioFeedbackSystem: React.FC<AudioFeedbackSystemProps> = ({
  audioStream,
  onConfigChange,
  className = ''
}) => {
  const [config, setConfig] = useState<AudioFeedbackConfig>({
    enableVoiceFeedback: true,
    enableVisualFeedback: true,
    enableHapticFeedback: false,
    volumeThreshold: 0.1,
    noiseGateLevel: 0.05,
    feedbackDelay: 0,
    compressionRatio: 2.0,
    equalizerSettings: {
      bass: 0,
      mid: 0,
      treble: 0
    }
  });

  const [metrics, setMetrics] = useState<AudioMetrics>({
    inputLevel: 0,
    outputLevel: 0,
    latency: 0,
    sampleRate: 44100,
    bitDepth: 16,
    channelCount: 1,
    bufferSize: 1024
  });

  const [isActive, setIsActive] = useState(false);
  const [feedbackStatus, setFeedbackStatus] = useState<'good' | 'warning' | 'error'>('good');
  const [statusMessage, setStatusMessage] = useState('Audio feedback system ready');

  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);
  const compressorRef = useRef<DynamicsCompressorNode | null>(null);
  const filterNodesRef = useRef<{
    bass: BiquadFilterNode | null;
    mid: BiquadFilterNode | null;
    treble: BiquadFilterNode | null;
  }>({ bass: null, mid: null, treble: null });

  const monitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize audio processing chain
  const initializeAudioProcessing = useCallback(async () => {
    if (!audioStream) return;

    try {
      // Create audio context
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      const audioContext = audioContextRef.current;

      // Create audio nodes
      const source = audioContext.createMediaStreamSource(audioStream);
      analyserRef.current = audioContext.createAnalyser();
      gainNodeRef.current = audioContext.createGain();
      compressorRef.current = audioContext.createDynamicsCompressor();

      // Create EQ filters
      filterNodesRef.current.bass = audioContext.createBiquadFilter();
      filterNodesRef.current.mid = audioContext.createBiquadFilter();
      filterNodesRef.current.treble = audioContext.createBiquadFilter();

      // Configure nodes
      const analyser = analyserRef.current;
      analyser.fftSize = 2048;
      analyser.smoothingTimeConstant = 0.8;

      const compressor = compressorRef.current;
      compressor.threshold.setValueAtTime(-24, audioContext.currentTime);
      compressor.knee.setValueAtTime(30, audioContext.currentTime);
      compressor.ratio.setValueAtTime(config.compressionRatio, audioContext.currentTime);
      compressor.attack.setValueAtTime(0.003, audioContext.currentTime);
      compressor.release.setValueAtTime(0.25, audioContext.currentTime);

      // Configure EQ filters
      const { bass, mid, treble } = filterNodesRef.current;
      if (bass && mid && treble) {
        bass.type = 'lowshelf';
        bass.frequency.setValueAtTime(320, audioContext.currentTime);
        bass.gain.setValueAtTime(config.equalizerSettings.bass, audioContext.currentTime);

        mid.type = 'peaking';
        mid.frequency.setValueAtTime(1000, audioContext.currentTime);
        mid.Q.setValueAtTime(1, audioContext.currentTime);
        mid.gain.setValueAtTime(config.equalizerSettings.mid, audioContext.currentTime);

        treble.type = 'highshelf';
        treble.frequency.setValueAtTime(3200, audioContext.currentTime);
        treble.gain.setValueAtTime(config.equalizerSettings.treble, audioContext.currentTime);
      }

      // Connect audio chain
      source.connect(analyser);
      analyser.connect(gainNodeRef.current);
      gainNodeRef.current.connect(compressor);
      
      if (bass && mid && treble) {
        compressor.connect(bass);
        bass.connect(mid);
        mid.connect(treble);
        treble.connect(audioContext.destination);
      }

      // Update metrics
      setMetrics(prev => ({
        ...prev,
        sampleRate: audioContext.sampleRate,
        channelCount: audioStream.getAudioTracks()[0]?.getSettings().channelCount || 1
      }));

      setIsActive(true);
      startMonitoring();
      setStatusMessage('Audio processing initialized successfully');
      setFeedbackStatus('good');

    } catch (error) {
      console.error('Failed to initialize audio processing:', error);
      setStatusMessage('Failed to initialize audio processing');
      setFeedbackStatus('error');
    }
  }, [audioStream, config.compressionRatio, config.equalizerSettings]);

  // Start audio monitoring
  const startMonitoring = useCallback(() => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    monitoringIntervalRef.current = setInterval(() => {
      analyser.getByteFrequencyData(dataArray);
      
      // Calculate input level
      const inputLevel = dataArray.reduce((sum, value) => sum + value, 0) / (dataArray.length * 255);
      
      // Calculate latency (simplified estimation)
      const latency = audioContextRef.current ? 
        (audioContextRef.current.baseLatency + audioContextRef.current.outputLatency) * 1000 : 0;

      setMetrics(prev => ({
        ...prev,
        inputLevel,
        outputLevel: inputLevel * (gainNodeRef.current?.gain.value || 1),
        latency
      }));

      // Check for feedback issues
      checkFeedbackStatus(inputLevel, latency);

    }, 100);
  }, []);

  // Check for audio feedback issues
  const checkFeedbackStatus = (inputLevel: number, latency: number) => {
    if (latency > 50) {
      setFeedbackStatus('warning');
      setStatusMessage(`High latency detected: ${latency.toFixed(1)}ms`);
    } else if (inputLevel > 0.9) {
      setFeedbackStatus('warning');
      setStatusMessage('Input level too high - risk of clipping');
    } else if (inputLevel < 0.01) {
      setFeedbackStatus('warning');
      setStatusMessage('Input level too low - check microphone');
    } else {
      setFeedbackStatus('good');
      setStatusMessage('Audio feedback system operating normally');
    }
  };

  // Update configuration
  const updateConfig = (newConfig: Partial<AudioFeedbackConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
    
    if (onConfigChange) {
      onConfigChange(updatedConfig);
    }

    // Apply real-time changes
    applyConfigChanges(updatedConfig);
  };

  // Apply configuration changes to audio nodes
  const applyConfigChanges = (newConfig: AudioFeedbackConfig) => {
    if (!audioContextRef.current) return;

    const audioContext = audioContextRef.current;

    // Update gain
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.setValueAtTime(
        newConfig.volumeThreshold, 
        audioContext.currentTime
      );
    }

    // Update compressor
    if (compressorRef.current) {
      compressorRef.current.ratio.setValueAtTime(
        newConfig.compressionRatio, 
        audioContext.currentTime
      );
    }

    // Update EQ
    const { bass, mid, treble } = filterNodesRef.current;
    if (bass && mid && treble) {
      bass.gain.setValueAtTime(
        newConfig.equalizerSettings.bass, 
        audioContext.currentTime
      );
      mid.gain.setValueAtTime(
        newConfig.equalizerSettings.mid, 
        audioContext.currentTime
      );
      treble.gain.setValueAtTime(
        newConfig.equalizerSettings.treble, 
        audioContext.currentTime
      );
    }
  };

  // Cleanup
  useEffect(() => {
    return () => {
      if (monitoringIntervalRef.current) {
        clearInterval(monitoringIntervalRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Initialize when audio stream is available
  useEffect(() => {
    if (audioStream) {
      initializeAudioProcessing();
    } else {
      setIsActive(false);
      if (monitoringIntervalRef.current) {
        clearInterval(monitoringIntervalRef.current);
      }
    }
  }, [audioStream, initializeAudioProcessing]);

  const getStatusIcon = () => {
    switch (feedbackStatus) {
      case 'good':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-400" />;
    }
  };

  const getStatusColor = () => {
    switch (feedbackStatus) {
      case 'good':
        return 'border-green-400/30 bg-green-500/10';
      case 'warning':
        return 'border-yellow-400/30 bg-yellow-500/10';
      case 'error':
        return 'border-red-400/30 bg-red-500/10';
    }
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center">
              <Zap className="w-4 h-4 text-white" />
            </div>
            <span>Audio Feedback System</span>
          </div>
          <Badge variant="secondary" className={`${getStatusColor()} text-white border`}>
            {getStatusIcon()}
            <span className="ml-1 capitalize">{feedbackStatus}</span>
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Status Message */}
        <div className={`p-3 rounded-lg border ${getStatusColor()}`}>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <p className="text-white text-sm">{statusMessage}</p>
          </div>
        </div>

        {/* Audio Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/5 rounded-lg p-3 text-center">
            <p className="text-blue-200 text-xs mb-1">Input Level</p>
            <p className="text-white font-mono text-lg">
              {(metrics.inputLevel * 100).toFixed(0)}%
            </p>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-200"
                style={{ width: `${metrics.inputLevel * 100}%` }}
              />
            </div>
          </div>

          <div className="bg-white/5 rounded-lg p-3 text-center">
            <p className="text-blue-200 text-xs mb-1">Output Level</p>
            <p className="text-white font-mono text-lg">
              {(metrics.outputLevel * 100).toFixed(0)}%
            </p>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div 
                className="bg-cyan-500 h-2 rounded-full transition-all duration-200"
                style={{ width: `${metrics.outputLevel * 100}%` }}
              />
            </div>
          </div>

          <div className="bg-white/5 rounded-lg p-3 text-center">
            <p className="text-blue-200 text-xs mb-1">Latency</p>
            <p className="text-white font-mono text-lg">
              {metrics.latency.toFixed(1)}ms
            </p>
            <p className={`text-xs mt-1 ${
              metrics.latency < 20 ? 'text-green-400' : 
              metrics.latency < 50 ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {metrics.latency < 20 ? 'Excellent' : 
               metrics.latency < 50 ? 'Good' : 'High'}
            </p>
          </div>

          <div className="bg-white/5 rounded-lg p-3 text-center">
            <p className="text-blue-200 text-xs mb-1">Sample Rate</p>
            <p className="text-white font-mono text-lg">
              {(metrics.sampleRate / 1000).toFixed(1)}kHz
            </p>
            <p className="text-blue-300 text-xs mt-1">
              {metrics.channelCount} Channel{metrics.channelCount > 1 ? 's' : ''}
            </p>
          </div>
        </div>

        {/* Feedback Controls */}
        <div className="space-y-4">
          <h4 className="text-white font-medium">Feedback Settings</h4>
          
          {/* Toggle Controls */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-blue-300" />
                <span className="text-white text-sm">Voice Feedback</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateConfig({ enableVoiceFeedback: !config.enableVoiceFeedback })}
                className={`${config.enableVoiceFeedback ? 'text-green-400' : 'text-gray-400'}`}
              >
                {config.enableVoiceFeedback ? 'ON' : 'OFF'}
              </Button>
            </div>

            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-2">
                <Info className="w-4 h-4 text-blue-300" />
                <span className="text-white text-sm">Visual Feedback</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateConfig({ enableVisualFeedback: !config.enableVisualFeedback })}
                className={`${config.enableVisualFeedback ? 'text-green-400' : 'text-gray-400'}`}
              >
                {config.enableVisualFeedback ? 'ON' : 'OFF'}
              </Button>
            </div>

            <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-300" />
                <span className="text-white text-sm">Haptic Feedback</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateConfig({ enableHapticFeedback: !config.enableHapticFeedback })}
                className={`${config.enableHapticFeedback ? 'text-green-400' : 'text-gray-400'}`}
              >
                {config.enableHapticFeedback ? 'ON' : 'OFF'}
              </Button>
            </div>
          </div>

          {/* Slider Controls */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-white text-sm">Volume Threshold</label>
                <span className="text-blue-300 text-sm">{(config.volumeThreshold * 100).toFixed(0)}%</span>
              </div>
              <Slider
                value={[config.volumeThreshold * 100]}
                onValueChange={(value) => updateConfig({ volumeThreshold: value[0] / 100 })}
                max={100}
                step={1}
                className="w-full"
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-white text-sm">Noise Gate Level</label>
                <span className="text-blue-300 text-sm">{(config.noiseGateLevel * 100).toFixed(0)}%</span>
              </div>
              <Slider
                value={[config.noiseGateLevel * 100]}
                onValueChange={(value) => updateConfig({ noiseGateLevel: value[0] / 100 })}
                max={50}
                step={1}
                className="w-full"
              />
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-white text-sm">Compression Ratio</label>
                <span className="text-blue-300 text-sm">{config.compressionRatio.toFixed(1)}:1</span>
              </div>
              <Slider
                value={[config.compressionRatio]}
                onValueChange={(value) => updateConfig({ compressionRatio: value[0] })}
                min={1}
                max={10}
                step={0.1}
                className="w-full"
              />
            </div>
          </div>

          {/* Equalizer */}
          <div className="space-y-3">
            <h5 className="text-white text-sm font-medium">Equalizer</h5>
            
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-blue-200 text-xs">Bass</label>
                  <span className="text-blue-300 text-xs">{config.equalizerSettings.bass > 0 ? '+' : ''}{config.equalizerSettings.bass}dB</span>
                </div>
                <Slider
                  value={[config.equalizerSettings.bass]}
                  onValueChange={(value) => updateConfig({ 
                    equalizerSettings: { ...config.equalizerSettings, bass: value[0] }
                  })}
                  min={-12}
                  max={12}
                  step={1}
                  className="w-full"
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-blue-200 text-xs">Mid</label>
                  <span className="text-blue-300 text-xs">{config.equalizerSettings.mid > 0 ? '+' : ''}{config.equalizerSettings.mid}dB</span>
                </div>
                <Slider
                  value={[config.equalizerSettings.mid]}
                  onValueChange={(value) => updateConfig({ 
                    equalizerSettings: { ...config.equalizerSettings, mid: value[0] }
                  })}
                  min={-12}
                  max={12}
                  step={1}
                  className="w-full"
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-blue-200 text-xs">Treble</label>
                  <span className="text-blue-300 text-xs">{config.equalizerSettings.treble > 0 ? '+' : ''}{config.equalizerSettings.treble}dB</span>
                </div>
                <Slider
                  value={[config.equalizerSettings.treble]}
                  onValueChange={(value) => updateConfig({ 
                    equalizerSettings: { ...config.equalizerSettings, treble: value[0] }
                  })}
                  min={-12}
                  max={12}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-white/10">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updateConfig({
                volumeThreshold: 0.1,
                noiseGateLevel: 0.05,
                compressionRatio: 2.0,
                equalizerSettings: { bass: 0, mid: 0, treble: 0 }
              })}
              className="text-blue-300 hover:text-white"
            >
              Reset to Default
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-300 hover:text-white"
            >
              <Settings className="w-4 h-4 mr-1" />
              Advanced
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AudioFeedbackSystem;