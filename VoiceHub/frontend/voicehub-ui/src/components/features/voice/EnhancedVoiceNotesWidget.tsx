import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>c<PERSON><PERSON>,
  Play,
  Pause,
  Square,
  FileText,
  Search,
  Filter,
  Download,
  Trash2,
  Edit3,
  <PERSON>rkles,
  Clock,
  Tag,
  Volume2,
  MoreVertical,
  Eye,
  Plus
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import VoiceNoteDetailDialog from './VoiceNoteDetailDialog';
import VoiceNoteEditDialog from './VoiceNoteEditDialog';

// 语音笔记类型定义
interface VoiceNote {
  id: string;
  title: string;
  content: string;
  transcription: string;
  summary: string;
  keywords: string[];
  category: string;
  duration: number;
  audioUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    actionItems: string[];
    importance: 'low' | 'medium' | 'high';
  };
}

// AI分析结果
interface AIAnalysis {
  summary: string;
  keywords: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  actionItems: string[];
  topics: string[];
}

interface EnhancedVoiceNotesWidgetProps {
  expanded?: boolean;
  className?: string;
}

export default function EnhancedVoiceNotesWidget({ expanded = false, className = '' }: EnhancedVoiceNotesWidgetProps) {
  const [notes, setNotes] = useState<VoiceNote[]>([]);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedNote, setSelectedNote] = useState<VoiceNote | null>(null);
  const [editMode, setEditMode] = useState<'create' | 'edit'>('create');

  // 初始化Mock数据
  useEffect(() => {
    const mockNotes: VoiceNote[] = [
      {
        id: '1',
        title: '项目会议记录',
        content: '今天讨论了VoiceHub项目的前端优化方案，重点关注用户体验提升和成本控制。',
        transcription: '今天讨论了VoiceHub项目的前端优化方案，主要包括UI改进、功能精简、成本控制等方面。团队一致认为需要专注核心功能，提升用户体验。',
        summary: '讨论VoiceHub前端优化，包括UI改进和功能精简，重点关注用户体验和成本控制。',
        keywords: ['前端优化', 'UI改进', '成本控制', '用户体验'],
        category: 'meeting',
        duration: 180,
        createdAt: new Date(Date.now() - 86400000),
        updatedAt: new Date(Date.now() - 86400000 + 3600000),
        aiAnalysis: {
          sentiment: 'positive',
          topics: ['项目管理', '前端开发', '用户体验'],
          actionItems: ['完成UI设计稿', '优化核心功能', '制定成本控制方案'],
          importance: 'high'
        }
      },
      {
        id: '2',
        title: '学习笔记 - React Hooks',
        content: 'useState和useEffect的使用技巧，以及自定义Hook的最佳实践。',
        transcription: 'useState和useEffect是React中最常用的Hooks，需要注意依赖数组的使用。自定义Hook可以很好地复用逻辑。',
        summary: 'React Hooks学习总结，包括useState、useEffect使用技巧和自定义Hook实践。',
        keywords: ['React', 'Hooks', 'useState', 'useEffect', '自定义Hook'],
        category: 'learning',
        duration: 120,
        createdAt: new Date(Date.now() - 172800000),
        updatedAt: new Date(Date.now() - 172800000 + 1800000),
        aiAnalysis: {
          sentiment: 'neutral',
          topics: ['前端开发', '技术学习', 'React框架'],
          actionItems: ['练习自定义Hook', '完成相关项目', '整理学习笔记'],
          importance: 'medium'
        }
      }
    ];

    setNotes(mockNotes);
  }, []);

  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<AIAnalysis | null>(null);
  
  const timerRef = useRef<NodeJS.Timeout>();

  // 录音计时器
  useEffect(() => {
    if (isRecording) {
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      setRecordingTime(0);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRecording]);

  // 开始录音
  const handleStartRecording = () => {
    setIsRecording(true);
    setRecordingTime(0);
  };

  // 停止录音并保存
  const handleStopRecording = () => {
    setIsRecording(false);
    
    // 模拟保存新笔记
    const newNote: VoiceNote = {
      id: Date.now().toString(),
      title: `语音笔记 ${new Date().toLocaleString()}`,
      content: '这是新录制的语音笔记内容...',
      transcription: '这是新录制的语音笔记的转写文本，展示了语音识别的效果。',
      duration: recordingTime,
      createdAt: new Date(),
      tags: ['新录制'],
      category: 'personal'
    };
    
    setNotes(prev => [newNote, ...prev]);
    setRecordingTime(0);
  };

  // 格式化时间
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 过滤笔记
  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.transcription.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || note.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // AI分析笔记
  const handleAnalyzeNote = async (note: VoiceNote) => {
    setIsAnalyzing(true);
    setSelectedNote(note);
    
    // 模拟AI分析
    setTimeout(() => {
      const mockAnalysis: AIAnalysis = {
        summary: `这是对"${note.title}"的AI分析摘要，提取了主要内容和关键信息。`,
        keywords: ['关键词1', '关键词2', '关键词3'],
        sentiment: 'positive',
        actionItems: ['行动项1：完成前端优化', '行动项2：测试新功能'],
        topics: ['技术讨论', '项目规划', '功能设计']
      };
      
      setCurrentAnalysis(mockAnalysis);
      setIsAnalyzing(false);
    }, 2000);
  };

  // 新的事件处理函数
  const handleViewNote = (note: VoiceNote) => {
    setSelectedNote(note);
    setShowDetailDialog(true);
  };

  const handleEditNote = (note: VoiceNote) => {
    setSelectedNote(note);
    setEditMode('edit');
    setShowEditDialog(true);
  };

  const handleCreateNote = () => {
    setSelectedNote(null);
    setEditMode('create');
    setShowEditDialog(true);
  };

  const handleSaveNote = (noteData: VoiceNote) => {
    if (editMode === 'create') {
      const newNote: VoiceNote = {
        ...noteData,
        id: Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setNotes(prev => [newNote, ...prev]);
    } else if (selectedNote) {
      setNotes(prev => prev.map(note =>
        note.id === selectedNote.id
          ? { ...noteData, id: selectedNote.id, createdAt: selectedNote.createdAt, updatedAt: new Date() }
          : note
      ));
    }
    setShowEditDialog(false);
  };

  const handleDeleteNote = (noteId: string) => {
    setNotes(prev => prev.filter(note => note.id !== noteId));
  };

  const handleExportNote = (note: VoiceNote, format: 'txt' | 'md' | 'pdf') => {
    // 模拟导出功能
    const content = `标题: ${note.title}\n内容: ${note.content}\n转写: ${note.transcription}`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${note.title}.${format}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    const colors = {
      meeting: 'bg-blue-500/20 text-blue-200 border-blue-400/30',
      personal: 'bg-green-500/20 text-green-200 border-green-400/30',
      study: 'bg-purple-500/20 text-purple-200 border-purple-400/30',
      other: 'bg-gray-500/20 text-gray-200 border-gray-400/30'
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  return (
    <>
      <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
        expanded ? 'col-span-full h-[700px]' : 'h-[500px]'
      } ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
              <FileText className="w-4 h-4 text-white" />
            </div>
            <span>智能语音笔记</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-green-500/20 text-green-200 border-green-400/30">
              {notes.length} 条笔记
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col h-full p-0">
        <Tabs defaultValue="record" className="flex-1 flex flex-col">
          <TabsList className="mx-6 mb-4 bg-white/10 border border-white/20">
            <TabsTrigger value="record" className="data-[state=active]:bg-white/20">
              录制笔记
            </TabsTrigger>
            <TabsTrigger value="manage" className="data-[state=active]:bg-white/20">
              管理笔记
            </TabsTrigger>
          </TabsList>

          {/* 录制笔记页面 */}
          <TabsContent value="record" className="flex-1 px-6">
            <div className="space-y-6">
              {/* 录音控制 */}
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center">
                  {!isRecording ? (
                    <Button
                      onClick={handleStartRecording}
                      className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Mic className="w-8 h-8" />
                    </Button>
                  ) : (
                    <Button
                      onClick={handleStopRecording}
                      className="w-20 h-20 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
                    >
                      <Square className="w-8 h-8" />
                    </Button>
                  )}
                </div>
                
                <div className="text-center">
                  <p className="text-white text-lg font-medium">
                    {isRecording ? `录音中... ${formatDuration(recordingTime)}` : '点击开始录制语音笔记'}
                  </p>
                  <p className="text-blue-200 text-sm">
                    {isRecording ? '再次点击停止录音' : '支持自动转写和AI分析'}
                  </p>
                </div>
              </div>

              {/* 录音波形可视化占位 */}
              {isRecording && (
                <div className="h-16 bg-white/5 rounded-lg border border-white/10 flex items-center justify-center">
                  <div className="flex items-center space-x-1">
                    {[...Array(20)].map((_, i) => (
                      <div
                        key={i}
                        className="w-1 bg-gradient-to-t from-green-500 to-emerald-500 rounded-full animate-pulse"
                        style={{
                          height: `${Math.random() * 40 + 10}px`,
                          animationDelay: `${i * 0.1}s`
                        }}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* 手动新增笔记 */}
              <div className="text-center pt-6 border-t border-white/10">
                <p className="text-blue-200 text-sm mb-3">或者</p>
                <Button
                  onClick={handleCreateNote}
                  variant="outline"
                  className="border-blue-400/30 text-blue-200 hover:bg-blue-500/20"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  手动创建笔记
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* 管理笔记页面 */}
          <TabsContent value="manage" className="flex-1 flex flex-col px-6">
            {/* 搜索和过滤 */}
            <div className="flex items-center space-x-2 mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-200" />
                <Input
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索笔记内容..."
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-blue-200"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white text-sm"
              >
                <option value="all">全部分类</option>
                <option value="meeting">会议</option>
                <option value="personal">个人</option>
                <option value="study">学习</option>
                <option value="other">其他</option>
              </select>
            </div>

            {/* 笔记列表 */}
            <ScrollArea className="flex-1">
              <div className="space-y-3">
                {filteredNotes.map((note) => (
                  <Card
                    key={note.id}
                    className="bg-white/5 border-white/10 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                    onClick={() => handleViewNote(note)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="text-white font-medium">{note.title}</h4>
                            <Badge className={getCategoryColor(note.category)}>
                              {note.category}
                            </Badge>
                          </div>

                          <p className="text-blue-200 text-sm mb-2 line-clamp-2">
                            {note.transcription}
                          </p>

                          <div className="flex items-center space-x-4 text-xs text-blue-300">
                            <span className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{formatDuration(note.duration)}</span>
                            </span>
                            <span>{note.createdAt.toLocaleDateString()}</span>
                            <div className="flex items-center space-x-1">
                              {note.keywords.map((keyword, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 ml-4">
                          <Button variant="ghost" size="sm" className="text-blue-200 hover:bg-white/10">
                            <Play className="w-3 h-3" />
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-gray-400 hover:text-white"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreVertical className="w-3 h-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-gray-800 border-gray-700">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewNote(note);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Eye className="w-4 h-4 mr-2" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditNote(note);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Edit3 className="w-4 h-4 mr-2" />
                                编辑笔记
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleAnalyzeNote(note);
                                }}
                                className="text-white hover:bg-gray-700"
                              >
                                <Sparkles className="w-4 h-4 mr-2" />
                                AI分析
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteNote(note.id);
                                }}
                                className="text-red-400 hover:bg-red-500/20"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                删除笔记
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {filteredNotes.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-blue-300 mx-auto mb-4 opacity-50" />
                    <p className="text-blue-200">
                      {searchQuery ? '没有找到匹配的笔记' : '还没有语音笔记，开始录制第一条吧！'}
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
      </Card>

      {/* 对话框组件 */}
      <VoiceNoteDetailDialog
        open={showDetailDialog}
        onClose={() => setShowDetailDialog(false)}
        note={selectedNote}
        onEdit={handleEditNote}
        onDelete={handleDeleteNote}
        onExport={handleExportNote}
      />

      <VoiceNoteEditDialog
        open={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        onSave={handleSaveNote}
        note={selectedNote}
        mode={editMode}
      />
    </>
  );
}
