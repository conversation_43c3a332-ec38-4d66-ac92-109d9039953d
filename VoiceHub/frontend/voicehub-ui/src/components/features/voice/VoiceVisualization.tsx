import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mic, MicOff, Volume2, VolumeX, Settings, Pause, Play } from 'lucide-react';

interface AudioData {
  frequency: Uint8Array;
  timeDomain: Uint8Array;
  volume: number;
  pitch: number;
  isVoiceDetected: boolean;
  noiseLevel: number;
}

interface VoiceVisualizationProps {
  isRecording?: boolean;
  audioStream?: MediaStream | null;
  onAudioData?: (data: AudioData) => void;
  visualizationType?: 'waveform' | 'frequency' | 'circular' | 'bars';
  className?: string;
  showControls?: boolean;
  realTimeAnalysis?: boolean;
}

const VoiceVisualization: React.FC<VoiceVisualizationProps> = ({
  isRecording = false,
  audioStream = null,
  onAudioData,
  visualizationType = 'waveform',
  className = '',
  showControls = true,
  realTimeAnalysis = true
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  
  const [isActive, setIsActive] = useState(false);
  const [currentVolume, setCurrentVolume] = useState(0);
  const [voiceDetected, setVoiceDetected] = useState(false);
  const [audioData, setAudioData] = useState<AudioData | null>(null);
  const [isPaused, setIsPaused] = useState(false);

  // Initialize audio context and analyser
  const initializeAudioAnalysis = useCallback(async () => {
    if (!audioStream || !realTimeAnalysis) return;

    try {
      // Create audio context
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      const audioContext = audioContextRef.current;

      // Create analyser node
      analyserRef.current = audioContext.createAnalyser();
      const analyser = analyserRef.current;
      
      // Configure analyser
      analyser.fftSize = 2048;
      analyser.smoothingTimeConstant = 0.8;
      analyser.minDecibels = -90;
      analyser.maxDecibels = -10;

      // Create source from stream
      sourceRef.current = audioContext.createMediaStreamSource(audioStream);
      sourceRef.current.connect(analyser);

      setIsActive(true);
      startVisualization();

    } catch (error) {
      console.error('Failed to initialize audio analysis:', error);
    }
  }, [audioStream, realTimeAnalysis]);

  // Start visualization loop
  const startVisualization = useCallback(() => {
    if (!analyserRef.current || !canvasRef.current || isPaused) return;

    const analyser = analyserRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const bufferLength = analyser.frequencyBinCount;
    const frequencyData = new Uint8Array(bufferLength);
    const timeDomainData = new Uint8Array(bufferLength);

    const draw = () => {
      if (!analyser || isPaused) return;

      // Get audio data
      analyser.getByteFrequencyData(frequencyData);
      analyser.getByteTimeDomainData(timeDomainData);

      // Calculate volume and voice detection
      const volume = calculateVolume(timeDomainData);
      const pitch = calculatePitch(frequencyData);
      const isVoiceDetected = detectVoice(frequencyData, volume);
      const noiseLevel = calculateNoiseLevel(frequencyData);

      const currentAudioData: AudioData = {
        frequency: frequencyData,
        timeDomain: timeDomainData,
        volume,
        pitch,
        isVoiceDetected,
        noiseLevel
      };

      setCurrentVolume(volume);
      setVoiceDetected(isVoiceDetected);
      setAudioData(currentAudioData);

      // Call callback with audio data
      if (onAudioData) {
        onAudioData(currentAudioData);
      }

      // Draw visualization
      drawVisualization(ctx, canvas, currentAudioData);

      animationFrameRef.current = requestAnimationFrame(draw);
    };

    draw();
  }, [onAudioData, isPaused]);

  // Calculate volume from time domain data
  const calculateVolume = (timeDomainData: Uint8Array): number => {
    let sum = 0;
    for (let i = 0; i < timeDomainData.length; i++) {
      const sample = (timeDomainData[i] - 128) / 128;
      sum += sample * sample;
    }
    return Math.sqrt(sum / timeDomainData.length);
  };

  // Calculate dominant pitch from frequency data
  const calculatePitch = (frequencyData: Uint8Array): number => {
    let maxIndex = 0;
    let maxValue = 0;
    
    for (let i = 0; i < frequencyData.length; i++) {
      if (frequencyData[i] > maxValue) {
        maxValue = frequencyData[i];
        maxIndex = i;
      }
    }
    
    // Convert bin index to frequency (assuming 44.1kHz sample rate)
    return (maxIndex * 44100) / (2 * frequencyData.length);
  };

  // Detect voice activity
  const detectVoice = (frequencyData: Uint8Array, volume: number): boolean => {
    // Voice detection based on volume threshold and frequency distribution
    const volumeThreshold = 0.01;
    const voiceFreqRange = frequencyData.slice(10, 100); // Typical voice frequency range
    const voiceEnergy = voiceFreqRange.reduce((sum, val) => sum + val, 0) / voiceFreqRange.length;
    
    return volume > volumeThreshold && voiceEnergy > 30;
  };

  // Calculate background noise level
  const calculateNoiseLevel = (frequencyData: Uint8Array): number => {
    const lowFreqRange = frequencyData.slice(0, 10);
    return lowFreqRange.reduce((sum, val) => sum + val, 0) / lowFreqRange.length;
  };

  // Draw visualization based on type
  const drawVisualization = (ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement, data: AudioData) => {
    const { width, height } = canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Set colors based on voice detection
    const primaryColor = data.isVoiceDetected ? '#3b82f6' : '#6b7280';
    const secondaryColor = data.isVoiceDetected ? '#60a5fa' : '#9ca3af';
    const accentColor = '#06b6d4';

    switch (visualizationType) {
      case 'waveform':
        drawWaveform(ctx, width, height, data.timeDomain, primaryColor, secondaryColor);
        break;
      case 'frequency':
        drawFrequencyBars(ctx, width, height, data.frequency, primaryColor, secondaryColor);
        break;
      case 'circular':
        drawCircularVisualization(ctx, width, height, data, primaryColor, accentColor);
        break;
      case 'bars':
        drawVerticalBars(ctx, width, height, data.frequency, primaryColor, secondaryColor);
        break;
    }

    // Draw volume indicator
    drawVolumeIndicator(ctx, width, height, data.volume, accentColor);
  };

  // Draw waveform visualization
  const drawWaveform = (ctx: CanvasRenderingContext2D, width: number, height: number, 
                       timeDomainData: Uint8Array, primaryColor: string, secondaryColor: string) => {
    ctx.lineWidth = 2;
    ctx.strokeStyle = primaryColor;
    ctx.beginPath();

    const sliceWidth = width / timeDomainData.length;
    let x = 0;

    for (let i = 0; i < timeDomainData.length; i++) {
      const v = timeDomainData[i] / 128.0;
      const y = (v * height) / 2;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }

      x += sliceWidth;
    }

    ctx.stroke();

    // Add glow effect
    ctx.shadowColor = secondaryColor;
    ctx.shadowBlur = 10;
    ctx.stroke();
    ctx.shadowBlur = 0;
  };

  // Draw frequency bars
  const drawFrequencyBars = (ctx: CanvasRenderingContext2D, width: number, height: number,
                            frequencyData: Uint8Array, primaryColor: string, secondaryColor: string) => {
    const barWidth = width / frequencyData.length;
    
    for (let i = 0; i < frequencyData.length; i++) {
      const barHeight = (frequencyData[i] / 255) * height;
      const x = i * barWidth;
      const y = height - barHeight;
      
      // Create gradient
      const gradient = ctx.createLinearGradient(0, y, 0, height);
      gradient.addColorStop(0, primaryColor);
      gradient.addColorStop(1, secondaryColor);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(x, y, barWidth - 1, barHeight);
    }
  };

  // Draw circular visualization
  const drawCircularVisualization = (ctx: CanvasRenderingContext2D, width: number, height: number,
                                   data: AudioData, primaryColor: string, accentColor: string) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 4;
    
    // Draw base circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = primaryColor;
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw frequency spikes
    const angleStep = (2 * Math.PI) / data.frequency.length;
    
    for (let i = 0; i < data.frequency.length; i++) {
      const angle = i * angleStep;
      const amplitude = (data.frequency[i] / 255) * radius * 0.5;
      
      const x1 = centerX + Math.cos(angle) * radius;
      const y1 = centerY + Math.sin(angle) * radius;
      const x2 = centerX + Math.cos(angle) * (radius + amplitude);
      const y2 = centerY + Math.sin(angle) * (radius + amplitude);
      
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.strokeStyle = data.isVoiceDetected ? accentColor : primaryColor;
      ctx.lineWidth = 1;
      ctx.stroke();
    }
    
    // Draw center indicator
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * data.volume * 2, 0, 2 * Math.PI);
    ctx.fillStyle = data.isVoiceDetected ? accentColor + '40' : primaryColor + '20';
    ctx.fill();
  };

  // Draw vertical bars
  const drawVerticalBars = (ctx: CanvasRenderingContext2D, width: number, height: number,
                           frequencyData: Uint8Array, primaryColor: string, secondaryColor: string) => {
    const barCount = Math.min(frequencyData.length, 64);
    const barWidth = width / barCount;
    
    for (let i = 0; i < barCount; i++) {
      const barHeight = (frequencyData[i] / 255) * height * 0.8;
      const x = i * barWidth;
      const y = height - barHeight;
      
      // Create gradient
      const gradient = ctx.createLinearGradient(0, height, 0, y);
      gradient.addColorStop(0, secondaryColor);
      gradient.addColorStop(1, primaryColor);
      
      ctx.fillStyle = gradient;
      ctx.fillRect(x, y, barWidth - 2, barHeight);
      
      // Add glow effect for active bars
      if (barHeight > height * 0.1) {
        ctx.shadowColor = primaryColor;
        ctx.shadowBlur = 5;
        ctx.fillRect(x, y, barWidth - 2, barHeight);
        ctx.shadowBlur = 0;
      }
    }
  };

  // Draw volume indicator
  const drawVolumeIndicator = (ctx: CanvasRenderingContext2D, width: number, height: number,
                              volume: number, color: string) => {
    const indicatorWidth = 4;
    const indicatorHeight = height * volume;
    const x = width - indicatorWidth - 5;
    const y = height - indicatorHeight;
    
    ctx.fillStyle = color;
    ctx.fillRect(x, y, indicatorWidth, indicatorHeight);
    
    // Add volume level markers
    for (let i = 0; i < 5; i++) {
      const markerY = height - (height / 5) * (i + 1);
      ctx.fillStyle = volume > (i + 1) / 5 ? color : color + '40';
      ctx.fillRect(x - 8, markerY - 1, 6, 2);
    }
  };

  // Initialize when recording starts
  useEffect(() => {
    if (isRecording && audioStream) {
      initializeAudioAnalysis();
    } else {
      setIsActive(false);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [isRecording, audioStream, initializeAudioAnalysis]);

  // Handle pause/resume
  const togglePause = () => {
    setIsPaused(!isPaused);
    if (!isPaused) {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    } else {
      startVisualization();
    }
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 ${className}`}>
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              isActive && voiceDetected ? 'bg-green-500 animate-pulse' : 
              isActive ? 'bg-blue-500' : 'bg-gray-500'
            }`} />
            <span className="text-white text-sm font-medium">
              {isActive ? (voiceDetected ? 'Voice Detected' : 'Listening') : 'Inactive'}
            </span>
          </div>
          
          {showControls && (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={togglePause}
                className="text-blue-200 hover:text-white"
                disabled={!isActive}
              >
                {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-200 hover:text-white"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Visualization Canvas */}
        <div className="relative bg-black/20 rounded-lg overflow-hidden">
          <canvas
            ref={canvasRef}
            width={400}
            height={200}
            className="w-full h-48 block"
          />
          
          {!isActive && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <Volume2 className="w-12 h-12 text-blue-400 mx-auto mb-2 opacity-50" />
                <p className="text-blue-200 text-sm">
                  {isRecording ? 'Initializing audio analysis...' : 'Start recording to see visualization'}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Audio Metrics */}
        {isActive && audioData && (
          <div className="grid grid-cols-4 gap-4 mt-4 text-center">
            <div>
              <p className="text-blue-200 text-xs">Volume</p>
              <p className="text-white font-mono text-sm">
                {(currentVolume * 100).toFixed(0)}%
              </p>
            </div>
            <div>
              <p className="text-blue-200 text-xs">Pitch</p>
              <p className="text-white font-mono text-sm">
                {audioData.pitch.toFixed(0)}Hz
              </p>
            </div>
            <div>
              <p className="text-blue-200 text-xs">Noise</p>
              <p className="text-white font-mono text-sm">
                {audioData.noiseLevel.toFixed(0)}dB
              </p>
            </div>
            <div>
              <p className="text-blue-200 text-xs">Status</p>
              <p className={`font-mono text-sm ${
                voiceDetected ? 'text-green-400' : 'text-gray-400'
              }`}>
                {voiceDetected ? 'VOICE' : 'QUIET'}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VoiceVisualization;