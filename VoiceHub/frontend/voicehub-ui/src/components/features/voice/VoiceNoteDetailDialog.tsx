import React, { useState } from 'react';
import { 
  FileText, 
  Clock, 
  Tag, 
  Mic,
  Play,
  Pause,
  Download,
  Edit3,
  Trash2,
  Copy,
  Share2,
  MoreVertical,
  Volume2,
  FileDown,
  Sparkles
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface VoiceNote {
  id: string;
  title: string;
  content: string;
  transcription: string;
  summary: string;
  keywords: string[];
  category: string;
  duration: number;
  audioUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    actionItems: string[];
    importance: 'low' | 'medium' | 'high';
  };
}

interface VoiceNoteDetailDialogProps {
  open: boolean;
  onClose: () => void;
  note: VoiceNote | null;
  onEdit: (note: VoiceNote) => void;
  onDelete: (noteId: string) => void;
  onExport: (note: VoiceNote, format: 'txt' | 'md' | 'pdf') => void;
}

export default function VoiceNoteDetailDialog({ 
  open, 
  onClose, 
  note, 
  onEdit, 
  onDelete,
  onExport 
}: VoiceNoteDetailDialogProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  if (!note) return null;

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取情感分析颜色
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'negative':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      case 'neutral':
        return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 获取重要性颜色
  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high':
        return 'text-red-400 bg-red-500/20 border-red-400/30';
      case 'medium':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'low':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 播放/暂停音频
  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
    // TODO: 实际的音频播放逻辑
  };

  // 处理删除
  const handleDelete = async () => {
    if (!confirm('确定要删除这条语音笔记吗？此操作不可撤销。')) return;
    
    setIsDeleting(true);
    try {
      await onDelete(note.id);
      onClose();
    } catch (error) {
      console.error('删除笔记失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // 复制内容
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: 添加toast提示
  };

  // 导出笔记
  const handleExport = (format: 'txt' | 'md' | 'pdf') => {
    onExport(note, format);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-blue-400" />
              <span>语音笔记详情</span>
            </DialogTitle>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-gray-800 border-gray-700">
                <DropdownMenuItem onClick={() => onEdit(note)} className="text-white hover:bg-gray-700">
                  <Edit3 className="w-4 h-4 mr-2" />
                  编辑笔记
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleCopy(note.content)} className="text-white hover:bg-gray-700">
                  <Copy className="w-4 h-4 mr-2" />
                  复制内容
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('txt')} className="text-white hover:bg-gray-700">
                  <FileDown className="w-4 h-4 mr-2" />
                  导出为TXT
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('md')} className="text-white hover:bg-gray-700">
                  <FileDown className="w-4 h-4 mr-2" />
                  导出为Markdown
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDelete} className="text-red-400 hover:bg-red-500/20">
                  <Trash2 className="w-4 h-4 mr-2" />
                  删除笔记
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {/* 标题和基本信息 */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h2 className="text-2xl font-bold text-white">{note.title}</h2>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="border-purple-400/30 text-purple-200">
                    <Tag className="w-3 h-3 mr-1" />
                    {note.category}
                  </Badge>
                  {note.aiAnalysis && (
                    <Badge className={getImportanceColor(note.aiAnalysis.importance)}>
                      {note.aiAnalysis.importance === 'high' ? '高重要性' : 
                       note.aiAnalysis.importance === 'medium' ? '中重要性' : '低重要性'}
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-blue-200">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatDuration(note.duration)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Mic className="w-4 h-4" />
                  <span>{note.createdAt.toLocaleDateString('zh-CN')}</span>
                </div>
                {note.aiAnalysis && (
                  <Badge className={getSentimentColor(note.aiAnalysis.sentiment)}>
                    {note.aiAnalysis.sentiment === 'positive' ? '积极' : 
                     note.aiAnalysis.sentiment === 'negative' ? '消极' : '中性'}
                  </Badge>
                )}
              </div>
            </div>

            <Separator className="bg-white/10" />

            {/* 音频播放器 */}
            {note.audioUrl && (
              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                  <Volume2 className="w-5 h-5 mr-2 text-blue-400" />
                  音频播放
                </h3>
                
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={togglePlayback}
                    className={`${isPlaying ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'}`}
                  >
                    {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </Button>
                  
                  <div className="flex-1 bg-white/10 rounded-full h-2 relative">
                    <div 
                      className="bg-blue-500 h-full rounded-full transition-all duration-300"
                      style={{ width: `${(currentTime / note.duration) * 100}%` }}
                    />
                  </div>
                  
                  <span className="text-blue-200 text-sm">
                    {formatDuration(currentTime)} / {formatDuration(note.duration)}
                  </span>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleExport('txt')}
                    className="text-blue-400 hover:text-white"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* 转写内容 */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <FileText className="w-5 h-5 mr-2 text-blue-400" />
                转写内容
              </h3>
              
              <div className="bg-white/5 rounded-lg p-4">
                <p className="text-blue-100 leading-relaxed whitespace-pre-wrap">
                  {note.transcription}
                </p>
                <div className="flex justify-end mt-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopy(note.transcription)}
                    className="text-blue-400 hover:text-white"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    复制
                  </Button>
                </div>
              </div>
            </div>

            {/* AI分析摘要 */}
            {note.summary && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-purple-400" />
                  AI智能摘要
                </h3>
                
                <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-4">
                  <p className="text-purple-100 leading-relaxed">
                    {note.summary}
                  </p>
                  <div className="flex justify-end mt-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(note.summary)}
                      className="text-purple-400 hover:text-white"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      复制
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 关键词 */}
            {note.keywords && note.keywords.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">关键词</h3>
                <div className="flex flex-wrap gap-2">
                  {note.keywords.map((keyword, index) => (
                    <Badge key={index} variant="outline" className="text-blue-200 border-blue-400/30">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* AI分析详情 */}
            {note.aiAnalysis && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-green-400" />
                  AI深度分析
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 主题分析 */}
                  {note.aiAnalysis.topics && note.aiAnalysis.topics.length > 0 && (
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-2">主要话题</h4>
                      <div className="space-y-1">
                        {note.aiAnalysis.topics.map((topic, index) => (
                          <div key={index} className="text-blue-200 text-sm">• {topic}</div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 行动项 */}
                  {note.aiAnalysis.actionItems && note.aiAnalysis.actionItems.length > 0 && (
                    <div className="bg-white/5 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-2">行动项</h4>
                      <div className="space-y-1">
                        {note.aiAnalysis.actionItems.map((item, index) => (
                          <div key={index} className="text-green-200 text-sm">✓ {item}</div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 元信息 */}
            <div className="text-xs text-gray-400 space-y-1 pt-4 border-t border-white/10">
              <p>创建时间：{note.createdAt.toLocaleString('zh-CN')}</p>
              <p>更新时间：{note.updatedAt.toLocaleString('zh-CN')}</p>
              <p>笔记ID：{note.id}</p>
            </div>
          </div>
        </ScrollArea>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            关闭
          </Button>
          <Button
            onClick={() => onEdit(note)}
            className="bg-blue-500 hover:bg-blue-600"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            编辑笔记
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
