import React, { useState } from 'react';
import { 
  FileText, 
  Mic, 
  Play, 
  Pause, 
  Search, 
  Filter,
  Star,
  Archive,
  MoreVertical,
  Volume2,
  Clock,
  Tag,
  Download
} from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface VoiceNote {
  id: string;
  title: string;
  transcription: string;
  duration: string;
  createdAt: Date;
  category: 'meeting' | 'idea' | 'reminder' | 'personal';
  isFavorite: boolean;
  tags: string[];
  fileSize: string;
}

interface VoiceNotesWidgetProps {
  expanded?: boolean;
}

const VoiceNotesWidget: React.FC<VoiceNotesWidgetProps> = ({ expanded = false }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [playingNote, setPlayingNote] = useState<string | null>(null);

  const voiceNotes: VoiceNote[] = [
    {
      id: '1',
      title: 'Project Ideas Discussion',
      transcription: 'We discussed several innovative project ideas including AI-powered voice assistants, smart home automation, and mobile app development strategies...',
      duration: '5:23',
      createdAt: new Date(Date.now() - 3600000),
      category: 'meeting',
      isFavorite: true,
      tags: ['project', 'brainstorming', 'AI'],
      fileSize: '2.1 MB'
    },
    {
      id: '2',
      title: 'Shopping List',
      transcription: 'Need to buy groceries: milk, bread, eggs, chicken, vegetables for the week. Also remember to get coffee beans and some snacks...',
      duration: '1:45',
      createdAt: new Date(Date.now() - 7200000),
      category: 'personal',
      isFavorite: false,
      tags: ['shopping', 'groceries'],
      fileSize: '0.8 MB'
    },
    {
      id: '3',
      title: 'Meeting Notes - Q4 Planning',
      transcription: 'Quarterly planning session covered budget allocation, team expansion, new product features, and market analysis for the upcoming quarter...',
      duration: '12:30',
      createdAt: new Date(Date.now() - 86400000),
      category: 'meeting',
      isFavorite: true,
      tags: ['planning', 'Q4', 'budget'],
      fileSize: '5.2 MB'
    },
    {
      id: '4',
      title: 'Creative Writing Ideas',
      transcription: 'Story concepts for the new novel: time travel mystery, AI consciousness themes, parallel universe exploration, character development notes...',
      duration: '8:15',
      createdAt: new Date(Date.now() - *********),
      category: 'idea',
      isFavorite: false,
      tags: ['writing', 'creative', 'novel'],
      fileSize: '3.4 MB'
    }
  ];

  const filteredNotes = voiceNotes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.transcription.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         note.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || note.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'meeting':
        return 'bg-blue-500/20 text-blue-200 border-blue-400/30';
      case 'idea':
        return 'bg-purple-500/20 text-purple-200 border-purple-400/30';
      case 'reminder':
        return 'bg-orange-500/20 text-orange-200 border-orange-400/30';
      case 'personal':
        return 'bg-green-500/20 text-green-200 border-green-400/30';
      default:
        return 'bg-gray-500/20 text-gray-200 border-gray-400/30';
    }
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const handlePlayNote = (noteId: string) => {
    if (playingNote === noteId) {
      setPlayingNote(null);
    } else {
      setPlayingNote(noteId);
      // Simulate audio playback
      setTimeout(() => {
        setPlayingNote(null);
      }, 3000);
    }
  };

  const toggleFavorite = (noteId: string) => {
    // In a real app, this would update the backend
    console.log('Toggle favorite for note:', noteId);
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full' : ''
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <FileText className="w-4 h-4 text-white" />
            </div>
            <span>Voice Notes</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-orange-500/20 text-orange-200 border-orange-400/30">
              {filteredNotes.length} notes
            </Badge>
            <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
              <Mic className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search and Filter */}
        <div className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-300 w-4 h-4" />
            <Input
              placeholder="Search notes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white/10 border-white/20 text-white placeholder-blue-300 focus:border-blue-400"
            />
          </div>
          <Button variant="ghost" size="sm" className="text-blue-200 hover:bg-white/10">
            <Filter className="w-4 h-4" />
          </Button>
        </div>

        {/* Category Filter */}
        <div className="flex items-center space-x-2 overflow-x-auto pb-2">
          {['all', 'meeting', 'idea', 'reminder', 'personal'].map((category) => (
            <Button
              key={category}
              variant="ghost"
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className={`whitespace-nowrap ${
                selectedCategory === category
                  ? 'bg-blue-500/20 text-blue-200 border border-blue-400/30'
                  : 'text-blue-300 hover:bg-white/10'
              }`}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </Button>
          ))}
        </div>

        {/* Notes List */}
        <ScrollArea className={expanded ? "h-[500px]" : "h-[300px]"}>
          <div className="space-y-3">
            {filteredNotes.length > 0 ? (
              filteredNotes.map((note) => (
                <div
                  key={note.id}
                  className="bg-white/5 rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-200"
                >
                  {/* Note Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-white font-medium text-sm truncate">{note.title}</h4>
                        {note.isFavorite && (
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-blue-300">
                        <Clock className="w-3 h-3" />
                        <span>{note.duration}</span>
                        <span>•</span>
                        <span>{note.fileSize}</span>
                        <span>•</span>
                        <span>{formatTimeAgo(note.createdAt)}</span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Transcription Preview */}
                  <p className="text-blue-200 text-xs leading-relaxed mb-3 line-clamp-2">
                    {note.transcription}
                  </p>

                  {/* Tags and Category */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className={`text-xs ${getCategoryColor(note.category)}`}>
                        {note.category}
                      </Badge>
                      {note.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="outline" className="border-white/20 text-blue-300 text-xs">
                          <Tag className="w-2 h-2 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      {note.tags.length > 2 && (
                        <span className="text-blue-400 text-xs">+{note.tags.length - 2}</span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between pt-3 border-t border-white/10">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePlayNote(note.id)}
                        className="text-blue-300 hover:text-white"
                      >
                        {playingNote === note.id ? (
                          <Pause className="w-4 h-4 mr-1" />
                        ) : (
                          <Play className="w-4 h-4 mr-1" />
                        )}
                        {playingNote === note.id ? 'Pause' : 'Play'}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleFavorite(note.id)}
                        className={`${
                          note.isFavorite ? 'text-yellow-400' : 'text-blue-300'
                        } hover:text-white`}
                      >
                        <Star className={`w-4 h-4 mr-1 ${note.isFavorite ? 'fill-current' : ''}`} />
                        {note.isFavorite ? 'Favorited' : 'Favorite'}
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-blue-300 hover:text-white">
                        <Archive className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-blue-400 mx-auto mb-3 opacity-50" />
                <p className="text-blue-200 text-sm mb-2">
                  {searchQuery ? 'No notes found matching your search' : 'No voice notes yet'}
                </p>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-blue-300 hover:text-white"
                >
                  <Mic className="w-4 h-4 mr-1" />
                  Record Your First Note
                </Button>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Quick Stats */}
        {expanded && (
          <div className="grid grid-cols-4 gap-4 pt-4 border-t border-white/10">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{voiceNotes.length}</p>
              <p className="text-blue-200 text-xs">Total Notes</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{voiceNotes.filter(n => n.isFavorite).length}</p>
              <p className="text-blue-200 text-xs">Favorites</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{voiceNotes.filter(n => n.category === 'meeting').length}</p>
              <p className="text-blue-200 text-xs">Meetings</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-white">
                {voiceNotes.reduce((total, note) => {
                  const [minutes, seconds] = note.duration.split(':').map(Number);
                  return total + minutes + (seconds / 60);
                }, 0).toFixed(0)}m
              </p>
              <p className="text-blue-200 text-xs">Total Time</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VoiceNotesWidget;