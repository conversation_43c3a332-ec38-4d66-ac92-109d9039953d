import React, { useState, useEffect, useRef } from 'react';
import {
  Mic,
  MicOff,
  Square,
  Play,
  Pause,
  Volume2,
  FileText,
  Loader,
  Waveform,
  Settings,
  Download,
  Save,
  Sparkles
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { mockDataService } from '@/services/mockDataService';

interface VoiceRecorderWidgetProps {
  expanded?: boolean;
}

const VoiceRecorderWidget: React.FC<VoiceRecorderWidgetProps> = ({ expanded = false }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasRecording, setHasRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [transcription, setTranscription] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [volume, setVolume] = useState(0);
  const [isVoiceDetected, setIsVoiceDetected] = useState(false);
  const [audioQuality, setAudioQuality] = useState('High');
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();

  // Simulate recording timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRecording]);

  // Simulate audio visualization
  useEffect(() => {
    if (isRecording && canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const animate = () => {
        const { width, height } = canvas;
        ctx.clearRect(0, 0, width, height);

        // Generate random waveform data
        const barCount = 32;
        const barWidth = width / barCount;
        
        for (let i = 0; i < barCount; i++) {
          const barHeight = Math.random() * height * 0.8;
          const x = i * barWidth;
          const y = height - barHeight;
          
          // Create gradient
          const gradient = ctx.createLinearGradient(0, y, 0, height);
          gradient.addColorStop(0, isVoiceDetected ? '#3b82f6' : '#64748b');
          gradient.addColorStop(1, isVoiceDetected ? '#06b6d4' : '#94a3b8');
          
          ctx.fillStyle = gradient;
          ctx.fillRect(x, y, barWidth - 2, barHeight);
        }

        // Simulate voice detection
        setIsVoiceDetected(Math.random() > 0.7);
        setVolume(Math.random());

        animationFrameRef.current = requestAnimationFrame(animate);
      };

      animate();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isRecording, isVoiceDetected]);

  const handleStartRecording = () => {
    setIsRecording(true);
    setRecordingDuration(0);
    setTranscription('');
    setHasRecording(false);
  };

  const handleStopRecording = async () => {
    setIsRecording(false);
    setHasRecording(true);
    setIsTranscribing(true);

    try {
      // 使用mock数据服务进行语音识别
      const mockAudioBlob = new Blob([], { type: 'audio/wav' });
      const transcriptionResult = await mockDataService.recognizeSpeech(mockAudioBlob);
      setTranscription(transcriptionResult);
    } catch (error) {
      setTranscription('语音识别失败，请重试');
    } finally {
      setIsTranscribing(false);
    }
  };

  const handlePlayRecording = () => {
    setIsPlaying(true);
    // Simulate playback
    setTimeout(() => {
      setIsPlaying(false);
    }, recordingDuration * 1000);
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 保存为笔记
  const handleSaveAsNote = () => {
    if (!transcription) return;

    // TODO: 调用API保存笔记
    console.log('保存笔记:', {
      title: `语音笔记 ${new Date().toLocaleString()}`,
      transcription,
      duration: recordingDuration,
      createdAt: new Date()
    });

    // 模拟保存成功
    alert('笔记已保存！');
  };

  // AI内容分析
  const handleAnalyzeContent = async () => {
    if (!transcription) return;

    // TODO: 调用AI分析API
    console.log('AI分析内容:', transcription);

    // 模拟分析结果
    alert('AI分析完成！已生成摘要和关键词。');
  };

  return (
    <Card className={`bg-white/10 backdrop-blur-xl border-white/20 hover:bg-white/15 transition-all duration-300 ${
      expanded ? 'col-span-full' : ''
    }`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
              <Mic className="w-4 h-4 text-white" />
            </div>
            <span>Voice Recorder</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-200 border-blue-400/30">
              {audioQuality}
            </Badge>
            <Button variant="ghost" size="sm" className="text-blue-200 hover:bg-white/10">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Recording Controls */}
        <div className="flex items-center justify-center space-x-4">
          {!isRecording ? (
            <Button
              onClick={handleStartRecording}
              className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Mic className="w-8 h-8" />
            </Button>
          ) : (
            <Button
              onClick={handleStopRecording}
              className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
            >
              <Square className="w-8 h-8" />
            </Button>
          )}

          {hasRecording && !isRecording && (
            <>
              <Button
                onClick={handlePlayRecording}
                disabled={isPlaying}
                className="w-12 h-12 bg-green-500 hover:bg-green-600 rounded-full disabled:opacity-50"
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="border-white/20 text-white hover:bg-white/10"
              >
                <Download className="w-4 h-4 mr-2" />
                Save
              </Button>
            </>
          )}
        </div>

        {/* Recording Status */}
        <div className="text-center">
          {isRecording ? (
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-red-400 font-medium">Recording: {formatDuration(recordingDuration)}</span>
              </div>
              <p className="text-blue-200 text-sm">Speak clearly into your microphone</p>
            </div>
          ) : hasRecording ? (
            <div className="flex items-center justify-center space-x-2">
              <Volume2 className="w-4 h-4 text-green-400" />
              <span className="text-green-400 font-medium">Recording ready ({formatDuration(recordingDuration)})</span>
            </div>
          ) : (
            <p className="text-blue-200">Click the microphone to start recording</p>
          )}
        </div>

        {/* Audio Visualization */}
        <div className="bg-slate-900/50 rounded-xl p-4 border border-white/10">
          <canvas
            ref={canvasRef}
            width={400}
            height={100}
            className="w-full h-20 rounded-lg"
          />
        </div>

        {/* Voice Activity Indicator */}
        {isRecording && (
          <div className="flex items-center justify-between text-sm bg-white/5 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                isVoiceDetected ? 'bg-green-400 animate-pulse' : 'bg-gray-400'
              }`}></div>
              <span className="text-blue-200">
                {isVoiceDetected ? 'Voice detected' : 'Listening...'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-300">Volume:</span>
              <div className="w-16 h-2 bg-gray-600 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 transition-all duration-100"
                  style={{ width: `${volume * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Transcription Section */}
        {(transcription || isTranscribing) && (
          <div className="bg-white/5 rounded-xl p-4 border border-white/10">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-blue-400" />
                <span className="text-sm font-medium text-blue-200">语音转写</span>
                {isTranscribing && <Loader className="w-4 h-4 animate-spin text-blue-400" />}
              </div>

              {transcription && !isTranscribing && (
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleSaveAsNote}
                    className="text-green-400 hover:bg-green-500/20"
                  >
                    <Save className="w-3 h-3 mr-1" />
                    保存笔记
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleAnalyzeContent}
                    className="text-purple-400 hover:bg-purple-500/20"
                  >
                    <Sparkles className="w-3 h-3 mr-1" />
                    AI分析
                  </Button>
                </div>
              )}
            </div>

            {isTranscribing ? (
              <div className="flex items-center space-x-2 text-blue-300">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm">正在转换语音为文字...</span>
              </div>
            ) : (
              <div className="text-white bg-white/10 p-3 rounded-lg border border-white/10">
                <p className="text-sm leading-relaxed">{transcription}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VoiceRecorderWidget;