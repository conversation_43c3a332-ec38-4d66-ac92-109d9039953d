import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Tag, 
  Mic,
  Save,
  X,
  AlertCircle,
  <PERSON>rkles,
  RefreshCw
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { creativeAIService } from '@/services/apiService';

interface VoiceNote {
  id?: string;
  title: string;
  content: string;
  transcription: string;
  summary: string;
  keywords: string[];
  category: string;
  duration: number;
  audioUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  aiAnalysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
    actionItems: string[];
    importance: 'low' | 'medium' | 'high';
  };
}

interface VoiceNoteEditDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (note: VoiceNote) => void;
  note?: VoiceNote | null;
  mode: 'create' | 'edit';
}

const CATEGORIES = [
  { value: 'meeting', label: '会议' },
  { value: 'idea', label: '想法' },
  { value: 'todo', label: '待办' },
  { value: 'learning', label: '学习' },
  { value: 'personal', label: '个人' },
  { value: 'work', label: '工作' },
  { value: 'other', label: '其他' }
];

export default function VoiceNoteEditDialog({ 
  open, 
  onClose, 
  onSave, 
  note, 
  mode 
}: VoiceNoteEditDialogProps) {
  const [formData, setFormData] = useState<VoiceNote>({
    title: '',
    content: '',
    transcription: '',
    summary: '',
    keywords: [],
    category: 'other',
    duration: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [keywordInput, setKeywordInput] = useState('');

  // 初始化表单数据
  useEffect(() => {
    if (note && mode === 'edit') {
      setFormData({
        ...note,
        keywords: note.keywords || [],
      });
      setKeywordInput(note.keywords?.join(', ') || '');
    } else {
      // 重置为默认值
      setFormData({
        title: '',
        content: '',
        transcription: '',
        summary: '',
        keywords: [],
        category: 'other',
        duration: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      setKeywordInput('');
    }
    setErrors({});
  }, [note, mode, open]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '请输入笔记标题';
    }

    if (!formData.content.trim() && !formData.transcription.trim()) {
      newErrors.content = '请输入笔记内容或转写文本';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSaving(true);
    try {
      // 处理关键词
      const keywords = keywordInput
        .split(',')
        .map(k => k.trim())
        .filter(k => k.length > 0);

      const noteData = {
        ...formData,
        keywords,
        updatedAt: new Date(),
      };

      await onSave(noteData);
      onClose();
    } catch (error) {
      console.error('保存笔记失败:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // AI生成摘要
  const generateAISummary = async () => {
    if (!formData.content && !formData.transcription) {
      alert('请先输入内容或转写文本');
      return;
    }

    setIsGeneratingAI(true);
    try {
      const content = formData.content || formData.transcription;
      const summary = await creativeAIService.processContent(content, 'summary');
      
      setFormData(prev => ({
        ...prev,
        summary: summary
      }));
    } catch (error) {
      console.error('AI摘要生成失败:', error);
      alert('AI摘要生成失败，请重试');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // AI提取关键词
  const extractKeywords = async () => {
    if (!formData.content && !formData.transcription) {
      alert('请先输入内容或转写文本');
      return;
    }

    setIsGeneratingAI(true);
    try {
      const content = formData.content || formData.transcription;
      // 模拟AI关键词提取
      const mockKeywords = ['项目管理', '团队协作', '技术方案', '进度跟踪'];
      
      setKeywordInput(mockKeywords.join(', '));
      setFormData(prev => ({
        ...prev,
        keywords: mockKeywords
      }));
    } catch (error) {
      console.error('关键词提取失败:', error);
      alert('关键词提取失败，请重试');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // 处理关键词输入
  const handleKeywordChange = (value: string) => {
    setKeywordInput(value);
    const keywords = value
      .split(',')
      .map(k => k.trim())
      .filter(k => k.length > 0);
    
    setFormData(prev => ({
      ...prev,
      keywords
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-400" />
            <span>{mode === 'create' ? '新建笔记' : '编辑笔记'}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title" className="text-white">笔记标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="输入笔记标题..."
                className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 mt-1"
              />
              {errors.title && (
                <p className="text-red-400 text-xs mt-1 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  {errors.title}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="category" className="text-white">分类</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
              >
                <SelectTrigger className="bg-white/10 border-white/20 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map(cat => (
                    <SelectItem key={cat.value} value={cat.value}>
                      {cat.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 转写内容 */}
          <div>
            <Label htmlFor="transcription" className="text-white">转写内容</Label>
            <Textarea
              id="transcription"
              value={formData.transcription}
              onChange={(e) => setFormData(prev => ({ ...prev, transcription: e.target.value }))}
              placeholder="语音转写的文本内容..."
              className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[120px] mt-1"
            />
          </div>

          {/* 笔记内容 */}
          <div>
            <Label htmlFor="content" className="text-white">笔记内容</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="补充的笔记内容或编辑后的文本..."
              className="bg-white/10 border-white/20 text-white placeholder:text-blue-200 min-h-[120px] mt-1"
            />
            {errors.content && (
              <p className="text-red-400 text-xs mt-1 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1" />
                {errors.content}
              </p>
            )}
          </div>

          {/* AI摘要 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="summary" className="text-white">AI智能摘要</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={generateAISummary}
                disabled={isGeneratingAI}
                className="text-purple-400 hover:text-white"
              >
                {isGeneratingAI ? (
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                ) : (
                  <Sparkles className="w-3 h-3 mr-1" />
                )}
                生成摘要
              </Button>
            </div>
            <Textarea
              id="summary"
              value={formData.summary}
              onChange={(e) => setFormData(prev => ({ ...prev, summary: e.target.value }))}
              placeholder="AI生成的内容摘要..."
              className="bg-purple-500/10 border-purple-400/30 text-purple-100 placeholder:text-purple-300 min-h-[80px]"
            />
          </div>

          {/* 关键词 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="keywords" className="text-white">关键词</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={extractKeywords}
                disabled={isGeneratingAI}
                className="text-blue-400 hover:text-white"
              >
                {isGeneratingAI ? (
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                ) : (
                  <Sparkles className="w-3 h-3 mr-1" />
                )}
                提取关键词
              </Button>
            </div>
            <Input
              id="keywords"
              value={keywordInput}
              onChange={(e) => handleKeywordChange(e.target.value)}
              placeholder="输入关键词，用逗号分隔..."
              className="bg-white/10 border-white/20 text-white placeholder:text-blue-200"
            />
            
            {/* 关键词预览 */}
            {formData.keywords.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="text-blue-200 border-blue-400/30">
                    <Tag className="w-3 h-3 mr-1" />
                    {keyword}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* 元信息显示 */}
          {mode === 'edit' && note && (
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">笔记信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm text-blue-200">
                <div>
                  <span className="text-gray-400">创建时间：</span>
                  {note.createdAt.toLocaleString('zh-CN')}
                </div>
                <div>
                  <span className="text-gray-400">音频时长：</span>
                  {Math.floor(note.duration / 60)}:{(note.duration % 60).toString().padStart(2, '0')}
                </div>
                <div>
                  <span className="text-gray-400">更新时间：</span>
                  {note.updatedAt.toLocaleString('zh-CN')}
                </div>
                <div>
                  <span className="text-gray-400">笔记ID：</span>
                  {note.id}
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
            <Button
              variant="ghost"
              onClick={onClose}
              disabled={isSaving}
              className="text-gray-400 hover:text-white"
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSaving}
              className="bg-blue-500 hover:bg-blue-600"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {mode === 'create' ? '创建笔记' : '保存修改'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
