import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  MessageSquare,
  FileText,
  Calendar,
  Sparkles,
  Clock,
  Filter,
  ArrowRight,
  X,
  History,
  TrendingUp,
  Star,
  Tag,
  User,
  Bot,
  Mic,
  BookOpen,
  Target
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// 搜索结果类型
interface SearchResult {
  id: string;
  type: 'conversation' | 'note' | 'schedule' | 'ai-task';
  title: string;
  content: string;
  excerpt: string;
  createdAt: Date;
  updatedAt: Date;
  relevanceScore: number;
  metadata: {
    author?: string;
    model?: string;
    category?: string;
    tags?: string[];
    status?: string;
    duration?: number;
    tokens?: number;
  };
}

// 搜索过滤器
interface SearchFilters {
  type: 'all' | 'conversation' | 'note' | 'schedule' | 'ai-task';
  dateRange: 'all' | 'today' | 'week' | 'month' | 'year';
  sortBy: 'relevance' | 'date' | 'title';
  category?: string;
  author?: string;
}

// 搜索建议
interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'popular' | 'suggestion';
  count?: number;
}

interface GlobalSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onResultClick: (result: SearchResult) => void;
}

export default function GlobalSearchDialog({ open, onClose, onResultClick }: GlobalSearchDialogProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'all',
    dateRange: 'all',
    sortBy: 'relevance'
  });
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);

  // 初始化数据
  useEffect(() => {
    if (open) {
      // 加载最近搜索
      const recent = localStorage.getItem('voicehub-recent-searches');
      if (recent) {
        setRecentSearches(JSON.parse(recent));
      }
      
      // 加载搜索建议
      loadSuggestions();
      
      // 聚焦输入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  // 加载搜索建议
  const loadSuggestions = () => {
    const mockSuggestions: SearchSuggestion[] = [
      { id: '1', text: 'VoiceHub功能', type: 'popular', count: 15 },
      { id: '2', text: '会议记录', type: 'popular', count: 12 },
      { id: '3', text: '语音识别', type: 'popular', count: 8 },
      { id: '4', text: 'AI分析', type: 'popular', count: 6 },
      { id: '5', text: '项目管理', type: 'suggestion' },
      { id: '6', text: '技术讨论', type: 'suggestion' },
      { id: '7', text: '创意写作', type: 'suggestion' },
      { id: '8', text: '代码生成', type: 'suggestion' }
    ];
    setSuggestions(mockSuggestions);
  };

  // 执行搜索
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsSearching(true);
    
    try {
      // 模拟搜索延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟搜索结果
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'conversation',
          title: 'VoiceHub功能咨询对话',
          content: '用户询问VoiceHub的主要功能，AI助手详细介绍了四大核心功能模块...',
          excerpt: '...VoiceHub是一个智能语音助手平台，主要功能包括AI对话、语音笔记、创意AI、日程管理...',
          createdAt: new Date(Date.now() - 86400000),
          updatedAt: new Date(Date.now() - 86400000 + 30000),
          relevanceScore: 0.95,
          metadata: {
            model: 'GPT-4',
            category: 'general',
            tags: ['功能介绍', '产品咨询'],
            tokens: 135
          }
        },
        {
          id: '2',
          type: 'note',
          title: '项目会议记录',
          content: '今天讨论了VoiceHub项目的前端优化方案，重点关注用户体验提升和成本控制...',
          excerpt: '...讨论VoiceHub前端优化，包括UI改进和功能精简，重点关注用户体验和成本控制...',
          createdAt: new Date(Date.now() - 172800000),
          updatedAt: new Date(Date.now() - 172800000 + 3600000),
          relevanceScore: 0.88,
          metadata: {
            category: 'meeting',
            tags: ['前端优化', 'UI改进', '成本控制', '用户体验'],
            duration: 180
          }
        },
        {
          id: '3',
          type: 'ai-task',
          title: '智能摘要 - 技术文档',
          content: 'AI生成的技术文档摘要，包含核心要点和关键信息...',
          excerpt: '...本文档介绍了VoiceHub的技术架构，包括语音识别、自然语言处理、AI模型集成等核心技术...',
          createdAt: new Date(Date.now() - 259200000),
          updatedAt: new Date(Date.now() - 259200000),
          relevanceScore: 0.82,
          metadata: {
            model: 'GPT-3.5',
            category: 'technical',
            tags: ['技术文档', '架构设计'],
            tokens: 450
          }
        },
        {
          id: '4',
          type: 'schedule',
          title: '产品发布会议',
          content: '讨论VoiceHub新版本发布计划，包括功能规划、时间安排、资源分配等...',
          excerpt: '...新版本将重点优化语音识别准确率，增加多语言支持，改进用户界面设计...',
          createdAt: new Date(Date.now() - 345600000),
          updatedAt: new Date(Date.now() - 345600000),
          relevanceScore: 0.75,
          metadata: {
            category: 'meeting',
            tags: ['产品发布', '版本规划'],
            status: 'scheduled'
          }
        }
      ];

      // 根据查询过滤结果
      const filteredResults = mockResults.filter(result => {
        const matchesQuery = result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            result.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            result.metadata.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
        
        const matchesType = filters.type === 'all' || result.type === filters.type;
        
        return matchesQuery && matchesType;
      });

      // 排序结果
      filteredResults.sort((a, b) => {
        switch (filters.sortBy) {
          case 'date':
            return b.updatedAt.getTime() - a.updatedAt.getTime();
          case 'title':
            return a.title.localeCompare(b.title);
          case 'relevance':
          default:
            return b.relevanceScore - a.relevanceScore;
        }
      });

      setResults(filteredResults);
      
      // 保存到最近搜索
      if (searchQuery.trim()) {
        const newRecentSearches = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 10);
        setRecentSearches(newRecentSearches);
        localStorage.setItem('voicehub-recent-searches', JSON.stringify(newRecentSearches));
      }
      
    } catch (error) {
      console.error('Search failed:', error);
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理搜索输入
  const handleSearchInput = (value: string) => {
    setQuery(value);
    
    // 防抖搜索
    const timeoutId = setTimeout(() => {
      performSearch(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // 获取结果类型图标
  const getResultTypeIcon = (type: string) => {
    switch (type) {
      case 'conversation':
        return MessageSquare;
      case 'note':
        return FileText;
      case 'schedule':
        return Calendar;
      case 'ai-task':
        return Sparkles;
      default:
        return FileText;
    }
  };

  // 获取结果类型标签
  const getResultTypeLabel = (type: string) => {
    switch (type) {
      case 'conversation':
        return '对话';
      case 'note':
        return '笔记';
      case 'schedule':
        return '日程';
      case 'ai-task':
        return 'AI任务';
      default:
        return '未知';
    }
  };

  // 获取结果类型颜色
  const getResultTypeColor = (type: string) => {
    switch (type) {
      case 'conversation':
        return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      case 'note':
        return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'schedule':
        return 'text-purple-400 bg-purple-500/20 border-purple-400/30';
      case 'ai-task':
        return 'text-orange-400 bg-orange-500/20 border-orange-400/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  // 高亮搜索关键词
  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-400/30 text-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5 text-blue-400" />
            <span>全局搜索</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索输入框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => handleSearchInput(e.target.value)}
              placeholder="搜索对话、笔记、日程、AI任务..."
              className="pl-10 pr-10 bg-white/10 border-white/20 text-white placeholder:text-blue-200 h-12 text-lg"
            />
            {query && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setQuery('');
                  setResults([]);
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* 过滤器 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={filters.type} onValueChange={(value: any) => setFilters(prev => ({ ...prev, type: value }))}>
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="conversation">对话</SelectItem>
                  <SelectItem value="note">笔记</SelectItem>
                  <SelectItem value="schedule">日程</SelectItem>
                  <SelectItem value="ai-task">AI任务</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.sortBy} onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}>
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">相关性</SelectItem>
                  <SelectItem value="date">时间</SelectItem>
                  <SelectItem value="title">标题</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="text-blue-200 hover:text-white"
              >
                <Filter className="w-4 h-4 mr-1" />
                更多筛选
              </Button>
            </div>

            {results.length > 0 && (
              <div className="text-sm text-blue-200">
                找到 {results.length} 个结果
              </div>
            )}
          </div>

          {/* 搜索内容区域 */}
          <div className="h-96">
            {!query ? (
              // 搜索建议和最近搜索
              <div className="space-y-6">
                {/* 最近搜索 */}
                {recentSearches.length > 0 && (
                  <div>
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <History className="w-4 h-4 mr-2 text-blue-400" />
                      最近搜索
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {recentSearches.map((search, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setQuery(search);
                            performSearch(search);
                          }}
                          className="text-blue-200 hover:bg-white/10 border border-white/10"
                        >
                          <Clock className="w-3 h-3 mr-1" />
                          {search}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {/* 热门搜索 */}
                <div>
                  <h3 className="text-white font-medium mb-3 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2 text-green-400" />
                    热门搜索
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {suggestions.filter(s => s.type === 'popular').map((suggestion) => (
                      <Button
                        key={suggestion.id}
                        variant="ghost"
                        onClick={() => {
                          setQuery(suggestion.text);
                          performSearch(suggestion.text);
                        }}
                        className="justify-between text-blue-200 hover:bg-white/10 border border-white/10"
                      >
                        <span>{suggestion.text}</span>
                        {suggestion.count && (
                          <Badge variant="outline" className="text-xs">
                            {suggestion.count}
                          </Badge>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 搜索建议 */}
                <div>
                  <h3 className="text-white font-medium mb-3 flex items-center">
                    <Target className="w-4 h-4 mr-2 text-purple-400" />
                    搜索建议
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {suggestions.filter(s => s.type === 'suggestion').map((suggestion) => (
                      <Button
                        key={suggestion.id}
                        variant="ghost"
                        onClick={() => {
                          setQuery(suggestion.text);
                          performSearch(suggestion.text);
                        }}
                        className="justify-start text-blue-200 hover:bg-white/10 border border-white/10"
                      >
                        <Search className="w-3 h-3 mr-2" />
                        {suggestion.text}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            ) : isSearching ? (
              // 搜索中状态
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-blue-200">正在搜索...</p>
                </div>
              </div>
            ) : results.length === 0 ? (
              // 无结果状态
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Search className="w-12 h-12 text-gray-400 mx-auto mb-4 opacity-50" />
                  <p className="text-blue-200 mb-2">未找到相关结果</p>
                  <p className="text-blue-300 text-sm">尝试使用不同的关键词或调整筛选条件</p>
                </div>
              </div>
            ) : (
              // 搜索结果
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {results.map((result) => {
                    const IconComponent = getResultTypeIcon(result.type);
                    return (
                      <div
                        key={result.id}
                        onClick={() => onResultClick(result)}
                        className="bg-white/5 border border-white/10 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${getResultTypeColor(result.type)}`}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="text-white font-medium truncate">
                                {highlightText(result.title, query)}
                              </h4>
                              <Badge variant="outline" className={`text-xs ${getResultTypeColor(result.type)}`}>
                                {getResultTypeLabel(result.type)}
                              </Badge>
                              <div className="flex items-center text-xs text-blue-300">
                                <Clock className="w-3 h-3 mr-1" />
                                {result.updatedAt.toLocaleDateString()}
                              </div>
                            </div>
                            
                            <p className="text-blue-200 text-sm line-clamp-2 mb-2">
                              {highlightText(result.excerpt, query)}
                            </p>
                            
                            {/* 元数据 */}
                            <div className="flex items-center space-x-4 text-xs text-blue-300">
                              {result.metadata.model && (
                                <span className="flex items-center">
                                  <Bot className="w-3 h-3 mr-1" />
                                  {result.metadata.model}
                                </span>
                              )}
                              {result.metadata.category && (
                                <span className="flex items-center">
                                  <Tag className="w-3 h-3 mr-1" />
                                  {result.metadata.category}
                                </span>
                              )}
                              {result.metadata.tokens && (
                                <span>{result.metadata.tokens} tokens</span>
                              )}
                              {result.metadata.duration && (
                                <span>{Math.floor(result.metadata.duration / 60)}:{(result.metadata.duration % 60).toString().padStart(2, '0')}</span>
                              )}
                              <div className="flex items-center">
                                <Star className="w-3 h-3 mr-1 text-yellow-400" />
                                <span>{(result.relevanceScore * 100).toFixed(0)}%</span>
                              </div>
                            </div>

                            {/* 标签 */}
                            {result.metadata.tags && result.metadata.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {result.metadata.tags.slice(0, 3).map((tag, index) => (
                                  <Badge key={index} variant="outline" className="text-xs text-blue-200 border-blue-400/30">
                                    {highlightText(tag, query)}
                                  </Badge>
                                ))}
                                {result.metadata.tags.length > 3 && (
                                  <Badge variant="outline" className="text-xs text-gray-400 border-gray-400/30">
                                    +{result.metadata.tags.length - 3}
                                  </Badge>
                                )}
                              </div>
                            )}
                          </div>
                          
                          <ArrowRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
