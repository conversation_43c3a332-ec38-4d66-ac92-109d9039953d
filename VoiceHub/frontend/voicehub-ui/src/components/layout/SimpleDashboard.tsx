import React from 'react';

interface User {
  name: string;
  email: string;
  avatar?: string;
}

interface SimpleDashboardProps {
  user?: User;
}

const SimpleDashboard: React.FC<SimpleDashboardProps> = ({ user }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                🎙️ VoiceHub
              </h1>
              <p className="text-blue-200">智能语音助手平台</p>
            </div>
            {user && (
              <div className="text-right">
                <p className="text-white font-medium">{user.name}</p>
                <p className="text-blue-200 text-sm">{user.email}</p>
              </div>
            )}
          </div>
        </header>

        {/* Main Content */}
        <main className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Voice Recorder Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">🎤 语音录制</h3>
            <p className="text-blue-200 mb-4">开始录制您的语音笔记</p>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              开始录制
            </button>
          </div>

          {/* Conversation Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">💬 智能对话</h3>
            <p className="text-blue-200 mb-4">与AI助手进行对话</p>
            <button className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              开始对话
            </button>
          </div>

          {/* Schedule Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">📅 日程管理</h3>
            <p className="text-blue-200 mb-4">管理您的日程安排</p>
            <button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              查看日程
            </button>
          </div>

          {/* Analytics Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">📊 数据分析</h3>
            <p className="text-blue-200 mb-4">查看使用统计和分析</p>
            <button className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              查看分析
            </button>
          </div>

          {/* Voice Notes Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">📝 语音笔记</h3>
            <p className="text-blue-200 mb-4">管理您的语音笔记</p>
            <button className="w-full bg-teal-600 hover:bg-teal-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              查看笔记
            </button>
          </div>

          {/* Settings Card */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h3 className="text-xl font-semibold text-white mb-4">⚙️ 设置</h3>
            <p className="text-blue-200 mb-4">配置应用设置</p>
            <button className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              打开设置
            </button>
          </div>
        </main>

        {/* Footer */}
        <footer className="mt-12 text-center">
          <p className="text-blue-200/60">
            VoiceHub - 让语音交互更自然、更智能
          </p>
        </footer>
      </div>
    </div>
  );
};

export default SimpleDashboard;
