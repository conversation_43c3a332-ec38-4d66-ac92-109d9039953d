import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import speechService, { VoiceRecorder } from '../../services/speechService';
import conversationService from '../../services/conversationService';
import {
  Mic,
  MicOff,
  MessageSquare,
  Calendar,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  User as UserIcon,
  Bell,
  Search
} from 'lucide-react';

interface User {
  name: string;
  email: string;
  avatar?: string;
}

interface TestDashboardProps {
  user?: User;
  onLogout: () => void;
}

const TestDashboard: React.FC<TestDashboardProps> = ({ user, onLogout }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recognizedText, setRecognizedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);
  const voiceRecorderRef = useRef<VoiceRecorder | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [notifications] = useState([
    { id: 1, message: '您有一个新的语音笔记', time: '2分钟前' },
    { id: 2, message: '今天下午3点有会议提醒', time: '1小时前' }
  ]);

  const handleStartRecording = async () => {
    try {
      console.log('🎙️ Starting voice recording...');
      setRecognizedText('');
      setIsProcessing(false);

      // 创建录音器
      voiceRecorderRef.current = speechService.createRecorder();
      await voiceRecorderRef.current.startRecording();

      setIsRecording(true);
      setRecordingTime(0);

      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      console.log('✅ Recording started successfully');
    } catch (error: any) {
      console.error('❌ Failed to start recording:', error);
      alert(error.message || '无法开始录音，请检查麦克风权限');
    }
  };

  const handleStopRecording = async () => {
    try {
      if (!voiceRecorderRef.current || !isRecording) {
        return;
      }

      console.log('🛑 Stopping voice recording...');
      setIsProcessing(true);

      // 停止录音
      const audioBlob = await voiceRecorderRef.current.stopRecording();
      setIsRecording(false);

      // 清除计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      console.log('📤 Sending audio for recognition...');

      // 发送到语音识别服务
      const response = await speechService.recognizeSpeech({
        audio: audioBlob,
        format: 'webm',
        sampleRate: 16000,
      });

      if (response.success && response.text) {
        setRecognizedText(response.text);
        console.log('✅ Speech recognition successful:', response.text);
      } else {
        setRecognizedText('识别失败：' + (response.error || '未知错误'));
        console.error('❌ Speech recognition failed:', response.error);
      }

    } catch (error: any) {
      console.error('❌ Failed to stop recording:', error);
      setRecognizedText('录音处理失败：' + error.message);
    } finally {
      setIsProcessing(false);
      setRecordingTime(0);
      voiceRecorderRef.current = null;
    }
  };

  const handleStartConversation = async () => {
    try {
      console.log('💬 Starting new conversation...');
      setIsCreatingConversation(true);

      // 创建新对话
      const conversation = await conversationService.createConversation({
        title: `对话 - ${new Date().toLocaleString()}`,
        type: 'GENERAL',
      });

      if (conversation) {
        console.log('✅ Conversation created:', conversation.id);
        setCurrentConversationId(conversation.id);

        // 发送测试消息
        const testMessage = await conversationService.sendMessage(conversation.id, {
          content: '你好！请介绍一下VoiceHub的功能。',
          isVoice: false,
        });

        if (testMessage) {
          alert(`✅ 对话已创建并发送测试消息！\n\n对话ID: ${conversation.id}\n\n用户消息: ${testMessage.content}\n\n现在可以查看对话历史或继续对话。`);
        } else {
          alert(`✅ 对话已创建！\n对话ID: ${conversation.id}\n\n您可以开始发送消息了。`);
        }
      } else {
        alert('❌ 创建对话失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('❌ Failed to start conversation:', error);
      alert('❌ 启动对话失败：' + error.message);
    } finally {
      setIsCreatingConversation(false);
    }
  };

  const handleViewSchedule = async () => {
    try {
      console.log('📅 Loading schedule...');
      // 这里可以调用日程管理API
      alert('📅 日程管理功能\n\n当前功能包括：\n• 查看今日日程\n• 创建新的日程\n• 语音创建提醒\n\n（后端API开发中...）');
    } catch (error: any) {
      console.error('❌ Failed to load schedule:', error);
      alert('❌ 加载日程失败：' + error.message);
    }
  };

  const handleViewAnalytics = async () => {
    try {
      console.log('📊 Loading analytics...');
      // 这里可以调用数据分析API
      const mockData = {
        totalRecordings: 15,
        totalDuration: '2小时30分钟',
        averageAccuracy: '95%',
        mostActiveTime: '下午2-4点'
      };

      alert(`📊 语音使用分析\n\n• 总录音次数: ${mockData.totalRecordings}\n• 总录音时长: ${mockData.totalDuration}\n• 平均识别准确率: ${mockData.averageAccuracy}\n• 最活跃时段: ${mockData.mostActiveTime}\n\n（模拟数据，后端API开发中...）`);
    } catch (error: any) {
      console.error('❌ Failed to load analytics:', error);
      alert('❌ 加载分析数据失败：' + error.message);
    }
  };

  const handleViewNotes = async () => {
    try {
      console.log('📝 Loading voice notes...');
      // 这里可以调用语音笔记API
      alert('📝 语音笔记管理\n\n功能包括：\n• 查看所有语音笔记\n• 搜索笔记内容\n• 播放原始录音\n• 编辑转写文本\n• 添加标签分类\n\n（后端API开发中...）');
    } catch (error: any) {
      console.error('❌ Failed to load notes:', error);
      alert('❌ 加载语音笔记失败：' + error.message);
    }
  };

  const handleSettings = async () => {
    try {
      console.log('⚙️ Opening settings...');
      const currentSettings = {
        language: '中文',
        voiceModel: '标准模型',
        autoSave: '开启',
        notifications: '开启'
      };

      alert(`⚙️ 系统设置\n\n当前配置：\n• 语言设置: ${currentSettings.language}\n• 语音模型: ${currentSettings.voiceModel}\n• 自动保存: ${currentSettings.autoSave}\n• 通知提醒: ${currentSettings.notifications}\n\n（设置页面开发中...）`);
    } catch (error: any) {
      console.error('❌ Failed to open settings:', error);
      alert('❌ 打开设置失败：' + error.message);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="bg-white/5 backdrop-blur-sm border-b border-white/10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">🎙️ VoiceHub</h1>
              <Badge variant="secondary" className="bg-blue-600/20 text-blue-200">
                智能语音助手
              </Badge>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <div className="relative">
                <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
                  <Bell className="h-4 w-4" />
                  {notifications.length > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 bg-red-500 text-xs">
                      {notifications.length}
                    </Badge>
                  )}
                </Button>
              </div>

              {/* User Menu */}
              {user && (
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-white font-medium text-sm">{user.name}</p>
                    <p className="text-blue-200 text-xs">{user.email}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onLogout}
                    className="text-white hover:bg-white/10"
                  >
                    <LogOut className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Voice Recorder Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mic className="h-5 w-5 mr-2" />
                语音录制
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-blue-200">
                  {isRecording
                    ? `录音中... ${formatTime(recordingTime)}`
                    : isProcessing
                    ? '正在识别语音...'
                    : '开始录制您的语音笔记'
                  }
                </p>

                {recognizedText && (
                  <div className="p-3 bg-white/5 rounded-lg border border-white/10">
                    <p className="text-sm text-gray-300 mb-1">识别结果：</p>
                    <p className="text-white">{recognizedText}</p>
                  </div>
                )}

                <Button
                  className={`w-full ${
                    isRecording
                      ? 'bg-red-600 hover:bg-red-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                  onClick={isRecording ? handleStopRecording : handleStartRecording}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      处理中...
                    </>
                  ) : isRecording ? (
                    <>
                      <MicOff className="h-4 w-4 mr-2" />
                      停止录制
                    </>
                  ) : (
                    <>
                      <Mic className="h-4 w-4 mr-2" />
                      开始录制
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Conversation Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                智能对话
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-blue-200">
                  {currentConversationId
                    ? `当前对话: ${currentConversationId.substring(0, 8)}...`
                    : '与AI助手进行智能对话'
                  }
                </p>

                {currentConversationId && (
                  <div className="p-3 bg-white/5 rounded-lg border border-white/10">
                    <p className="text-sm text-gray-300 mb-1">对话状态：</p>
                    <p className="text-green-400">✅ 已连接</p>
                  </div>
                )}

                <Button
                  className="w-full bg-green-600 hover:bg-green-700"
                  onClick={handleStartConversation}
                  disabled={isCreatingConversation}
                >
                  {isCreatingConversation ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      创建中...
                    </>
                  ) : currentConversationId ? (
                    <>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      新建对话
                    </>
                  ) : (
                    <>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      开始对话
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                日程管理
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-200 mb-4">管理您的日程安排和提醒</p>
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700"
                onClick={handleViewSchedule}
              >
                <Calendar className="h-4 w-4 mr-2" />
                查看日程
              </Button>
            </CardContent>
          </Card>

          {/* Analytics Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                数据分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-200 mb-4">查看使用统计和语音分析</p>
              <Button
                className="w-full bg-orange-600 hover:bg-orange-700"
                onClick={handleViewAnalytics}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                查看分析
              </Button>
            </CardContent>
          </Card>

          {/* Voice Notes Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                语音笔记
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-200 mb-4">管理和搜索您的语音笔记</p>
              <Button
                className="w-full bg-teal-600 hover:bg-teal-700"
                onClick={handleViewNotes}
              >
                <FileText className="h-4 w-4 mr-2" />
                查看笔记
              </Button>
            </CardContent>
          </Card>

          {/* Settings Card */}
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                系统设置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-200 mb-4">配置应用设置和偏好</p>
              <Button
                className="w-full bg-gray-600 hover:bg-gray-700"
                onClick={handleSettings}
              >
                <Settings className="h-4 w-4 mr-2" />
                打开设置
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white">最近活动</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {notifications.map((notification) => (
                  <div key={notification.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <p className="text-white">{notification.message}</p>
                    <span className="text-blue-200 text-sm">{notification.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TestDashboard;
