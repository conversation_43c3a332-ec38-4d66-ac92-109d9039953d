import React, { useState, useEffect } from 'react';
import {
  Mic,
  MessageSquare,
  Calendar,
  FileText,
  Settings,
  User,
  Bell,
  Search,
  Menu,
  X,
  TrendingUp,
  Sparkles,
  Bot,
  Home,
  Command,
  Database
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import VoiceRecorderWidget from '../features/voice/VoiceRecorderWidget';
import BasicChatWidget from '../features/conversation/BasicChatWidget';
import ScheduleWidget from '../features/schedule/ScheduleWidget';
import EnhancedVoiceNotesWidget from '../features/voice/EnhancedVoiceNotesWidget';
import EnhancedCreativeAIWidget from '../features/ai/EnhancedCreativeAIWidget';
import GlobalSearchDialog from '../features/search/GlobalSearchDialog';
import UserSettingsDialog from '../features/settings/UserSettingsDialog';
import DataManagementDialog from '../features/data/DataManagementDialog';
import WelcomeSection from '../features/dashboard/WelcomeSection';

interface DashboardProps {
  user?: {
    name: string;
    avatar?: string;
    email: string;
  };
  onLogout?: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [isRecording, setIsRecording] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showGlobalSearch, setShowGlobalSearch] = useState(false);
  const [showUserSettings, setShowUserSettings] = useState(false);
  const [showDataManagement, setShowDataManagement] = useState(false);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K 打开全局搜索
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setShowGlobalSearch(true);
      }

      // ESC 关闭搜索
      if (event.key === 'Escape' && showGlobalSearch) {
        setShowGlobalSearch(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showGlobalSearch]);

  // 处理搜索结果点击
  const handleSearchResultClick = (result: any) => {
    setShowGlobalSearch(false);

    // 根据结果类型导航到相应页面
    switch (result.type) {
      case 'conversation':
        setActiveSection('ai-chat');
        break;
      case 'note':
        setActiveSection('voice-notes');
        break;
      case 'schedule':
        setActiveSection('schedule');
        break;
      case 'ai-task':
        setActiveSection('creative-ai');
        break;
      default:
        break;
    }

    // TODO: 可以添加更具体的导航逻辑，比如直接打开特定的对话或笔记
  };

  // 处理用户设置保存
  const handleSettingsSave = async (settings: any) => {
    try {
      // 这里可以调用API保存设置到服务器
      console.log('Settings saved:', settings);

      // 应用一些设置到当前界面
      if (settings.appearance?.theme) {
        // TODO: 应用主题设置
      }

      // TODO: 显示保存成功提示
    } catch (error) {
      console.error('Failed to save settings:', error);
      // TODO: 显示错误提示
    }
  };

  const navigationItems = [
    { id: 'dashboard', label: '主页', icon: Home },
    { id: 'ai-chat', label: 'AI对话', icon: Bot },
    { id: 'voice-notes', label: '语音笔记', icon: FileText },
    { id: 'creative-ai', label: '创意AI', icon: Sparkles },
    { id: 'schedule', label: '日程管理', icon: Calendar },
    { id: 'settings', label: '设置', icon: Settings },
  ];

  const quickStats = [
    { label: '今日对话', value: '12', trend: '+3', color: 'text-blue-400' },
    { label: '语音笔记', value: '48', trend: '+8', color: 'text-green-400' },
    { label: 'AI处理任务', value: '24', trend: '+12', color: 'text-purple-400' },
    { label: '日程安排', value: '6', trend: '+2', color: 'text-orange-400' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-5"></div>
      <div className="fixed inset-0 bg-gradient-to-br from-blue-500/10 via-cyan-500/5 to-purple-500/10"></div>
      
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:translate-x-0`}>
        <div className="h-full bg-white/10 backdrop-blur-xl border-r border-white/20">
          {/* Logo */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <Mic className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">VoiceHub</h1>
                <p className="text-xs text-blue-200">AI Assistant</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-white hover:bg-white/10"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* User Profile */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.name || 'Guest User'}
                </p>
                <p className="text-xs text-blue-200 truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
              {onLogout && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onLogout}
                  className="text-blue-200 hover:bg-white/10"
                  title="退出登录"
                >
                  <Settings className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    if (item.id === 'settings') {
                      setShowUserSettings(true);
                    } else {
                      setActiveSection(item.id);
                    }
                  }}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                    activeSection === item.id && item.id !== 'settings'
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-white border border-blue-400/30'
                      : 'text-blue-100 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Quick Actions */}
          <div className="p-4 mt-auto">
            <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl p-4 border border-blue-400/30">
              <h3 className="text-sm font-medium text-white mb-2">Quick Record</h3>
              <Button
                onClick={() => setIsRecording(!isRecording)}
                className={`w-full ${
                  isRecording
                    ? 'bg-red-500 hover:bg-red-600'
                    : 'bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600'
                }`}
              >
                <Mic className="w-4 h-4 mr-2" />
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="lg:ml-64">
        {/* Top Bar */}
        <header className="bg-white/10 backdrop-blur-xl border-b border-white/20 sticky top-0 z-40">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden text-white hover:bg-white/10"
                onClick={() => setIsSidebarOpen(true)}
              >
                <Menu className="w-5 h-5" />
              </Button>
              
              <div className="hidden md:block">
                <h2 className="text-2xl font-bold text-white capitalize">
                  {activeSection.replace('-', ' ')}
                </h2>
                <p className="text-blue-200 text-sm">
                  {currentTime.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Global Search */}
              <Button
                variant="ghost"
                onClick={() => setShowGlobalSearch(true)}
                className="hidden md:flex items-center space-x-2 text-blue-200 hover:bg-white/10 border border-white/20 px-3 py-2 rounded-lg"
              >
                <Search className="w-4 h-4" />
                <span className="text-sm">搜索...</span>
                <div className="flex items-center space-x-1 text-xs text-blue-300 bg-white/10 px-2 py-1 rounded">
                  <Command className="w-3 h-3" />
                  <span>K</span>
                </div>
              </Button>

              {/* Notifications */}
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/10 relative">
                <Bell className="w-5 h-5" />
                <Badge className="absolute -top-1 -right-1 w-5 h-5 p-0 bg-red-500 text-xs">
                  3
                </Badge>
              </Button>

              {/* Data Management */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDataManagement(true)}
                className="text-white hover:bg-white/10"
                title="数据管理"
              >
                <Database className="w-5 h-5" />
              </Button>

              {/* Voice Status */}
              <div className="flex items-center space-x-2 bg-white/10 rounded-full px-3 py-2">
                <div className={`w-2 h-2 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-green-500'}`}></div>
                <span className="text-sm text-white">
                  {isRecording ? 'Recording' : 'Ready'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          {activeSection === 'dashboard' && (
            <WelcomeSection
              user={user}
              onNavigate={setActiveSection}
            />
          )}

          {/* Other sections would be rendered here based on activeSection */}
          {activeSection === 'ai-chat' && (
            <div className="max-w-4xl mx-auto">
              <BasicChatWidget expanded />
            </div>
          )}

          {activeSection === 'voice-notes' && (
            <div className="max-w-6xl mx-auto">
              <EnhancedVoiceNotesWidget expanded />
            </div>
          )}

          {activeSection === 'creative-ai' && (
            <div className="max-w-6xl mx-auto">
              <EnhancedCreativeAIWidget expanded />
            </div>
          )}

          {activeSection === 'schedule' && (
            <div className="max-w-6xl mx-auto">
              <ScheduleWidget expanded />
            </div>
          )}
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Global Search Dialog */}
      <GlobalSearchDialog
        open={showGlobalSearch}
        onClose={() => setShowGlobalSearch(false)}
        onResultClick={handleSearchResultClick}
      />

      {/* User Settings Dialog */}
      <UserSettingsDialog
        open={showUserSettings}
        onClose={() => setShowUserSettings(false)}
        onSave={handleSettingsSave}
      />

      {/* Data Management Dialog */}
      <DataManagementDialog
        open={showDataManagement}
        onClose={() => setShowDataManagement(false)}
      />
    </div>
  );
};

export default Dashboard;