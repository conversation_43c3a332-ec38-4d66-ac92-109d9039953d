// Mock数据服务 - 为演示提供假数据，预留真实API接口

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: Date;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isVoice?: boolean;
  model?: string;
}

export interface VoiceNote {
  id: string;
  title: string;
  content: string;
  transcription: string;
  duration: number;
  createdAt: Date;
  tags: string[];
  summary?: string;
  keywords?: string[];
  audioUrl?: string;
  category: 'meeting' | 'personal' | 'study' | 'other';
}

export interface Schedule {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  type: 'meeting' | 'reminder' | 'task';
  isCompleted?: boolean;
}

export interface AITask {
  id: string;
  type: 'summary' | 'translate' | 'format' | 'analyze' | 'creative';
  input: string;
  output: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  createdAt: Date;
  model: string;
}

class MockDataService {
  // 模拟用户数据
  getCurrentUser(): User {
    return {
      id: '1',
      name: 'VoiceHub用户',
      email: '<EMAIL>',
      avatar: '',
      createdAt: new Date('2024-01-01')
    };
  }

  // 模拟对话历史
  getChatHistory(): ChatMessage[] {
    return [
      {
        id: '1',
        content: '你好！我是VoiceHub AI助手，可以帮你处理各种问题。',
        role: 'assistant',
        timestamp: new Date(Date.now() - 3600000),
        model: 'gpt-3.5-turbo'
      },
      {
        id: '2',
        content: '请帮我分析一下今天的会议内容',
        role: 'user',
        timestamp: new Date(Date.now() - 3500000),
      },
      {
        id: '3',
        content: '好的，我来帮您分析会议内容。请提供会议的录音或文字记录，我可以为您生成摘要、提取关键点和行动项。',
        role: 'assistant',
        timestamp: new Date(Date.now() - 3400000),
        model: 'gpt-3.5-turbo'
      }
    ];
  }

  // 模拟语音笔记数据
  getVoiceNotes(): VoiceNote[] {
    return [
      {
        id: '1',
        title: '项目会议记录 - VoiceHub优化',
        content: '今天讨论了VoiceHub项目的前端优化方案...',
        transcription: '今天讨论了VoiceHub项目的前端优化方案，主要包括UI改进、功能精简、成本控制等方面。团队决定专注于核心功能，提升用户体验。',
        duration: 180,
        createdAt: new Date(Date.now() - 86400000),
        tags: ['会议', '项目', 'VoiceHub', '优化'],
        summary: '讨论VoiceHub前端优化，包括UI改进和功能精简',
        keywords: ['前端优化', 'UI改进', '成本控制', '用户体验'],
        category: 'meeting'
      },
      {
        id: '2',
        title: '学习笔记 - React最佳实践',
        content: 'React Hooks的使用技巧和注意事项...',
        transcription: 'useState和useEffect是React中最常用的Hooks，需要注意依赖数组的使用。useCallback和useMemo可以优化性能。',
        duration: 120,
        createdAt: new Date(Date.now() - 172800000),
        tags: ['学习', 'React', 'Hooks', '前端'],
        summary: 'React Hooks的使用方法和性能优化技巧',
        keywords: ['useState', 'useEffect', 'useCallback', 'useMemo'],
        category: 'study'
      },
      {
        id: '3',
        title: '个人思考 - 技术发展方向',
        content: '关于AI技术在前端开发中的应用思考...',
        transcription: 'AI技术正在改变前端开发的方式，从代码生成到用户体验优化，都有很大的潜力。需要关注最新的技术趋势。',
        duration: 95,
        createdAt: new Date(Date.now() - 259200000),
        tags: ['思考', 'AI', '技术', '前端'],
        summary: 'AI技术在前端开发中的应用前景分析',
        keywords: ['AI技术', '前端开发', '代码生成', '用户体验'],
        category: 'personal'
      }
    ];
  }

  // 模拟日程数据
  getSchedules(): Schedule[] {
    const now = new Date();
    return [
      {
        id: '1',
        title: '团队周会',
        description: '讨论本周工作进展和下周计划',
        startTime: new Date(now.getTime() + 3600000), // 1小时后
        endTime: new Date(now.getTime() + 7200000), // 2小时后
        type: 'meeting'
      },
      {
        id: '2',
        title: '完成前端优化',
        description: 'VoiceHub前端功能优化和UI改进',
        startTime: new Date(now.getTime() + 86400000), // 明天
        endTime: new Date(now.getTime() + 90000000), // 明天+1小时
        type: 'task'
      },
      {
        id: '3',
        title: '学习新技术',
        description: '研究最新的AI集成方案',
        startTime: new Date(now.getTime() + 172800000), // 后天
        endTime: new Date(now.getTime() + 176400000), // 后天+1小时
        type: 'reminder'
      }
    ];
  }

  // 模拟AI任务历史
  getAITaskHistory(): AITask[] {
    return [
      {
        id: '1',
        type: 'summary',
        input: '今天的会议讨论了项目进展，包括前端优化、后端接口完善、测试计划等内容...',
        output: '📝 会议摘要：\n• 前端优化进展顺利\n• 后端接口基本完成\n• 测试计划需要细化\n• 下周进行整体联调',
        status: 'completed',
        createdAt: new Date(Date.now() - 3600000),
        model: 'gpt-3.5-turbo'
      },
      {
        id: '2',
        type: 'translate',
        input: '这是一个关于技术实现的讨论',
        output: '🌐 翻译结果 (English)：\nThis is a discussion about technical implementation.',
        status: 'completed',
        createdAt: new Date(Date.now() - 7200000),
        model: 'gpt-3.5-turbo'
      }
    ];
  }

  // 模拟统计数据
  getQuickStats() {
    return {
      todayConversations: 12,
      voiceNotes: 48,
      aiTasks: 24,
      schedules: 6,
      trends: {
        conversations: '+3',
        notes: '+8',
        tasks: '+12',
        schedules: '+2'
      }
    };
  }

  // 模拟AI回复生成
  generateAIResponse(message: string, model: string): Promise<string> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const responses = [
          `基于${model}模型，我理解您的问题。让我为您详细解答...`,
          `使用${model}分析您的输入，我建议您可以从以下几个方面考虑...`,
          `根据${model}的理解能力，这个问题涉及多个层面...`,
          `通过${model}处理您的请求，我认为最佳的解决方案是...`,
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        resolve(randomResponse + `\n\n这是一个模拟回复，展示了AI对话的基本功能。在实际部署中，这里会连接到真实的AI服务。`);
      }, 1000 + Math.random() * 2000);
    });
  }

  // 模拟语音识别
  recognizeSpeech(audioBlob: Blob): Promise<string> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockTexts = [
          '这是通过语音识别转换的文字内容',
          '语音识别功能工作正常，可以准确转换语音为文字',
          'VoiceHub的语音识别功能支持多种语言和方言',
          '您可以通过语音快速记录想法和创建笔记'
        ];
        
        const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
        resolve(randomText);
      }, 2000 + Math.random() * 1000);
    });
  }
}

// 导出单例实例
export const mockDataService = new MockDataService();

// 导出类型
export type {
  User,
  ChatMessage,
  VoiceNote,
  Schedule,
  AITask
};
