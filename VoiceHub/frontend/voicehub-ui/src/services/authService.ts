import api from './api';

// 认证相关类型定义
export interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
}

export interface AuthResponse {
  success?: boolean;
  token?: string;
  type?: string;
  user?: {
    id: number;
    username: string;
    email: string;
    fullName?: string;
    avatarUrl?: string;
    phoneNumber?: string;
    role?: string;
  };
  message?: string;
  error?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  phoneNumber?: string;
  role?: string;
}

// 认证服务类
class AuthService {
  private readonly TOKEN_KEY = 'voicehub_token';
  private readonly USER_KEY = 'voicehub_user';
  private readonly AUTH_KEY = 'voicehub_authenticated';

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      console.log('🔐 Attempting login for:', credentials.usernameOrEmail);
      
      const response = await api.post<AuthResponse>('/auth/login', credentials);

      if (response.token && response.user) {
        // 保存认证信息到本地存储
        this.saveAuthData(response.token, response.user);
        console.log('✅ Login successful for:', response.user.username);

        // 返回标准化的响应格式
        return {
          success: true,
          token: response.token,
          user: response.user,
        };
      }

      return {
        success: false,
        error: '登录失败，请检查用户名和密码',
      };
    } catch (error: any) {
      console.error('❌ Login failed:', error);
      
      const errorMessage = error.response?.data?.message || error.message || '登录失败';
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      console.log('📝 Attempting registration for:', userData.username);
      
      const response = await api.post<AuthResponse>('/auth/register', userData);

      if (response.token && response.user) {
        // 保存认证信息到本地存储
        this.saveAuthData(response.token, response.user);
        console.log('✅ Registration successful for:', response.user.username);

        // 返回标准化的响应格式
        return {
          success: true,
          token: response.token,
          user: response.user,
        };
      }

      return {
        success: false,
        error: '注册失败，请检查输入信息',
      };
    } catch (error: any) {
      console.error('❌ Registration failed:', error);
      
      const errorMessage = error.response?.data?.message || error.message || '注册失败';
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      // 调用后端登出接口
      await api.post('/auth/logout');
    } catch (error) {
      console.error('❌ Logout API call failed:', error);
    } finally {
      // 无论API调用是否成功，都清除本地存储
      this.clearAuthData();
      console.log('👋 User logged out');
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await api.get<{ user: User }>('/auth/me');
      return response.user;
    } catch (error) {
      console.error('❌ Failed to get current user:', error);
      return null;
    }
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem(this.TOKEN_KEY);
    const isAuth = localStorage.getItem(this.AUTH_KEY) === 'true';
    return !!(token && isAuth);
  }

  /**
   * 获取当前用户
   */
  getCurrentUserFromStorage(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('❌ Failed to parse user data:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * 获取访问令牌
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * 保存认证数据到本地存储
   */
  private saveAuthData(token: string, user: User): void {
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    localStorage.setItem(this.AUTH_KEY, 'true');
  }

  /**
   * 清除认证数据
   */
  private clearAuthData(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem(this.AUTH_KEY);
  }
}

// 导出单例实例
export const authService = new AuthService();
export default authService;
