import api from './api';

// 对话相关类型定义
export interface Conversation {
  id: string;
  title: string;
  type: 'GENERAL' | 'VOICE_ASSISTANT' | 'SCHEDULE_PLANNING' | 'EMOTIONAL_SUPPORT';
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED';
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
  messageCount: number;
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  content: string;
  role: 'USER' | 'ASSISTANT';
  messageType: 'TEXT' | 'VOICE' | 'IMAGE';
  voiceFilePath?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface CreateConversationRequest {
  title: string;
  type?: string;
}

export interface SendMessageRequest {
  content: string;
  isVoice?: boolean;
  voiceFilePath?: string;
}

export interface ConversationListResponse {
  success: boolean;
  data?: {
    conversations: Conversation[];
    totalCount: number;
    currentPage: number;
    totalPages: number;
  };
  message?: string;
  error?: string;
}

export interface MessageResponse {
  success: boolean;
  data?: ChatMessage;
  message?: string;
  error?: string;
}

// 对话服务类
class ConversationService {
  /**
   * 创建新对话
   */
  async createConversation(request: CreateConversationRequest): Promise<Conversation | null> {
    try {
      console.log('💬 Creating new conversation:', request.title);
      
      const response = await api.post<{ success: boolean; data: Conversation }>('/conversations', request);
      
      if (response.success && response.data) {
        console.log('✅ Conversation created:', response.data.id);
        return response.data;
      }
      
      return null;
    } catch (error: any) {
      console.error('❌ Failed to create conversation:', error);
      throw new Error(error.response?.data?.message || '创建对话失败');
    }
  }

  /**
   * 获取对话列表
   */
  async getConversations(page = 0, size = 20): Promise<ConversationListResponse> {
    try {
      console.log(`📋 Fetching conversations (page: ${page}, size: ${size})`);
      
      const response = await api.get<ConversationListResponse>(`/conversations?page=${page}&size=${size}`);
      
      if (response.success) {
        console.log(`✅ Fetched ${response.data?.conversations.length} conversations`);
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch conversations:', error);
      return {
        success: false,
        error: error.response?.data?.message || '获取对话列表失败',
      };
    }
  }

  /**
   * 获取对话详情
   */
  async getConversation(conversationId: string): Promise<Conversation | null> {
    try {
      console.log('🔍 Fetching conversation:', conversationId);
      
      const response = await api.get<{ success: boolean; data: Conversation }>(`/conversations/${conversationId}`);
      
      if (response.success && response.data) {
        console.log('✅ Conversation fetched:', response.data.title);
        return response.data;
      }
      
      return null;
    } catch (error: any) {
      console.error('❌ Failed to fetch conversation:', error);
      return null;
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(conversationId: string, request: SendMessageRequest): Promise<ChatMessage | null> {
    try {
      console.log('📤 Sending message to conversation:', conversationId);
      
      const response = await api.post<MessageResponse>(`/conversations/${conversationId}/messages`, request);
      
      if (response.success && response.data) {
        console.log('✅ Message sent:', response.data.id);
        return response.data;
      }
      
      return null;
    } catch (error: any) {
      console.error('❌ Failed to send message:', error);
      throw new Error(error.response?.data?.message || '发送消息失败');
    }
  }

  /**
   * 获取对话消息
   */
  async getMessages(conversationId: string, page = 0, size = 50): Promise<ChatMessage[]> {
    try {
      console.log(`📨 Fetching messages for conversation: ${conversationId}`);
      
      const response = await api.get<{ success: boolean; data: ChatMessage[] }>(`/conversations/${conversationId}/messages?page=${page}&size=${size}`);
      
      if (response.success && response.data) {
        console.log(`✅ Fetched ${response.data.length} messages`);
        return response.data;
      }
      
      return [];
    } catch (error: any) {
      console.error('❌ Failed to fetch messages:', error);
      return [];
    }
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      console.log('🗑️ Deleting conversation:', conversationId);
      
      const response = await api.delete<{ success: boolean }>(`/conversations/${conversationId}`);
      
      if (response.success) {
        console.log('✅ Conversation deleted');
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ Failed to delete conversation:', error);
      return false;
    }
  }

  /**
   * 更新对话标题
   */
  async updateConversationTitle(conversationId: string, title: string): Promise<boolean> {
    try {
      console.log('✏️ Updating conversation title:', conversationId);
      
      const response = await api.put<{ success: boolean }>(`/conversations/${conversationId}`, { title });
      
      if (response.success) {
        console.log('✅ Conversation title updated');
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ Failed to update conversation title:', error);
      return false;
    }
  }

  /**
   * 获取最近对话
   */
  async getRecentConversations(days = 7): Promise<Conversation[]> {
    try {
      console.log(`📅 Fetching recent conversations (${days} days)`);
      
      const response = await api.get<{ success: boolean; data: Conversation[] }>(`/conversations/recent?days=${days}`);
      
      if (response.success && response.data) {
        console.log(`✅ Fetched ${response.data.length} recent conversations`);
        return response.data;
      }
      
      return [];
    } catch (error: any) {
      console.error('❌ Failed to fetch recent conversations:', error);
      return [];
    }
  }
}

// 导出单例实例
export const conversationService = new ConversationService();
export default conversationService;
