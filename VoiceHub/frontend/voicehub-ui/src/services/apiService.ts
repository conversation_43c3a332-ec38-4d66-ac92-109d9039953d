// API服务层 - 统一管理所有API调用，支持Mock和真实API切换

import { mockDataService, User, ChatMessage, VoiceNote, Schedule, AITask } from './mockDataService';

// API配置
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK !== 'false'; // 默认使用Mock数据

// HTTP客户端配置
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }
}

const apiClient = new ApiClient(API_BASE_URL);

// 认证服务
export class AuthService {
  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    if (USE_MOCK_DATA) {
      // Mock登录
      const user = mockDataService.getCurrentUser();
      const token = 'mock_jwt_token_' + Date.now();
      apiClient.setToken(token);
      return { user, token };
    }
    
    // 真实API调用
    const response = await apiClient.post<{ user: User; token: string }>('/auth/login', {
      email,
      password
    });
    
    apiClient.setToken(response.token);
    return response;
  }

  async register(userData: { name: string; email: string; password: string }): Promise<{ user: User; token: string }> {
    if (USE_MOCK_DATA) {
      // Mock注册
      const user: User = {
        id: Date.now().toString(),
        name: userData.name,
        email: userData.email,
        createdAt: new Date()
      };
      const token = 'mock_jwt_token_' + Date.now();
      apiClient.setToken(token);
      return { user, token };
    }
    
    // 真实API调用
    const response = await apiClient.post<{ user: User; token: string }>('/auth/register', userData);
    apiClient.setToken(response.token);
    return response;
  }

  async logout(): Promise<void> {
    if (USE_MOCK_DATA) {
      apiClient.clearToken();
      return;
    }
    
    await apiClient.post('/auth/logout');
    apiClient.clearToken();
  }

  getCurrentUser(): User | null {
    if (USE_MOCK_DATA) {
      return mockDataService.getCurrentUser();
    }
    
    // TODO: 实现真实的用户信息获取
    return null;
  }
}

// AI对话服务
export class ChatService {
  async sendMessage(message: string, conversationId?: string, model?: string): Promise<ChatMessage> {
    if (USE_MOCK_DATA) {
      const response = await mockDataService.generateAIResponse(message, model || 'gpt-3.5-turbo');
      return {
        id: Date.now().toString(),
        content: response,
        role: 'assistant',
        timestamp: new Date(),
        model: model || 'gpt-3.5-turbo'
      };
    }
    
    // 真实API调用
    return apiClient.post<ChatMessage>('/conversations/messages', {
      message,
      conversationId,
      model
    });
  }

  async getChatHistory(conversationId?: string): Promise<ChatMessage[]> {
    if (USE_MOCK_DATA) {
      return mockDataService.getChatHistory();
    }
    
    return apiClient.get<ChatMessage[]>(`/conversations/${conversationId}/messages`);
  }

  async createConversation(): Promise<{ id: string }> {
    if (USE_MOCK_DATA) {
      return { id: 'mock_conversation_' + Date.now() };
    }
    
    return apiClient.post<{ id: string }>('/conversations');
  }
}

// 语音服务
export class VoiceService {
  async recognizeSpeech(audioBlob: Blob, language?: string): Promise<string> {
    if (USE_MOCK_DATA) {
      return mockDataService.recognizeSpeech(audioBlob);
    }
    
    // 真实API调用
    const formData = new FormData();
    formData.append('audio', audioBlob);
    if (language) formData.append('language', language);
    
    const response = await fetch(`${API_BASE_URL}/speech/recognize`, {
      method: 'POST',
      headers: {
        ...(apiClient['token'] && { Authorization: `Bearer ${apiClient['token']}` }),
      },
      body: formData,
    });
    
    const result = await response.json();
    return result.transcription;
  }

  async synthesizeSpeech(text: string, voice?: string): Promise<Blob> {
    if (USE_MOCK_DATA) {
      // 返回空的音频Blob作为Mock
      return new Blob([], { type: 'audio/wav' });
    }
    
    // 真实API调用
    const response = await fetch(`${API_BASE_URL}/speech/synthesize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(apiClient['token'] && { Authorization: `Bearer ${apiClient['token']}` }),
      },
      body: JSON.stringify({ text, voice }),
    });
    
    return response.blob();
  }
}

// 笔记服务
export class NotesService {
  async getVoiceNotes(): Promise<VoiceNote[]> {
    if (USE_MOCK_DATA) {
      return mockDataService.getVoiceNotes();
    }
    
    return apiClient.get<VoiceNote[]>('/voice-notes');
  }

  async createVoiceNote(noteData: Partial<VoiceNote>): Promise<VoiceNote> {
    if (USE_MOCK_DATA) {
      const newNote: VoiceNote = {
        id: Date.now().toString(),
        title: noteData.title || `语音笔记 ${new Date().toLocaleString()}`,
        content: noteData.content || '',
        transcription: noteData.transcription || '',
        duration: noteData.duration || 0,
        createdAt: new Date(),
        tags: noteData.tags || [],
        category: noteData.category || 'personal'
      };
      return newNote;
    }
    
    return apiClient.post<VoiceNote>('/voice-notes', noteData);
  }

  async searchNotes(query: string): Promise<VoiceNote[]> {
    if (USE_MOCK_DATA) {
      const allNotes = mockDataService.getVoiceNotes();
      return allNotes.filter(note => 
        note.title.toLowerCase().includes(query.toLowerCase()) ||
        note.transcription.toLowerCase().includes(query.toLowerCase()) ||
        note.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
    }
    
    return apiClient.get<VoiceNote[]>(`/voice-notes/search?q=${encodeURIComponent(query)}`);
  }
}

// 日程服务
export class ScheduleService {
  async getSchedules(): Promise<Schedule[]> {
    if (USE_MOCK_DATA) {
      return mockDataService.getSchedules();
    }
    
    return apiClient.get<Schedule[]>('/schedules');
  }

  async createSchedule(scheduleData: Partial<Schedule>): Promise<Schedule> {
    if (USE_MOCK_DATA) {
      const newSchedule: Schedule = {
        id: Date.now().toString(),
        title: scheduleData.title || '',
        description: scheduleData.description || '',
        startTime: scheduleData.startTime || new Date(),
        endTime: scheduleData.endTime || new Date(),
        type: scheduleData.type || 'reminder'
      };
      return newSchedule;
    }
    
    return apiClient.post<Schedule>('/schedules', scheduleData);
  }
}

// 创意AI服务
export class CreativeAIService {
  async processContent(
    content: string, 
    type: 'summary' | 'translate' | 'format' | 'analyze' | 'creative',
    options?: any
  ): Promise<string> {
    if (USE_MOCK_DATA) {
      // 使用mock数据服务生成结果
      return new Promise((resolve) => {
        setTimeout(() => {
          let result = '';
          switch (type) {
            case 'summary':
              result = `📝 智能摘要：\n\n${content.substring(0, 50)}...的主要内容包括：\n• 核心观点1\n• 核心观点2\n• 核心观点3`;
              break;
            case 'translate':
              result = `🌐 翻译结果：\nThis is a translation of your content. The meaning has been preserved while adapting to the target language.`;
              break;
            case 'format':
              result = `📄 格式转换：\n\n根据您的要求，内容已转换为指定格式。\n\n[转换后的内容]`;
              break;
            case 'analyze':
              result = `🔍 内容分析：\n\n📊 基本信息：\n• 字数：${content.length}字\n• 主题：技术讨论\n• 情感：积极\n\n💡 关键洞察：\n• 重点关注实用性\n• 强调用户体验`;
              break;
            case 'creative':
              result = `✨ 创意扩展：\n\n基于您的内容，我生成了以下创意想法：\n\n🎨 角度1：从用户角度思考...\n🎨 角度2：结合技术趋势...\n🎨 角度3：考虑商业价值...`;
              break;
          }
          resolve(result);
        }, 2000 + Math.random() * 2000);
      });
    }
    
    // 真实API调用
    return apiClient.post<{ result: string }>('/ai/process', {
      content,
      type,
      options
    }).then(response => response.result);
  }
}

// 导出服务实例
export const authService = new AuthService();
export const chatService = new ChatService();
export const voiceService = new VoiceService();
export const notesService = new NotesService();
export const scheduleService = new ScheduleService();
export const creativeAIService = new CreativeAIService();
