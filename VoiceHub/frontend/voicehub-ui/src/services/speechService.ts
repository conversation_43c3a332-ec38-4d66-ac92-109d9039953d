import api from './api';

// 语音识别相关类型定义
export interface SpeechRecognitionRequest {
  audio: File | Blob;
  format?: string;
  sampleRate?: number;
}

export interface SpeechRecognitionResponse {
  success: boolean;
  text?: string;
  confidence?: number;
  language?: string;
  duration?: number;
  error?: string;
}

export interface SpeechServiceStatus {
  status: string;
  service: string;
  version: string;
  supportedLanguages: string[];
}

// 语音服务类
class SpeechService {
  /**
   * 语音转文本
   */
  async recognizeSpeech(request: SpeechRecognitionRequest): Promise<SpeechRecognitionResponse> {
    try {
      console.log('🎤 Starting speech recognition...');
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('audio', request.audio);
      formData.append('format', request.format || 'wav');
      formData.append('rate', (request.sampleRate || 16000).toString());
      
      const response = await api.upload<SpeechRecognitionResponse>('/speech/recognize', formData);
      
      if (response.success) {
        console.log('✅ Speech recognition successful:', response.text);
      } else {
        console.error('❌ Speech recognition failed:', response.error);
      }
      
      return response;
    } catch (error: any) {
      console.error('❌ Speech recognition error:', error);
      
      const errorMessage = error.response?.data?.error || error.message || '语音识别失败';
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 获取语音服务状态
   */
  async getServiceStatus(): Promise<SpeechServiceStatus | null> {
    try {
      const response = await api.get<SpeechServiceStatus>('/speech/status');
      console.log('📊 Speech service status:', response);
      return response;
    } catch (error) {
      console.error('❌ Failed to get speech service status:', error);
      return null;
    }
  }

  /**
   * 获取WebSocket连接信息
   */
  async getWebSocketInfo(): Promise<any> {
    try {
      const response = await api.get('/speech/websocket-info');
      console.log('🔌 WebSocket info:', response);
      return response;
    } catch (error) {
      console.error('❌ Failed to get WebSocket info:', error);
      return null;
    }
  }

  /**
   * 录音工具类
   */
  createRecorder(): VoiceRecorder {
    return new VoiceRecorder();
  }
}

// 语音录制器类
export class VoiceRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private isRecording = false;

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    try {
      console.log('🎙️ Starting voice recording...');
      
      // 获取麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
        } 
      });
      
      // 创建MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      this.audioChunks = [];
      
      // 设置事件监听器
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };
      
      // 开始录音
      this.mediaRecorder.start(100); // 每100ms收集一次数据
      this.isRecording = true;
      
      console.log('✅ Voice recording started');
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      throw new Error('无法开始录音，请检查麦克风权限');
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.isRecording) {
        reject(new Error('录音未开始'));
        return;
      }

      this.mediaRecorder.onstop = () => {
        console.log('🛑 Voice recording stopped');
        
        // 创建音频Blob
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        
        // 清理资源
        this.cleanup();
        
        resolve(audioBlob);
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  /**
   * 获取录音状态
   */
  getRecordingState(): boolean {
    return this.isRecording;
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
  }
}

// 导出单例实例
export const speechService = new SpeechService();
export default speechService;
