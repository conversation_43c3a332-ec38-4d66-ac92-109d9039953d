// Common types for VoiceHub UI

export interface User {
  id?: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface VoiceNote {
  id: string;
  title: string;
  content: string;
  audioUrl?: string;
  transcription?: string;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  audioUrl?: string;
}

export interface Schedule {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  type: 'meeting' | 'reminder' | 'task';
  status: 'pending' | 'completed' | 'cancelled';
}

export interface VoiceAnalytics {
  totalRecordings: number;
  totalDuration: number;
  averageAccuracy: number;
  dailyUsage: Array<{
    date: string;
    recordings: number;
    duration: number;
  }>;
}

export interface EmotionData {
  emotion: string;
  confidence: number;
  timestamp: Date;
}

export interface AudioData {
  frequency: Uint8Array;
  timeDomain: Uint8Array;
  volume: number;
  pitch: number;
  isVoiceDetected: boolean;
  noiseLevel: number;
}
