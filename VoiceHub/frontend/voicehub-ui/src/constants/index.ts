// Application constants

export const APP_CONFIG = {
  name: 'VoiceHub',
  version: '1.0.0',
  description: 'Intelligent Voice Assistant Platform',
} as const;

export const API_ENDPOINTS = {
  base: process.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  auth: '/auth',
  speech: '/speech',
  conversations: '/conversations',
  voiceNotes: '/voice-notes',
  schedules: '/schedules',
  analytics: '/analytics',
  users: '/users',
} as const;

export const AUDIO_CONFIG = {
  sampleRate: 44100,
  channels: 1,
  bitDepth: 16,
  maxRecordingDuration: 300, // 5 minutes in seconds
  minRecordingDuration: 1, // 1 second
} as const;

export const THEME_CONFIG = {
  defaultTheme: 'dark',
  storageKey: 'voicehub-ui-theme',
} as const;

export const ROUTES = {
  home: '/',
  dashboard: '/dashboard',
  voice: '/voice',
  conversations: '/conversations',
  schedule: '/schedule',
  analytics: '/analytics',
  settings: '/settings',
} as const;

export const VOICE_COMMANDS = {
  start: ['start recording', '开始录音', 'record'],
  stop: ['stop recording', '停止录音', 'stop'],
  play: ['play', '播放'],
  pause: ['pause', '暂停'],
} as const;

export const EMOTION_COLORS = {
  happy: '#10B981',
  sad: '#3B82F6',
  angry: '#EF4444',
  neutral: '#6B7280',
  excited: '#F59E0B',
  calm: '#8B5CF6',
} as const;
