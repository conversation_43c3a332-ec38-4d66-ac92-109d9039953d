# VoiceHub UI - 智能语音助手前端

## 📁 项目结构

```
src/
├── components/                 # 组件目录
│   ├── features/              # 功能模块组件
│   │   ├── voice/            # 语音相关组件
│   │   │   ├── VoiceRecorderWidget.tsx
│   │   │   ├── VoiceVisualization.tsx
│   │   │   ├── VoiceNotesWidget.tsx
│   │   │   └── AudioFeedbackSystem.tsx
│   │   ├── conversation/     # 对话相关组件
│   │   │   └── ConversationWidget.tsx
│   │   ├── schedule/         # 日程管理组件
│   │   │   └── ScheduleWidget.tsx
│   │   └── analytics/        # 数据分析组件
│   │       ├── VoiceAnalytics.tsx
│   │       └── MoodTracker.tsx
│   ├── layout/               # 布局组件
│   │   └── Dashboard.tsx
│   ├── ui/                   # 基础UI组件库 (shadcn/ui)
│   └── theme-provider.tsx    # 主题提供者
├── hooks/                    # 自定义Hooks
├── lib/                      # 工具库
├── services/                 # API服务
├── types/                    # TypeScript类型定义
├── utils/                    # 工具函数
├── constants/                # 常量定义
├── App.tsx                   # 主应用组件
├── main.tsx                  # 应用入口
└── globals.css               # 全局样式
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🛠️ 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI库**: shadcn/ui + Tailwind CSS
- **状态管理**: React Hooks + Context
- **图表**: Recharts
- **音频处理**: Web Audio API

## 📦 主要功能模块

### 🎤 语音功能 (features/voice)
- 语音录制和播放
- 实时语音可视化
- 语音笔记管理
- 音频反馈系统

### 💬 对话功能 (features/conversation)
- AI智能对话
- 对话历史管理
- 实时消息处理

### 📅 日程管理 (features/schedule)
- 智能日程创建
- 日程提醒
- 日历集成

### 📊 数据分析 (features/analytics)
- 语音使用统计
- 情绪追踪分析
- 性能监控面板

## 🎨 UI组件库

基于 shadcn/ui 构建的完整组件库，包含：
- 基础组件 (Button, Input, Card等)
- 复合组件 (Dialog, Dropdown, Table等)
- 图表组件 (Chart, Progress等)
- 布局组件 (Sidebar, Navigation等)

## 🔧 开发规范

### 组件命名
- 功能组件：`FeatureWidget.tsx`
- UI组件：小写连字符 `button.tsx`
- 布局组件：`LayoutName.tsx`

### 目录规范
- 按功能模块组织组件
- 相关文件放在同一目录
- 保持目录结构扁平化

### 导入规范
```typescript
// 绝对路径导入
import { Button } from '@/components/ui/button'
import { VoiceRecorder } from '@/components/features/voice/VoiceRecorderWidget'
```

## 📝 开发注意事项

1. 所有组件都应该有TypeScript类型定义
2. 使用Tailwind CSS进行样式开发
3. 遵循React Hooks最佳实践
4. 保持组件的单一职责原则
5. 添加适当的错误边界处理

## 🚀 部署

### Docker部署
```bash
docker build -t voicehub-ui .
docker run -p 80:80 voicehub-ui
```

### 静态部署
```bash
npm run build
# 将 dist/ 目录部署到静态服务器
```
