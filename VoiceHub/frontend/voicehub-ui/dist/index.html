<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VoiceHub - Intelligent Voice Assistant Platform</title>
    <meta name="description" content="VoiceHub is an AI-powered voice assistant platform with schedule management, voice notes, emotional companion chat, and smart analytics." />
    <style>
      /* Prevent flash of unstyled content */
      body {
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      /* Loading animation */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(59, 130, 246, 0.3);
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading when app loads */
      .app-loaded .loading-container {
        display: none;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CRjTccQJ.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CU2Hs0Zy.css">
  </head>
  <body>
    <div id="root">
      <!-- Loading screen -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script>
      // Hide loading screen when app loads
      window.addEventListener('load', function() {
        document.body.classList.add('app-loaded');
      });
    </script>
  </body>
</html>