#!/bin/bash

# VoiceHub 演示启动脚本

echo "🎙️ VoiceHub - 轻量级语音笔记和助手平台"
echo "================================================"
echo ""

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 进入前端目录
cd frontend/voicehub-ui

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
    echo ""
fi

# 启动开发服务器
echo "🚀 启动VoiceHub前端服务..."
echo ""
echo "📝 功能特性："
echo "   • 基础AI对话 - ChatGPT式一问一答"
echo "   • 智能语音笔记 - 录音转写和AI分析"
echo "   • 创意AI工具 - 内容摘要、翻译、格式转换"
echo "   • 日程管理 - 语音创建和管理日程"
echo ""
echo "💰 成本说明："
echo "   • 当前使用Mock数据演示"
echo "   • 支持多种AI模型选择"
echo "   • 提供本地化部署选项"
echo ""
echo "🌐 访问地址: http://localhost:5173"
echo "⏹️  停止服务: Ctrl+C"
echo ""

# 启动开发服务器
npm run dev
