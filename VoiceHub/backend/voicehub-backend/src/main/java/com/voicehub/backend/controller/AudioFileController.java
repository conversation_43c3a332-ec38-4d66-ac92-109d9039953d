package com.voicehub.backend.controller;

import com.voicehub.backend.entity.AudioFile;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.service.AudioFileService;
import com.voicehub.backend.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 音频文件控制器
 */
@RestController
@RequestMapping("/audio-files")
@Tag(name = "音频文件", description = "音频文件管理API")
public class AudioFileController {

    @Autowired
    private AudioFileService audioFileService;

    /**
     * 上传音频文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传音频文件", description = "上传音频文件到服务器")
    public ResponseEntity<ApiResponse<AudioFile>> uploadAudioFile(
            @AuthenticationPrincipal User currentUser,
            @RequestParam("file") @Parameter(description = "音频文件") MultipartFile file,
            @RequestParam(value = "source", defaultValue = "WEB") @Parameter(description = "上传来源") String uploadSource) {
        
        try {
            AudioFile audioFile = audioFileService.uploadAudioFile(currentUser.getId(), file, uploadSource);
            return ResponseEntity.ok(ApiResponse.success(audioFile, "文件上传成功"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("文件验证失败: " + e.getMessage()));
        } catch (IOException e) {
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的音频文件列表
     */
    @GetMapping("/my-files")
    @Operation(summary = "获取我的音频文件", description = "获取当前用户的所有音频文件")
    public ResponseEntity<ApiResponse<List<AudioFile>>> getMyAudioFiles(
            @AuthenticationPrincipal User currentUser) {
        
        List<AudioFile> audioFiles = audioFileService.getByUserId(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(audioFiles));
    }

    /**
     * 根据ID获取音频文件信息
     */
    @GetMapping("/{fileId}")
    @Operation(summary = "获取音频文件信息", description = "根据ID获取音频文件的详细信息")
    public ResponseEntity<ApiResponse<AudioFile>> getAudioFileById(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId) {
        
        AudioFile audioFile = audioFileService.getById(fileId);
        
        if (audioFile == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 检查文件所有权
        if (!audioFile.getUserId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        return ResponseEntity.ok(ApiResponse.success(audioFile));
    }

    /**
     * 下载音频文件
     */
    @GetMapping("/{fileId}/download")
    @Operation(summary = "下载音频文件", description = "下载指定的音频文件")
    public ResponseEntity<Resource> downloadAudioFile(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId) {
        
        AudioFile audioFile = audioFileService.getById(fileId);
        
        if (audioFile == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 检查文件所有权
        if (!audioFile.getUserId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
            return ResponseEntity.status(403).build();
        }
        
        try {
            Path filePath = Paths.get(audioFile.getFilePath());
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(audioFile.getMimeType()))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                                "attachment; filename=\"" + audioFile.getOriginalName() + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (MalformedURLException e) {
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 更新音频文件元数据
     */
    @PutMapping("/{fileId}/metadata")
    @Operation(summary = "更新音频元数据", description = "更新音频文件的元数据信息")
    public ResponseEntity<ApiResponse<String>> updateAudioMetadata(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId,
            @RequestParam(required = false) @Parameter(description = "时长（秒）") Integer durationSeconds,
            @RequestParam(required = false) @Parameter(description = "采样率") Integer sampleRate,
            @RequestParam(required = false) @Parameter(description = "比特率") Integer bitRate,
            @RequestParam(required = false) @Parameter(description = "声道数") Integer channels) {
        
        AudioFile audioFile = audioFileService.getById(fileId);
        
        if (audioFile == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 检查文件所有权
        if (!audioFile.getUserId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = audioFileService.updateAudioMetadata(fileId, durationSeconds, sampleRate, bitRate, channels);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("元数据更新成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新元数据失败"));
        }
    }

    /**
     * 更新处理状态
     */
    @PutMapping("/{fileId}/processing-status")
    @Operation(summary = "更新处理状态", description = "更新音频文件的处理状态")
    public ResponseEntity<ApiResponse<String>> updateProcessingStatus(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId,
            @RequestParam @Parameter(description = "处理状态") String status) {
        
        // 只有管理员可以更新处理状态
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = audioFileService.updateProcessingStatus(fileId, status);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("处理状态更新成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新处理状态失败"));
        }
    }

    /**
     * 更新转写状态
     */
    @PutMapping("/{fileId}/transcription-status")
    @Operation(summary = "更新转写状态", description = "更新音频文件的转写状态")
    public ResponseEntity<ApiResponse<String>> updateTranscriptionStatus(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId,
            @RequestParam @Parameter(description = "转写状态") String status) {
        
        // 只有管理员可以更新转写状态
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = audioFileService.updateTranscriptionStatus(fileId, status);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("转写状态更新成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新转写状态失败"));
        }
    }

    /**
     * 删除音频文件
     */
    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除音频文件", description = "删除指定的音频文件")
    public ResponseEntity<ApiResponse<String>> deleteAudioFile(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "文件ID") Long fileId) {
        
        AudioFile audioFile = audioFileService.getById(fileId);
        
        if (audioFile == null) {
            return ResponseEntity.notFound().build();
        }
        
        // 检查文件所有权
        if (!audioFile.getUserId().equals(currentUser.getId()) && !currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = audioFileService.deleteAudioFile(fileId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("文件删除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除文件失败"));
        }
    }

    /**
     * 获取用户音频文件统计
     */
    @GetMapping("/my-stats")
    @Operation(summary = "获取我的音频统计", description = "获取当前用户的音频文件统计信息")
    public ResponseEntity<ApiResponse<AudioFileService.AudioFileStats>> getMyAudioStats(
            @AuthenticationPrincipal User currentUser) {
        
        AudioFileService.AudioFileStats stats = audioFileService.getUserAudioStats(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取待处理的音频文件（管理员功能）
     */
    @GetMapping("/pending")
    @Operation(summary = "获取待处理文件", description = "获取待处理的音频文件列表（管理员功能）")
    public ResponseEntity<ApiResponse<List<AudioFile>>> getPendingFiles(
            @AuthenticationPrincipal User currentUser,
            @RequestParam(defaultValue = "10") @Parameter(description = "限制数量") int limit) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        List<AudioFile> pendingFiles = audioFileService.getPendingFiles(limit);
        return ResponseEntity.ok(ApiResponse.success(pendingFiles));
    }

    /**
     * 获取待转写的音频文件（管理员功能）
     */
    @GetMapping("/pending-transcription")
    @Operation(summary = "获取待转写文件", description = "获取待转写的音频文件列表（管理员功能）")
    public ResponseEntity<ApiResponse<List<AudioFile>>> getPendingTranscriptionFiles(
            @AuthenticationPrincipal User currentUser,
            @RequestParam(defaultValue = "10") @Parameter(description = "限制数量") int limit) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        List<AudioFile> pendingFiles = audioFileService.getPendingTranscriptionFiles(limit);
        return ResponseEntity.ok(ApiResponse.success(pendingFiles));
    }
}
