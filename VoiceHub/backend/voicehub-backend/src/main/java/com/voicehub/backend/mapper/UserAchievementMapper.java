package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.UserAchievement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户成就Mapper接口
 */
@Mapper
public interface UserAchievementMapper extends BaseMapper<UserAchievement> {

    /**
     * 根据用户ID查找成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE user_id = #{userId} ORDER BY unlocked_at DESC")
    List<UserAchievement> findByUserId(@Param("userId") Long userId);

    /**
     * 根据成就类型查找成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE achievement_type = #{achievementType}")
    List<UserAchievement> findByAchievementType(@Param("achievementType") String achievementType);

    /**
     * 查找用户的特定类型成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE user_id = #{userId} AND achievement_type = #{achievementType}")
    List<UserAchievement> findByUserIdAndAchievementType(@Param("userId") Long userId, @Param("achievementType") String achievementType);

    /**
     * 检查用户是否已获得特定成就
     */
    @Select("SELECT COUNT(*) > 0 FROM voicehub.user_achievements WHERE user_id = #{userId} AND achievement_type = #{achievementType}")
    boolean existsByUserIdAndAchievementType(@Param("userId") Long userId, @Param("achievementType") String achievementType);

    /**
     * 根据时间范围查找成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE unlocked_at BETWEEN #{startDate} AND #{endDate} ORDER BY unlocked_at DESC")
    List<UserAchievement> findByUnlockedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户在时间范围内获得的成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE user_id = #{userId} AND unlocked_at BETWEEN #{startDate} AND #{endDate} ORDER BY unlocked_at DESC")
    List<UserAchievement> findByUserIdAndUnlockedAtBetween(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户的成就总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.user_achievements WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总积分
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM voicehub.user_achievements WHERE user_id = #{userId}")
    long sumPointsByUserId(@Param("userId") Long userId);

    /**
     * 查找用户最近获得的成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE user_id = #{userId} ORDER BY unlocked_at DESC LIMIT #{limit}")
    List<UserAchievement> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查找积分最高的成就
     */
    @Select("SELECT * FROM voicehub.user_achievements WHERE user_id = #{userId} ORDER BY points DESC LIMIT #{limit}")
    List<UserAchievement> findTopPointsByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 统计特定类型成就的获得人数
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM voicehub.user_achievements WHERE achievement_type = #{achievementType}")
    long countUsersByAchievementType(@Param("achievementType") String achievementType);

    /**
     * 查找今日获得成就的用户
     */
    @Select("SELECT DISTINCT user_id FROM voicehub.user_achievements WHERE DATE(unlocked_at) = CURRENT_DATE")
    List<Long> findTodayAchievedUserIds();
}
