package com.voicehub.backend.exception;

/**
 * 资源未找到异常类
 * 用于处理资源不存在的情况
 * 
 * <AUTHOR> Team
 */
public class ResourceNotFoundException extends RuntimeException {

    /**
     * 构造函数
     * @param message 异常消息
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * @param message 异常消息
     * @param cause 原因异常
     */
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    // 静态工厂方法

    /**
     * 创建用户未找到异常
     * @param userId 用户ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException user(Long userId) {
        return new ResourceNotFoundException("用户不存在，ID: " + userId);
    }

    /**
     * 创建用户未找到异常
     * @param username 用户名
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException user(String username) {
        return new ResourceNotFoundException("用户不存在，用户名: " + username);
    }

    /**
     * 创建对话未找到异常
     * @param conversationId 对话ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException conversation(Long conversationId) {
        return new ResourceNotFoundException("对话不存在，ID: " + conversationId);
    }

    /**
     * 创建语音笔记未找到异常
     * @param voiceNoteId 语音笔记ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException voiceNote(Long voiceNoteId) {
        return new ResourceNotFoundException("语音笔记不存在，ID: " + voiceNoteId);
    }

    /**
     * 创建音频文件未找到异常
     * @param audioFileId 音频文件ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException audioFile(Long audioFileId) {
        return new ResourceNotFoundException("音频文件不存在，ID: " + audioFileId);
    }

    /**
     * 创建日程未找到异常
     * @param scheduleId 日程ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException schedule(Long scheduleId) {
        return new ResourceNotFoundException("日程不存在，ID: " + scheduleId);
    }

    /**
     * 创建聊天消息未找到异常
     * @param messageId 消息ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException chatMessage(Long messageId) {
        return new ResourceNotFoundException("聊天消息不存在，ID: " + messageId);
    }

    /**
     * 创建通用资源未找到异常
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @return 资源未找到异常
     */
    public static ResourceNotFoundException resource(String resourceType, Object resourceId) {
        return new ResourceNotFoundException(resourceType + "不存在，ID: " + resourceId);
    }
}
