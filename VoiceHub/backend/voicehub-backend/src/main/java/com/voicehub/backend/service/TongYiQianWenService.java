package com.voicehub.backend.service;

import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 通义千问AI服务
 * 集成阿里云通义千问大模型API
 * 
 * <AUTHOR> Team
 */
@Service
public class TongYiQianWenService {

    private static final Logger logger = LoggerFactory.getLogger(TongYiQianWenService.class);

    @Value("${voicehub.ai.tongyi.api-key:}")
    private String apiKey;

    @Value("${voicehub.ai.tongyi.base-url:https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation}")
    private String baseUrl;

    @Value("${voicehub.ai.tongyi.enabled:false}")
    private boolean enabled;

    @Value("${voicehub.ai.tongyi.timeout:30000}")
    private int timeout;

    private final RestTemplate restTemplate;

    public TongYiQianWenService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 生成AI响应
     * 
     * @param conversation 对话上下文
     * @param prompt 用户输入
     * @param modelId 模型ID
     * @return AI响应
     */
    public String generateResponse(Conversation conversation, String prompt, String modelId) {
        if (!enabled) {
            throw BusinessException.internalError("通义千问服务未启用");
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw BusinessException.internalError("通义千问API密钥未配置");
        }

        logger.info("调用通义千问API: model={}, promptLength={}", modelId, prompt.length());

        try {
            // 构建请求
            Map<String, Object> request = buildRequest(conversation, prompt, modelId);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("X-DashScope-SSE", "disable"); // 禁用流式响应
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(baseUrl, entity, Map.class);
            
            // 解析响应
            return parseResponse(response.getBody());
            
        } catch (Exception e) {
            logger.error("通义千问API调用失败: {}", e.getMessage());
            throw BusinessException.internalError("通义千问服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI响应（使用默认模型）
     */
    public String generateResponse(Conversation conversation, String prompt) {
        return generateResponse(conversation, prompt, "qwen-turbo");
    }

    /**
     * 构建API请求
     */
    private Map<String, Object> buildRequest(Conversation conversation, String prompt, String modelId) {
        Map<String, Object> request = new HashMap<>();
        
        // 模型参数
        request.put("model", modelId);
        
        // 输入参数
        Map<String, Object> input = new HashMap<>();
        
        // 构建消息列表
        List<Map<String, Object>> messages = new ArrayList<>();
        
        // 系统消息
        if (conversation != null) {
            String systemPrompt = buildSystemPrompt(conversation);
            if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                Map<String, Object> systemMessage = new HashMap<>();
                systemMessage.put("role", "system");
                systemMessage.put("content", systemPrompt);
                messages.add(systemMessage);
            }
        }
        
        // 用户消息
        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", prompt);
        messages.add(userMessage);
        
        input.put("messages", messages);
        request.put("input", input);
        
        // 参数设置
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("max_tokens", 2000);
        parameters.put("temperature", 0.7);
        parameters.put("top_p", 0.9);
        parameters.put("repetition_penalty", 1.1);
        request.put("parameters", parameters);
        
        return request;
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(Conversation conversation) {
        if (conversation == null) {
            return "你是一个智能助手，请用中文回答用户的问题，回答要准确、有帮助且友善。";
        }
        
        switch (conversation.getType()) {
            case EMOTIONAL_SUPPORT:
                return "你是一个温暖、理解和支持的心理健康助手。请以同理心倾听用户的情感需求，提供积极正面的支持和建议。用温和、鼓励的语调回应，避免给出专业医疗建议。";
                
            case SCHEDULE_HELP:
                return "你是一个高效的日程管理助手。帮助用户规划时间、安排任务、提醒重要事项。回答要简洁明了，提供实用的时间管理建议。";
                
            case NOTE_ASSISTANCE:
                return "你是一个专业的笔记整理助手。帮助用户整理、总结和优化笔记内容。注重信息的结构化和条理性，提供清晰的格式建议。";
                
            case BRAINSTORMING:
                return "你是一个富有创意的头脑风暴伙伴。鼓励发散思维，提供多角度的创意想法和解决方案。保持开放和积极的态度，激发用户的创造力。";
                
            case PROBLEM_SOLVING:
                return "你是一个逻辑清晰的问题解决专家。分析问题的根本原因，提供系统性的解决方案。回答要有逻辑性，步骤清晰，便于执行。";
                
            case CASUAL_CHAT:
                return "你是一个友善的聊天伙伴。进行自然、愉快的对话，保持有趣和有帮助。要亲切和引人入胜。";
                
            default:
                return "你是一个智能助手，请用中文回答用户的问题，回答要准确、有帮助且友善。";
        }
    }

    /**
     * 解析API响应
     */
    private String parseResponse(Map<String, Object> responseBody) {
        if (responseBody == null) {
            throw BusinessException.internalError("通义千问API返回空响应");
        }
        
        // 检查错误
        if (responseBody.containsKey("code")) {
            String code = (String) responseBody.get("code");
            String message = (String) responseBody.get("message");
            throw BusinessException.internalError("通义千问API错误: " + code + " - " + message);
        }
        
        // 解析输出
        @SuppressWarnings("unchecked")
        Map<String, Object> output = (Map<String, Object>) responseBody.get("output");
        if (output == null) {
            throw BusinessException.internalError("通义千问API响应格式错误：缺少output字段");
        }
        
        String text = (String) output.get("text");
        if (text == null || text.trim().isEmpty()) {
            throw BusinessException.internalError("通义千问API返回空文本");
        }
        
        return text.trim();
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return enabled && apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 检查Plus版本是否可用
     */
    public boolean isPlusAvailable() {
        // 这里可以添加更具体的Plus版本检查逻辑
        return isAvailable();
    }

    /**
     * 测试API连接
     */
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        
        if (!isAvailable()) {
            result.put("available", false);
            result.put("error", "服务未启用或API密钥未配置");
            return result;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            String response = generateResponse(null, "你好");
            long responseTime = System.currentTimeMillis() - startTime;
            
            result.put("available", true);
            result.put("responseTime", responseTime);
            result.put("testResponse", response.substring(0, Math.min(50, response.length())));
            
        } catch (Exception e) {
            result.put("available", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取支持的模型列表
     */
    public List<Map<String, Object>> getSupportedModels() {
        List<Map<String, Object>> models = new ArrayList<>();
        
        Map<String, Object> qwenTurbo = new HashMap<>();
        qwenTurbo.put("id", "qwen-turbo");
        qwenTurbo.put("name", "通义千问-Turbo");
        qwenTurbo.put("description", "快速响应，适合日常对话");
        qwenTurbo.put("maxTokens", 2000);
        qwenTurbo.put("costPer1kTokens", 0.001);
        models.add(qwenTurbo);
        
        Map<String, Object> qwenPlus = new HashMap<>();
        qwenPlus.put("id", "qwen-plus");
        qwenPlus.put("name", "通义千问-Plus");
        qwenPlus.put("description", "增强版本，推理能力更强");
        qwenPlus.put("maxTokens", 4000);
        qwenPlus.put("costPer1kTokens", 0.008);
        models.add(qwenPlus);
        
        Map<String, Object> qwenMax = new HashMap<>();
        qwenMax.put("id", "qwen-max");
        qwenMax.put("name", "通义千问-Max");
        qwenMax.put("description", "最强版本，适合复杂任务");
        qwenMax.put("maxTokens", 8000);
        qwenMax.put("costPer1kTokens", 0.02);
        models.add(qwenMax);
        
        return models;
    }

    /**
     * 获取使用统计
     */
    public Map<String, Object> getUsageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里应该从数据库查询实际统计数据
        stats.put("totalRequests", 89);
        stats.put("successfulRequests", 86);
        stats.put("failedRequests", 3);
        stats.put("averageResponseTime", 1600);
        stats.put("totalTokensUsed", 45230);
        stats.put("totalCost", 0.45);
        stats.put("lastUsed", new Date());
        
        return stats;
    }

    /**
     * 生成情感支持响应
     */
    public String generateEmotionalResponse(String userMessage, String emotionType) {
        String systemPrompt = "你是一个专业的情感支持助手。请根据用户的情感状态提供温暖、理解和支持的回应。";
        
        if ("supportive".equals(emotionType)) {
            systemPrompt += "请特别注重给予用户鼓励和正面支持。";
        } else if ("empathetic".equals(emotionType)) {
            systemPrompt += "请特别注重理解和共情用户的感受。";
        }
        
        // 构建特殊的情感支持请求
        String enhancedPrompt = systemPrompt + "\n\n用户说：" + userMessage + "\n\n请给出温暖、支持性的回应：";
        
        return generateResponse(null, enhancedPrompt);
    }
}
