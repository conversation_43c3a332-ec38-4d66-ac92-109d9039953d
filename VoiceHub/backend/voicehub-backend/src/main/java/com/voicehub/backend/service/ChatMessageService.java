package com.voicehub.backend.service;

import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.mapper.ChatMessageMapper;
import com.voicehub.backend.mapper.ConversationMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息服务
 * 处理AI对话消息的发送、接收和AI回复生成
 * 
 * <AUTHOR> Team
 */
@Service
@Transactional
public class ChatMessageService {

    private static final Logger logger = LoggerFactory.getLogger(ChatMessageService.class);

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private OpenAIService openAIService;

    /**
     * 发送消息并获取AI回复
     * 完整的对话流程：保存用户消息 -> 生成AI回复 -> 保存AI回复 -> 更新对话统计
     * 
     * @param conversationId 对话ID
     * @param content 消息内容
     * @param user 发送用户
     * @param isVoice 是否为语音消息
     * @param voiceFilePath 语音文件路径（如果是语音消息）
     * @return AI回复消息
     */
    public ChatMessage sendMessageAndGetReply(Long conversationId, String content, User user, 
                                            boolean isVoice, String voiceFilePath) {
        // 1. 验证对话是否存在
        Conversation conversation = conversationMapper.selectById(conversationId);
        if (conversation == null) {
            throw ResourceNotFoundException.conversation(conversationId);
        }

        // 2. 验证用户权限
        if (!conversation.getUserId().equals(user.getId())) {
            throw BusinessException.forbidden("无权访问此对话");
        }

        try {
            // 3. 保存用户消息
            ChatMessage userMessage = saveUserMessage(conversationId, content, user, isVoice, voiceFilePath);
            
            // 4. 获取对话历史（用于AI上下文）
            List<ChatMessage> conversationHistory = getRecentMessages(conversationId, 10);
            
            // 5. 生成AI回复
            String aiResponse = generateAIResponse(conversation, conversationHistory, content);
            
            // 6. 保存AI回复消息
            ChatMessage aiMessage = saveAIMessage(conversationId, aiResponse, "gpt-3.5-turbo");
            
            // 7. 更新对话统计信息
            updateConversationStats(conversation, 2); // 用户消息 + AI回复
            
            logger.info("成功处理对话消息: conversationId={}, userId={}", conversationId, user.getId());
            return aiMessage;
            
        } catch (Exception e) {
            logger.error("处理对话消息失败: conversationId={}, error={}", conversationId, e.getMessage());
            throw BusinessException.internalError("AI回复生成失败: " + e.getMessage());
        }
    }

    /**
     * 保存用户消息
     * 
     * @param conversationId 对话ID
     * @param content 消息内容
     * @param user 用户
     * @param isVoice 是否为语音消息
     * @param voiceFilePath 语音文件路径
     * @return 保存的用户消息
     */
    private ChatMessage saveUserMessage(Long conversationId, String content, User user, 
                                      boolean isVoice, String voiceFilePath) {
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setRole(ChatMessage.MessageRole.USER);
        message.setType(isVoice ? ChatMessage.MessageType.VOICE : ChatMessage.MessageType.TEXT);
        message.setIsVoiceInput(isVoice);
        message.setVoiceFilePath(voiceFilePath);
        message.setCreatedAt(LocalDateTime.now());
        
        chatMessageMapper.insert(message);
        return message;
    }

    /**
     * 保存AI回复消息
     * 
     * @param conversationId 对话ID
     * @param content AI回复内容
     * @param aiModel AI模型名称
     * @return 保存的AI消息
     */
    private ChatMessage saveAIMessage(Long conversationId, String content, String aiModel) {
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setRole(ChatMessage.MessageRole.ASSISTANT);
        message.setType(ChatMessage.MessageType.TEXT);
        message.setIsVoiceInput(false);
        // message.setAiModel(aiModel != null ? aiModel : "gpt-3.5-turbo"); // 暂时注释，等实体类添加字段
        message.setCreatedAt(LocalDateTime.now());
        
        chatMessageMapper.insert(message);
        return message;
    }

    /**
     * 获取最近的对话消息（用于AI上下文）
     * 
     * @param conversationId 对话ID
     * @param limit 消息数量限制
     * @return 最近的消息列表
     */
    private List<ChatMessage> getRecentMessages(Long conversationId, int limit) {
        return chatMessageMapper.findRecentByConversationId(conversationId, limit);
    }

    /**
     * 生成AI回复
     * 
     * @param conversation 对话信息
     * @param conversationHistory 对话历史
     * @param userMessage 用户消息
     * @return AI回复内容
     */
    private String generateAIResponse(Conversation conversation, List<ChatMessage> conversationHistory, 
                                    String userMessage) {
        try {
            // 根据对话类型选择不同的AI处理策略
            switch (conversation.getType()) {
                case EMOTIONAL_SUPPORT:
                    return openAIService.generateResponse(conversation, "请以温暖、支持的语气回复：" + userMessage);
                case SCHEDULE_HELP:
                    return generateScheduleHelperResponse(conversation, userMessage);
                case NOTE_ASSISTANCE:
                    return generateNoteAssistantResponse(conversation, userMessage);
                case BRAINSTORMING:
                    return generateBrainstormingResponse(conversation, userMessage);
                case PROBLEM_SOLVING:
                    return generateProblemSolvingResponse(conversation, userMessage);
                default:
                    return openAIService.generateResponse(conversation, userMessage);
            }
        } catch (Exception e) {
            logger.error("AI回复生成失败: {}", e.getMessage());
            return "抱歉，我现在无法处理您的请求，请稍后再试。";
        }
    }

    /**
     * 生成日程助手回复
     */
    private String generateScheduleHelperResponse(Conversation conversation, String userMessage) {
        // 这里可以集成日程管理逻辑
        return openAIService.generateResponse(conversation, "作为日程助手，请帮助用户处理以下请求：" + userMessage);
    }

    /**
     * 生成笔记助手回复
     */
    private String generateNoteAssistantResponse(Conversation conversation, String userMessage) {
        // 这里可以集成笔记管理逻辑
        return openAIService.generateResponse(conversation, "作为笔记助手，请帮助用户整理和管理以下内容：" + userMessage);
    }

    /**
     * 生成头脑风暴回复
     */
    private String generateBrainstormingResponse(Conversation conversation, String userMessage) {
        return openAIService.generateResponse(conversation, "让我们一起进行头脑风暴，针对以下话题提供创意想法：" + userMessage);
    }

    /**
     * 生成问题解决回复
     */
    private String generateProblemSolvingResponse(Conversation conversation, String userMessage) {
        return openAIService.generateResponse(conversation, "让我帮您分析和解决以下问题：" + userMessage);
    }

    /**
     * 更新对话统计信息
     * 
     * @param conversation 对话对象
     * @param messageCount 新增消息数量
     */
    private void updateConversationStats(Conversation conversation, int messageCount) {
        conversation.setMessageCount(conversation.getMessageCount() + messageCount);
        conversation.setLastActivityAt(LocalDateTime.now());
        conversation.setUpdatedAt(LocalDateTime.now());
        conversationMapper.updateById(conversation);
    }

    /**
     * 获取对话中的所有消息
     * 
     * @param conversationId 对话ID
     * @param userId 用户ID（用于权限验证）
     * @return 消息列表
     */
    public List<ChatMessage> getConversationMessages(Long conversationId, Long userId) {
        // 验证用户权限
        Conversation conversation = conversationMapper.selectById(conversationId);
        if (conversation == null) {
            throw ResourceNotFoundException.conversation(conversationId);
        }
        
        if (!conversation.getUserId().equals(userId)) {
            throw BusinessException.forbidden("无权访问此对话");
        }
        
        return chatMessageMapper.findByConversationIdOrderByCreatedAt(conversationId);
    }
}
