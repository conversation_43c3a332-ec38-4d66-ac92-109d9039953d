package com.voicehub.backend.controller;

import com.voicehub.backend.entity.Notification;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.service.NotificationService;
import com.voicehub.backend.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通知控制器
 * 处理用户通知的创建、查询、标记已读、删除等功能
 */
@RestController
@RequestMapping("/notifications")
@Tag(name = "通知管理", description = "用户通知管理API")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    /**
     * 获取当前用户的通知列表
     *
     * @param currentUser 当前认证用户
     * @return 返回通知列表
     */
    @GetMapping("/my-notifications")
    @Operation(summary = "获取我的通知", description = "获取当前用户的所有通知")
    public ResponseEntity<ApiResponse<List<Notification>>> getMyNotifications(
            @AuthenticationPrincipal User currentUser) {
        
        List<Notification> notifications = notificationService.getByUserId(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    /**
     * 获取当前用户的未读通知
     *
     * @param currentUser 当前认证用户
     * @return 返回未读通知列表
     */
    @GetMapping("/my-notifications/unread")
    @Operation(summary = "获取未读通知", description = "获取当前用户的未读通知")
    public ResponseEntity<ApiResponse<List<Notification>>> getUnreadNotifications(
            @AuthenticationPrincipal User currentUser) {
        
        List<Notification> notifications = notificationService.getUnreadByUserId(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    /**
     * 获取当前用户的已读通知
     *
     * @param currentUser 当前认证用户
     * @return 返回已读通知列表
     */
    @GetMapping("/my-notifications/read")
    @Operation(summary = "获取已读通知", description = "获取当前用户的已读通知")
    public ResponseEntity<ApiResponse<List<Notification>>> getReadNotifications(
            @AuthenticationPrincipal User currentUser) {
        
        List<Notification> notifications = notificationService.getReadByUserId(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    /**
     * 根据类型获取通知
     *
     * @param currentUser 当前认证用户
     * @param type 通知类型
     * @return 返回指定类型的通知列表
     */
    @GetMapping("/my-notifications/type/{type}")
    @Operation(summary = "根据类型获取通知", description = "根据通知类型获取当前用户的通知")
    public ResponseEntity<ApiResponse<List<Notification>>> getNotificationsByType(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "通知类型") String type) {
        
        List<Notification> notifications = notificationService.getByUserIdAndType(currentUser.getId(), type);
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    /**
     * 根据优先级获取通知
     *
     * @param currentUser 当前认证用户
     * @param priority 通知优先级
     * @return 返回指定优先级的通知列表
     */
    @GetMapping("/my-notifications/priority/{priority}")
    @Operation(summary = "根据优先级获取通知", description = "根据优先级获取当前用户的通知")
    public ResponseEntity<ApiResponse<List<Notification>>> getNotificationsByPriority(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "通知优先级") String priority) {
        
        List<Notification> notifications = notificationService.getByUserIdAndPriority(currentUser.getId(), priority);
        return ResponseEntity.ok(ApiResponse.success(notifications));
    }

    /**
     * 标记通知为已读
     *
     * @param currentUser 当前认证用户
     * @param notificationId 通知ID
     * @return 标记结果
     */
    @PutMapping("/{notificationId}/read")
    @Operation(summary = "标记通知为已读", description = "将指定通知标记为已读")
    public ResponseEntity<ApiResponse<String>> markAsRead(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "通知ID") Long notificationId) {
        
        // 验证通知所有权
        Notification notification = notificationService.getById(notificationId);
        if (notification == null) {
            return ResponseEntity.notFound().build();
        }
        
        if (!notification.getUserId().equals(currentUser.getId())) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = notificationService.markAsRead(notificationId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("通知已标记为已读"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("标记失败"));
        }
    }

    /**
     * 标记所有通知为已读
     *
     * @param currentUser 当前认证用户
     * @return 标记结果
     */
    @PutMapping("/mark-all-read")
    @Operation(summary = "标记所有通知为已读", description = "将当前用户的所有通知标记为已读")
    public ResponseEntity<ApiResponse<String>> markAllAsRead(
            @AuthenticationPrincipal User currentUser) {
        
        boolean success = notificationService.markAllAsRead(currentUser.getId());
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("所有通知已标记为已读"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("标记失败"));
        }
    }

    /**
     * 标记特定类型通知为已读
     *
     * @param currentUser 当前认证用户
     * @param type 通知类型
     * @return 标记结果
     */
    @PutMapping("/mark-read-by-type/{type}")
    @Operation(summary = "按类型标记已读", description = "将当前用户的特定类型通知标记为已读")
    public ResponseEntity<ApiResponse<String>> markAsReadByType(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "通知类型") String type) {
        
        boolean success = notificationService.markAsReadByType(currentUser.getId(), type);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("指定类型通知已标记为已读"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("标记失败"));
        }
    }

    /**
     * 获取未读通知数量
     *
     * @param currentUser 当前认证用户
     * @return 未读通知数量
     */
    @GetMapping("/unread-count")
    @Operation(summary = "获取未读通知数量", description = "获取当前用户的未读通知数量")
    public ResponseEntity<ApiResponse<Long>> getUnreadCount(
            @AuthenticationPrincipal User currentUser) {
        
        long count = notificationService.getUnreadCount(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    /**
     * 获取通知统计
     *
     * @param currentUser 当前认证用户
     * @return 通知统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取通知统计", description = "获取当前用户的通知统计信息")
    public ResponseEntity<ApiResponse<NotificationService.NotificationStats>> getNotificationStats(
            @AuthenticationPrincipal User currentUser) {
        
        NotificationService.NotificationStats stats = notificationService.getUserNotificationStats(currentUser.getId());
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 删除通知
     *
     * @param currentUser 当前认证用户
     * @param notificationId 通知ID
     * @return 删除结果
     */
    @DeleteMapping("/{notificationId}")
    @Operation(summary = "删除通知", description = "删除指定的通知")
    public ResponseEntity<ApiResponse<String>> deleteNotification(
            @AuthenticationPrincipal User currentUser,
            @PathVariable @Parameter(description = "通知ID") Long notificationId) {
        
        // 验证通知所有权
        Notification notification = notificationService.getById(notificationId);
        if (notification == null) {
            return ResponseEntity.notFound().build();
        }
        
        if (!notification.getUserId().equals(currentUser.getId())) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        boolean success = notificationService.deleteNotification(notificationId);
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("通知删除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除失败"));
        }
    }

    /**
     * 批量删除已读通知
     *
     * @param currentUser 当前认证用户
     * @return 删除结果
     */
    @DeleteMapping("/read-notifications")
    @Operation(summary = "删除已读通知", description = "批量删除当前用户的已读通知")
    public ResponseEntity<ApiResponse<String>> deleteReadNotifications(
            @AuthenticationPrincipal User currentUser) {
        
        boolean success = notificationService.deleteReadNotifications(currentUser.getId());
        
        if (success) {
            return ResponseEntity.ok(ApiResponse.success("已读通知删除成功"));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("删除失败"));
        }
    }

    /**
     * 创建通知（管理员功能）
     * 管理员向指定用户发送通知消息
     *
     * @param currentUser 当前认证用户（需为管理员）
     * @param targetUserId 目标用户ID
     * @param type 通知类型
     * @param title 通知标题
     * @param message 通知内容
     * @param priority 通知优先级
     * @param data 附加数据
     * @return 创建的通知信息
     */
    @PostMapping("/create")
    @Operation(summary = "创建通知", description = "创建新通知（管理员功能）")
    public ResponseEntity<ApiResponse<Notification>> createNotification(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "目标用户ID") Long targetUserId,
            @RequestParam @Parameter(description = "通知类型") String type,
            @RequestParam @Parameter(description = "通知标题") String title,
            @RequestParam @Parameter(description = "通知内容") String message,
            @RequestParam(required = false) @Parameter(description = "优先级") String priority,
            @RequestBody(required = false) Map<String, Object> data) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        Notification notification = notificationService.createNotification(
                targetUserId, type, title, message, data, priority, null, null);
        
        return ResponseEntity.ok(ApiResponse.success(notification, "通知创建成功"));
    }

    /**
     * 创建系统通知（管理员功能）
     * 管理员向所有用户发送系统通知消息
     *
     * @param currentUser 当前认证用户（需为管理员）
     * @param title 通知标题
     * @param message 通知内容
     * @param priority 通知优先级
     * @return 创建的系统通知信息
     */
    @PostMapping("/system")
    @Operation(summary = "创建系统通知", description = "创建系统通知（管理员功能）")
    public ResponseEntity<ApiResponse<Notification>> createSystemNotification(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "通知标题") String title,
            @RequestParam @Parameter(description = "通知内容") String message,
            @RequestParam(required = false) @Parameter(description = "优先级") String priority) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        Notification notification = notificationService.createSystemNotification(title, message, priority, null);
        
        return ResponseEntity.ok(ApiResponse.success(notification, "系统通知创建成功"));
    }

    /**
     * 创建提醒通知（管理员功能）
     * 管理员向指定用户发送定时提醒通知
     *
     * @param currentUser 当前认证用户（需为管理员）
     * @param targetUserId 目标用户ID
     * @param title 提醒标题
     * @param message 提醒内容
     * @param scheduledFor 计划提醒时间
     * @return 创建的提醒通知信息
     */
    @PostMapping("/reminder")
    @Operation(summary = "创建提醒通知", description = "创建提醒通知（管理员功能）")
    public ResponseEntity<ApiResponse<Notification>> createReminderNotification(
            @AuthenticationPrincipal User currentUser,
            @RequestParam @Parameter(description = "目标用户ID") Long targetUserId,
            @RequestParam @Parameter(description = "提醒标题") String title,
            @RequestParam @Parameter(description = "提醒内容") String message,
            @RequestParam @Parameter(description = "计划时间") String scheduledFor) {
        
        // 检查管理员权限
        if (!currentUser.isAdmin()) {
            return ResponseEntity.status(403)
                    .body(ApiResponse.error("权限不足"));
        }
        
        try {
            LocalDateTime scheduledTime = LocalDateTime.parse(scheduledFor);
            Notification notification = notificationService.createReminderNotification(
                    targetUserId, title, message, scheduledTime);
            
            return ResponseEntity.ok(ApiResponse.success(notification, "提醒通知创建成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("时间格式错误: " + e.getMessage()));
        }
    }


    /**
     * 批量标记通知为已读
     *
     * @param request 请求参数，包含需要标记的通知ID列表
     * @param currentUser 当前认证用户
     * @return 批量标记结果
     */
    @PutMapping("/batch-read")
    @Operation(summary = "批量标记已读", description = "批量将通知标记为已读")
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchMarkAsRead(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User currentUser) {

        @SuppressWarnings("unchecked")
        List<Long> notificationIds = (List<Long>) request.get("notificationIds");

        int updateCount = notificationService.batchMarkAsRead(notificationIds, currentUser);

        Map<String, Object> data = Map.of(
            "updateCount", updateCount,
            "notificationIds", notificationIds
        );

        return ResponseEntity.ok(ApiResponse.success(data, "批量标记已读成功"));
    }

    /**
     * 获取当前用户的详细通知统计信息，包括不同类型通知的分布情况
     *
     * @param currentUser 当前认证用户
     * @return 增强的通知统计信息
     */
    @GetMapping("/enhanced-statistics")
    @Operation(summary = "获取增强统计", description = "获取用户的详细通知统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEnhancedStatistics(
            @AuthenticationPrincipal User currentUser) {

        Map<String, Object> stats = notificationService.getEnhancedNotificationStats(currentUser);

        return ResponseEntity.ok(ApiResponse.success(stats, "获取增强统计成功"));
    }

    /**
     * 发送任务完成通知
     *
     * @param request 请求参数，包含用户ID、任务名称等信息
     * @param currentUser 当前认证用户
     * @return 发送的通知信息
     */
    @PostMapping("/task-complete")
    @Operation(summary = "发送任务完成通知", description = "发送任务完成通知给指定用户")
    public ResponseEntity<ApiResponse<Notification>> sendTaskCompleteNotification(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User currentUser) {

        Long userId = Long.valueOf(request.get("userId").toString());
        String taskName = (String) request.get("taskName");
        String resourceType = (String) request.getOrDefault("resourceType", "task");
        Long resourceId = request.get("resourceId") != null ?
            Long.valueOf(request.get("resourceId").toString()) : null;

        Notification notification = notificationService.sendTaskCompleteNotification(
            userId, taskName, resourceType, resourceId);

        return ResponseEntity.ok(ApiResponse.success(notification, "任务完成通知发送成功"));
    }

    /**
     * 向新用户发送欢迎通知消息
     *
     * @param request 请求参数，包含用户ID和用户名
     * @param currentUser 当前认证用户
     * @return 发送的欢迎通知信息
     */
    @PostMapping("/welcome")
    @Operation(summary = "发送欢迎通知", description = "为新用户发送欢迎通知")
    public ResponseEntity<ApiResponse<Notification>> sendWelcomeNotification(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User currentUser) {

        Long userId = Long.valueOf(request.get("userId").toString());
        String username = (String) request.get("username");

        User newUser = new User();
        newUser.setId(userId);
        newUser.setUsername(username);

        Notification notification = notificationService.sendWelcomeNotification(newUser);

        return ResponseEntity.ok(ApiResponse.success(notification, "欢迎通知发送成功"));
    }

    /**
     * 获取系统支持的所有通知类型及其描述信息
     *
     * @return 通知类型列表
     */
    @GetMapping("/types")
    @Operation(summary = "获取通知类型", description = "获取系统支持的通知类型列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getNotificationTypes() {

        Map<String, Object> types = new java.util.HashMap<>();

        for (Notification.NotificationType type : Notification.NotificationType.values()) {
            Map<String, Object> typeInfo = Map.of(
                "name", type.name(),
                "description", type.getDescription()
            );

            types.put(type.name().toLowerCase(), typeInfo);
        }

        Map<String, Object> data = Map.of(
            "types", types,
            "totalCount", types.size()
        );

        return ResponseEntity.ok(ApiResponse.success(data, "获取通知类型成功"));
    }
}
