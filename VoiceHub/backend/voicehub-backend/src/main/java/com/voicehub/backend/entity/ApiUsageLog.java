package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * API使用日志实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("api_usage_logs")
public class ApiUsageLog extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * API提供商：openai/qianwen/zhipu/baidu
     */
    @TableField("api_provider")
    private String apiProvider;

    /**
     * API端点URL
     */
    @TableField("api_endpoint")
    private String apiEndpoint;

    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 请求类型：chat/speech_recognition/text_processing
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 输入token数量
     */
    @TableField("tokens_input")
    private Integer tokensInput;

    /**
     * 输出token数量
     */
    @TableField("tokens_output")
    private Integer tokensOutput;

    /**
     * 调用成本（美元）
     */
    @TableField("cost_usd")
    private BigDecimal costUsd;

    /**
     * 响应时间（毫秒）
     */
    @TableField("response_time_ms")
    private Long responseTimeMs;

    /**
     * HTTP状态码
     */
    @TableField("status_code")
    private Integer statusCode;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 请求唯一标识
     */
    @TableField("request_id")
    private String requestId;

    public ApiUsageLog() {
        super();
    }

    public ApiUsageLog(Long userId, String apiProvider, String requestType) {
        this();
        this.userId = userId;
        this.apiProvider = apiProvider;
        this.requestType = requestType;
    }
}
