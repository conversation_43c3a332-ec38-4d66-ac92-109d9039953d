package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户设置实体类
 * 存储用户的个性化设置和偏好
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_settings")
public class UserSettings extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * AI模型偏好设置
     */
    @TableField(value = "ai_preferences", typeHandler = JacksonTypeHandler.class)
    private AIPreferences aiPreferences;

    /**
     * 语音识别设置
     */
    @TableField(value = "speech_settings", typeHandler = JacksonTypeHandler.class)
    private SpeechSettings speechSettings;

    /**
     * 通知设置
     */
    @TableField(value = "notification_settings", typeHandler = JacksonTypeHandler.class)
    private NotificationSettings notificationSettings;

    /**
     * 界面设置
     */
    @TableField(value = "ui_settings", typeHandler = JacksonTypeHandler.class)
    private UISettings uiSettings;

    /**
     * 隐私设置
     */
    @TableField(value = "privacy_settings", typeHandler = JacksonTypeHandler.class)
    private PrivacySettings privacySettings;

    /**
     * 扩展设置（JSON格式存储其他设置）
     */
    @TableField(value = "extended_settings", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extendedSettings;

    // 内部类定义各种设置结构

    /**
     * AI模型偏好设置
     */
    @Data
    public static class AIPreferences {
        private String defaultModel = "OPENAI"; // 默认AI模型
        private String fallbackModel = "TONGYI_QIANWEN"; // 备用模型
        private boolean enableAutoSelection = true; // 启用智能模型选择
        private double confidenceThreshold = 0.8; // 置信度阈值
        private int maxTokens = 2000; // 最大token数
        private double temperature = 0.7; // 创造性参数
        private Map<String, String> conversationTypeModels = new HashMap<>(); // 对话类型对应的模型
        private boolean enableCostOptimization = false; // 启用成本优化
        private double maxDailyCost = 10.0; // 每日最大成本限制
    }

    /**
     * 语音识别设置
     */
    @Data
    public static class SpeechSettings {
        private String defaultEngine = "BAIDU"; // 默认识别引擎
        private String language = "zh-CN"; // 识别语言
        private boolean enableEnhancement = true; // 启用音频增强
        private boolean addPunctuation = true; // 自动添加标点符号
        private boolean enableRealtime = false; // 启用实时识别
        private double confidenceThreshold = 0.8; // 置信度阈值
        private boolean autoSave = true; // 自动保存识别结果
        private int maxAudioDuration = 300; // 最大音频时长（秒）
        private String audioFormat = "wav"; // 首选音频格式
        private boolean enableNoiseReduction = true; // 启用降噪
    }

    /**
     * 通知设置
     */
    @Data
    public static class NotificationSettings {
        private boolean enableEmailNotifications = true; // 启用邮件通知
        private boolean enablePushNotifications = true; // 启用推送通知
        private boolean enableSMSNotifications = false; // 启用短信通知
        private boolean notifyOnNewMessage = true; // 新消息通知
        private boolean notifyOnTaskComplete = true; // 任务完成通知
        private boolean notifyOnSystemUpdate = false; // 系统更新通知
        private boolean notifyOnSecurityAlert = true; // 安全警报通知
        private String quietHoursStart = "22:00"; // 免打扰开始时间
        private String quietHoursEnd = "08:00"; // 免打扰结束时间
        private boolean enableQuietHours = false; // 启用免打扰时间
        private int digestFrequency = 24; // 摘要通知频率（小时）
    }

    /**
     * 界面设置
     */
    @Data
    public static class UISettings {
        private String theme = "light"; // 主题：light, dark, auto
        private String language = "zh-CN"; // 界面语言
        private String fontSize = "medium"; // 字体大小：small, medium, large
        private String fontFamily = "default"; // 字体族
        private boolean enableAnimations = true; // 启用动画效果
        private boolean enableSounds = true; // 启用声音效果
        private boolean compactMode = false; // 紧凑模式
        private int itemsPerPage = 20; // 每页显示项目数
        private boolean showTips = true; // 显示提示信息
        private String dateFormat = "YYYY-MM-DD"; // 日期格式
        private String timeFormat = "24h"; // 时间格式：12h, 24h
    }

    /**
     * 隐私设置
     */
    @Data
    public static class PrivacySettings {
        private boolean shareUsageData = false; // 分享使用数据
        private boolean enableAnalytics = true; // 启用分析统计
        private boolean saveConversationHistory = true; // 保存对话历史
        private int historyRetentionDays = 90; // 历史记录保留天数
        private boolean enableDataExport = true; // 启用数据导出
        private boolean enableDataDeletion = true; // 启用数据删除
        private boolean requirePasswordForSensitive = true; // 敏感操作需要密码
        private boolean enableTwoFactorAuth = false; // 启用双因素认证
        private boolean logSecurityEvents = true; // 记录安全事件
        private String dataProcessingConsent = "granted"; // 数据处理同意状态
    }

    // 构造函数
    public UserSettings() {
        this.aiPreferences = new AIPreferences();
        this.speechSettings = new SpeechSettings();
        this.notificationSettings = new NotificationSettings();
        this.uiSettings = new UISettings();
        this.privacySettings = new PrivacySettings();
        this.extendedSettings = new HashMap<>();
    }

    public UserSettings(Long userId) {
        this();
        this.userId = userId;
    }

    /**
     * 获取设置的摘要信息
     */
    public Map<String, Object> getSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("defaultAIModel", aiPreferences.getDefaultModel());
        summary.put("defaultSpeechEngine", speechSettings.getDefaultEngine());
        summary.put("theme", uiSettings.getTheme());
        summary.put("language", uiSettings.getLanguage());
        summary.put("notificationsEnabled", notificationSettings.isEnableEmailNotifications());
        summary.put("privacyLevel", getPrivacyLevel());
        return summary;
    }

    /**
     * 获取隐私级别
     */
    private String getPrivacyLevel() {
        int score = 0;
        if (!privacySettings.isShareUsageData()) score++;
        if (!privacySettings.isEnableAnalytics()) score++;
        if (privacySettings.isRequirePasswordForSensitive()) score++;
        if (privacySettings.isEnableTwoFactorAuth()) score++;
        
        if (score >= 3) return "high";
        if (score >= 2) return "medium";
        return "low";
    }

    /**
     * 验证设置的有效性
     */
    public boolean isValid() {
        // 验证AI设置
        if (aiPreferences.getMaxTokens() < 100 || aiPreferences.getMaxTokens() > 8000) {
            return false;
        }
        if (aiPreferences.getTemperature() < 0 || aiPreferences.getTemperature() > 2) {
            return false;
        }
        
        // 验证语音设置
        if (speechSettings.getMaxAudioDuration() < 10 || speechSettings.getMaxAudioDuration() > 3600) {
            return false;
        }
        
        // 验证隐私设置
        if (privacySettings.getHistoryRetentionDays() < 1 || privacySettings.getHistoryRetentionDays() > 365) {
            return false;
        }
        
        return true;
    }

    /**
     * 重置为默认设置
     */
    public void resetToDefaults() {
        this.aiPreferences = new AIPreferences();
        this.speechSettings = new SpeechSettings();
        this.notificationSettings = new NotificationSettings();
        this.uiSettings = new UISettings();
        this.privacySettings = new PrivacySettings();
        this.extendedSettings = new HashMap<>();
    }

    /**
     * 合并设置（用于部分更新）
     */
    public void mergeSettings(UserSettings other) {
        if (other.getAiPreferences() != null) {
            this.aiPreferences = other.getAiPreferences();
        }
        if (other.getSpeechSettings() != null) {
            this.speechSettings = other.getSpeechSettings();
        }
        if (other.getNotificationSettings() != null) {
            this.notificationSettings = other.getNotificationSettings();
        }
        if (other.getUiSettings() != null) {
            this.uiSettings = other.getUiSettings();
        }
        if (other.getPrivacySettings() != null) {
            this.privacySettings = other.getPrivacySettings();
        }
        if (other.getExtendedSettings() != null && !other.getExtendedSettings().isEmpty()) {
            if (this.extendedSettings == null) {
                this.extendedSettings = new HashMap<>();
            }
            this.extendedSettings.putAll(other.getExtendedSettings());
        }
    }

    /**
     * 获取特定类型的设置
     */
    @SuppressWarnings("unchecked")
    public <T> T getSettingsByType(Class<T> type) {
        if (type == AIPreferences.class) {
            return (T) this.aiPreferences;
        } else if (type == SpeechSettings.class) {
            return (T) this.speechSettings;
        } else if (type == NotificationSettings.class) {
            return (T) this.notificationSettings;
        } else if (type == UISettings.class) {
            return (T) this.uiSettings;
        } else if (type == PrivacySettings.class) {
            return (T) this.privacySettings;
        }
        return null;
    }

    /**
     * 检查是否启用了特定功能
     */
    public boolean isFeatureEnabled(String feature) {
        switch (feature.toLowerCase()) {
            case "auto_ai_selection":
                return aiPreferences.isEnableAutoSelection();
            case "speech_enhancement":
                return speechSettings.isEnableEnhancement();
            case "realtime_speech":
                return speechSettings.isEnableRealtime();
            case "email_notifications":
                return notificationSettings.isEnableEmailNotifications();
            case "push_notifications":
                return notificationSettings.isEnablePushNotifications();
            case "animations":
                return uiSettings.isEnableAnimations();
            case "two_factor_auth":
                return privacySettings.isEnableTwoFactorAuth();
            default:
                return false;
        }
    }
}
