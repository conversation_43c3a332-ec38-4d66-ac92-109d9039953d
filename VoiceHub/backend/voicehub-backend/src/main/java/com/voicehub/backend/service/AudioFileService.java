package com.voicehub.backend.service;

import com.voicehub.backend.entity.AudioFile;
import com.voicehub.backend.mapper.AudioFileMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 音频文件服务实现
 */
@Service
@Transactional
public class AudioFileService extends BaseServiceImpl<AudioFileMapper, AudioFile> {

    @Autowired
    private AudioFileMapper audioFileMapper;

    @Value("${voicehub.file.upload-dir:/tmp/voicehub/audio}")
    private String uploadDir;

    @Value("${voicehub.file.max-size:50MB}")
    private String maxFileSize;

    /**
     * 上传音频文件
     */
    public AudioFile uploadAudioFile(Long userId, MultipartFile file, String uploadSource) throws IOException {
        // 验证文件
        validateAudioFile(file);
        
        // 生成文件名和路径
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String fileName = UUID.randomUUID().toString() + "." + fileExtension;
        String filePath = uploadDir + "/" + userId + "/" + fileName;
        
        // 确保目录存在
        Path uploadPath = Paths.get(uploadDir, userId.toString());
        Files.createDirectories(uploadPath);
        
        // 保存文件
        Path targetPath = uploadPath.resolve(fileName);
        Files.copy(file.getInputStream(), targetPath);
        
        // 计算文件哈希
        String fileHash = calculateFileHash(targetPath);
        
        // 检查是否已存在相同文件
        AudioFile existingFile = audioFileMapper.findByFileHash(fileHash);
        if (existingFile != null) {
            // 删除刚上传的重复文件
            Files.deleteIfExists(targetPath);
            return existingFile;
        }
        
        // 创建音频文件记录
        AudioFile audioFile = new AudioFile();
        audioFile.setUserId(userId);
        audioFile.setFileName(fileName);
        audioFile.setOriginalName(originalFilename);
        audioFile.setFilePath(filePath);
        audioFile.setFileSize(file.getSize());
        audioFile.setMimeType(file.getContentType());
        audioFile.setAudioFormat(fileExtension);
        audioFile.setFileHash(fileHash);
        audioFile.setUploadSource(uploadSource);
        audioFile.setProcessingStatus("PENDING");
        audioFile.setTranscriptionStatus("PENDING");
        audioFile.setCreatedAt(LocalDateTime.now());
        audioFile.setUpdatedAt(LocalDateTime.now());
        
        audioFileMapper.insert(audioFile);
        return audioFile;
    }

    /**
     * 根据用户ID获取音频文件列表
     */
    public List<AudioFile> getByUserId(Long userId) {
        return audioFileMapper.findByUserId(userId);
    }

    /**
     * 根据处理状态获取音频文件
     */
    public List<AudioFile> getByProcessingStatus(String status) {
        return audioFileMapper.findByProcessingStatus(status);
    }

    /**
     * 根据转写状态获取音频文件
     */
    public List<AudioFile> getByTranscriptionStatus(String status) {
        return audioFileMapper.findByTranscriptionStatus(status);
    }

    /**
     * 更新处理状态
     */
    public boolean updateProcessingStatus(Long fileId, String status) {
        AudioFile audioFile = audioFileMapper.selectById(fileId);
        if (audioFile != null) {
            audioFile.setProcessingStatus(status);
            audioFile.setUpdatedAt(LocalDateTime.now());
            audioFileMapper.updateById(audioFile);
            return true;
        }
        return false;
    }

    /**
     * 更新转写状态
     */
    public boolean updateTranscriptionStatus(Long fileId, String status) {
        AudioFile audioFile = audioFileMapper.selectById(fileId);
        if (audioFile != null) {
            audioFile.setTranscriptionStatus(status);
            audioFile.setUpdatedAt(LocalDateTime.now());
            audioFileMapper.updateById(audioFile);
            return true;
        }
        return false;
    }

    /**
     * 更新音频元数据
     */
    public boolean updateAudioMetadata(Long fileId, Integer durationSeconds, Integer sampleRate, Integer bitRate, Integer channels) {
        AudioFile audioFile = audioFileMapper.selectById(fileId);
        if (audioFile != null) {
            audioFile.setDurationSeconds(durationSeconds);
            audioFile.setSampleRate(sampleRate);
            audioFile.setBitRate(bitRate);
            audioFile.setChannels(channels);
            audioFile.setUpdatedAt(LocalDateTime.now());
            audioFileMapper.updateById(audioFile);
            return true;
        }
        return false;
    }

    /**
     * 删除音频文件
     */
    public boolean deleteAudioFile(Long fileId) {
        AudioFile audioFile = audioFileMapper.selectById(fileId);
        if (audioFile != null) {
            try {
                // 删除物理文件
                Path filePath = Paths.get(audioFile.getFilePath());
                Files.deleteIfExists(filePath);
                
                // 删除数据库记录
                audioFileMapper.deleteById(fileId);
                return true;
            } catch (IOException e) {
                // 记录日志但继续删除数据库记录
                audioFileMapper.deleteById(fileId);
                return true;
            }
        }
        return false;
    }

    /**
     * 获取用户音频文件统计
     */
    public AudioFileStats getUserAudioStats(Long userId) {
        long totalFiles = audioFileMapper.countByUserId(userId);
        long totalSize = audioFileMapper.sumFileSizeByUserId(userId);
        
        AudioFileStats stats = new AudioFileStats();
        stats.setTotalFiles(totalFiles);
        stats.setTotalSize(totalSize);
        stats.setFormattedSize(formatFileSize(totalSize));
        
        return stats;
    }

    /**
     * 获取待处理的音频文件
     */
    public List<AudioFile> getPendingFiles(int limit) {
        return audioFileMapper.findPendingFiles(limit);
    }

    /**
     * 获取待转写的音频文件
     */
    public List<AudioFile> getPendingTranscriptionFiles(int limit) {
        return audioFileMapper.findPendingTranscriptionFiles(limit);
    }

    // 私有辅助方法
    private void validateAudioFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("audio/")) {
            throw new IllegalArgumentException("文件必须是音频格式");
        }
        
        // 检查文件大小（这里简化处理，实际应该解析maxFileSize配置）
        if (file.getSize() > 50 * 1024 * 1024) { // 50MB
            throw new IllegalArgumentException("文件大小不能超过50MB");
        }
    }

    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }

    private String calculateFileHash(Path filePath) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(filePath);
            byte[] hashBytes = digest.digest(fileBytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }

    // 内部类：音频文件统计
    public static class AudioFileStats {
        private long totalFiles;
        private long totalSize;
        private String formattedSize;

        // Getters and Setters
        public long getTotalFiles() { return totalFiles; }
        public void setTotalFiles(long totalFiles) { this.totalFiles = totalFiles; }
        public long getTotalSize() { return totalSize; }
        public void setTotalSize(long totalSize) { this.totalSize = totalSize; }
        public String getFormattedSize() { return formattedSize; }
        public void setFormattedSize(String formattedSize) { this.formattedSize = formattedSize; }
    }
}
