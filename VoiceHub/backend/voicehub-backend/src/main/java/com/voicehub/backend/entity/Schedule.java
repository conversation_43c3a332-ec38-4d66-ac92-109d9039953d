package com.voicehub.backend.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 日程实体类
 * 存储用户的日程安排信息
 */
@TableName("schedules")
public class Schedule extends BaseEntity {

    @NotBlank(message = "Title cannot be blank")
    @TableField("title")
    private String title;

    @TableField("description")
    private String description;

    @NotNull(message = "Start time cannot be null")
    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("location")
    private String location;

    @TableField("priority")
    private Priority priority = Priority.MEDIUM;

    @TableField("status")
    private Status status = Status.SCHEDULED;

    // 逻辑字段，不存储在数据库中
    @TableField(exist = false)
    private Boolean isAllDay = false;

    // 对应数据库中的jsonb字段
    @TableField("reminder_settings")
    private String reminderSettings;

    // 原始语音指令
    @TableField("voice_command")
    private String voiceCommand;

    // 对应数据库中的voice_created字段
    @TableField("voice_created")
    private Boolean voiceCreated = false;

    @TableField("user_id")
    private Long userId;

    @TableField(exist = false)
    private User user;

    // 新增的getter/setter方法
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    // 枚举定义
    public enum Priority {
        LOW, MEDIUM, HIGH, URGENT
    }

    public enum Status {
        SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED, POSTPONED
    }

    // 默认构造函数
    public Schedule() {}

    // 构造函数
    public Schedule(String title, String description, LocalDateTime startTime, User user) {
        this.title = title;
        this.description = description;
        this.startTime = startTime;
        this.user = user;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Boolean getIsAllDay() {
        return isAllDay;
    }

    public void setIsAllDay(Boolean isAllDay) {
        this.isAllDay = isAllDay;
    }

    public String getReminderSettings() {
        return reminderSettings;
    }

    public void setReminderSettings(String reminderSettings) {
        this.reminderSettings = reminderSettings;
    }

    public String getVoiceCommand() {
        return voiceCommand;
    }

    public void setVoiceCommand(String voiceCommand) {
        this.voiceCommand = voiceCommand;
    }

    public Boolean getVoiceCreated() {
        return voiceCreated;
    }

    public void setVoiceCreated(Boolean voiceCreated) {
        this.voiceCreated = voiceCreated;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // 工具方法
    public boolean isUpcoming() {
        return startTime.isAfter(LocalDateTime.now());
    }

    public boolean isToday() {
        LocalDateTime now = LocalDateTime.now();
        return startTime.toLocalDate().equals(now.toLocalDate());
    }

    public boolean isOverdue() {
        return endTime != null && endTime.isBefore(LocalDateTime.now()) && 
               (status == Status.SCHEDULED || status == Status.IN_PROGRESS);
    }

    public long getDurationMinutes() {
        if (endTime == null) return 0;
        return java.time.Duration.between(startTime, endTime).toMinutes();
    }

    @Override
    public String toString() {
        return "Schedule{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", priority=" + priority +
                ", status=" + status +
                '}';
    }
}