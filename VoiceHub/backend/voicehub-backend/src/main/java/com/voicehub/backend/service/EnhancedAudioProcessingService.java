package com.voicehub.backend.service;

import com.voicehub.backend.entity.AudioFile;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 增强的音频处理服务
 * 提供音频文件上传、格式转换、质量分析、异步处理等功能
 * 
 * <AUTHOR> Team
 */
@Service
public class EnhancedAudioProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedAudioProcessingService.class);

    @Autowired
    private AudioFileService audioFileService;

    @Autowired
    private SpeechToTextService speechToTextService;

    @Value("${voicehub.file.upload-dir:/tmp/voicehub/audio}")
    private String uploadDir;

    @Value("${voicehub.file.max-size:52428800}") // 50MB in bytes
    private long maxFileSize;

    // 支持的音频格式
    private static final Set<String> SUPPORTED_FORMATS = Set.of(
        "wav", "mp3", "m4a", "aac", "ogg", "flac", "opus", "amr", "pcm"
    );

    // 支持的MIME类型
    private static final Set<String> SUPPORTED_MIME_TYPES = Set.of(
        "audio/wav", "audio/wave", "audio/x-wav",
        "audio/mpeg", "audio/mp3",
        "audio/mp4", "audio/m4a", "audio/x-m4a",
        "audio/aac", "audio/x-aac",
        "audio/ogg", "audio/vorbis",
        "audio/flac", "audio/x-flac",
        "audio/opus",
        "audio/amr", "audio/3gpp",
        "audio/pcm", "audio/l16"
    );

    /**
     * 音频质量信息
     */
    public static class AudioQuality {
        private final String format;
        private final long fileSize;
        private final Integer duration;
        private final Integer sampleRate;
        private final Integer bitRate;
        private final Integer channels;
        private final String quality;
        private final List<String> recommendations;

        public AudioQuality(String format, long fileSize, Integer duration, 
                          Integer sampleRate, Integer bitRate, Integer channels) {
            this.format = format;
            this.fileSize = fileSize;
            this.duration = duration;
            this.sampleRate = sampleRate;
            this.bitRate = bitRate;
            this.channels = channels;
            this.quality = assessQuality();
            this.recommendations = generateRecommendations();
        }

        private String assessQuality() {
            if (sampleRate != null && bitRate != null) {
                if (sampleRate >= 44100 && bitRate >= 320) return "高质量";
                if (sampleRate >= 22050 && bitRate >= 128) return "中等质量";
                if (sampleRate >= 16000 && bitRate >= 64) return "可接受";
                return "低质量";
            }
            return "未知";
        }

        private List<String> generateRecommendations() {
            List<String> recommendations = new ArrayList<>();
            
            if (sampleRate != null && sampleRate < 16000) {
                recommendations.add("建议使用16kHz或更高的采样率以获得更好的语音识别效果");
            }
            
            if (bitRate != null && bitRate < 64) {
                recommendations.add("建议使用64kbps或更高的比特率以保证音质");
            }
            
            if (channels != null && channels > 1) {
                recommendations.add("语音识别建议使用单声道音频");
            }
            
            if (fileSize > 10 * 1024 * 1024) { // 10MB
                recommendations.add("文件较大，建议压缩以提高处理速度");
            }
            
            return recommendations;
        }

        // Getters
        public String getFormat() { return format; }
        public long getFileSize() { return fileSize; }
        public Integer getDuration() { return duration; }
        public Integer getSampleRate() { return sampleRate; }
        public Integer getBitRate() { return bitRate; }
        public Integer getChannels() { return channels; }
        public String getQuality() { return quality; }
        public List<String> getRecommendations() { return recommendations; }
    }

    /**
     * 增强的音频文件上传处理
     * 包含文件验证、格式检查、质量分析等功能
     * 
     * @param file 音频文件
     * @param user 用户
     * @param options 处理选项
     * @return 处理结果
     */
    public Map<String, Object> processAudioUpload(MultipartFile file, User user, Map<String, Object> options) {
        logger.info("开始处理音频上传: userId={}, fileName={}, size={}", 
                   user.getId(), file.getOriginalFilename(), file.getSize());

        try {
            // 1. 验证文件
            validateAudioFile(file);
            
            // 2. 分析音频质量
            AudioQuality quality = analyzeAudioQuality(file);
            
            // 3. 上传文件
            AudioFile audioFile = audioFileService.uploadAudioFile(user.getId(), file, "web_upload");
            
            // 4. 异步处理音频（转写等）
            boolean enableTranscription = options != null && 
                Boolean.TRUE.equals(options.get("enableTranscription"));
            
            CompletableFuture<String> transcriptionFuture = null;
            if (enableTranscription) {
                transcriptionFuture = processAudioAsync(audioFile, user);
            }
            
            // 5. 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("audioFile", audioFile);
            result.put("quality", quality);
            result.put("uploadSuccess", true);
            result.put("transcriptionStarted", enableTranscription);
            
            if (transcriptionFuture != null) {
                result.put("transcriptionId", audioFile.getId());
                result.put("estimatedProcessingTime", estimateProcessingTime(quality.getDuration()));
            }
            
            logger.info("音频上传处理完成: userId={}, audioFileId={}", user.getId(), audioFile.getId());
            return result;
            
        } catch (Exception e) {
            logger.error("音频上传处理失败: userId={}, error={}", user.getId(), e.getMessage());
            throw BusinessException.internalError("音频上传处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证音频文件
     * 
     * @param file 音频文件
     */
    private void validateAudioFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw BusinessException.validationError("音频文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            throw BusinessException.validationError(
                String.format("文件大小超过限制，最大允许%dMB", maxFileSize / (1024 * 1024)));
        }
        
        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw BusinessException.validationError("文件名不能为空");
        }
        
        // 检查文件扩展名
        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!SUPPORTED_FORMATS.contains(extension)) {
            throw BusinessException.validationError(
                "不支持的音频格式: " + extension + "，支持的格式: " + String.join(", ", SUPPORTED_FORMATS));
        }
        
        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && !SUPPORTED_MIME_TYPES.contains(contentType.toLowerCase())) {
            logger.warn("可能不支持的MIME类型: {}", contentType);
        }
    }

    /**
     * 分析音频质量
     * 
     * @param file 音频文件
     * @return 音频质量信息
     */
    private AudioQuality analyzeAudioQuality(MultipartFile file) {
        String format = getFileExtension(file.getOriginalFilename()).toLowerCase();
        long fileSize = file.getSize();
        
        // 这里是简化的质量分析，实际项目中可以使用FFmpeg等工具进行详细分析
        Integer duration = estimateDuration(fileSize, format);
        Integer sampleRate = getDefaultSampleRate(format);
        Integer bitRate = estimateBitRate(fileSize, duration);
        Integer channels = 1; // 默认单声道
        
        return new AudioQuality(format, fileSize, duration, sampleRate, bitRate, channels);
    }

    /**
     * 异步处理音频
     * 
     * @param audioFile 音频文件
     * @param user 用户
     * @return 转写结果的Future
     */
    private CompletableFuture<String> processAudioAsync(AudioFile audioFile, User user) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始异步音频处理: audioFileId={}", audioFile.getId());
                
                // 这里可以添加音频预处理逻辑
                // 例如：格式转换、降噪、音量标准化等
                
                // 执行语音转文字
                // 注意：这里需要根据实际的AudioFile实体结构调整
                // String transcription = speechToTextService.convertSpeechToText(audioData, format, sampleRate);
                
                // 模拟处理时间
                Thread.sleep(2000);
                
                String transcription = "这是模拟的转写结果"; // 实际应该调用真实的转写服务
                
                logger.info("异步音频处理完成: audioFileId={}", audioFile.getId());
                return transcription;
                
            } catch (Exception e) {
                logger.error("异步音频处理失败: audioFileId={}, error={}", audioFile.getId(), e.getMessage());
                throw new RuntimeException("音频处理失败", e);
            }
        });
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    /**
     * 估算音频时长（秒）
     */
    private Integer estimateDuration(long fileSize, String format) {
        // 这是一个简化的估算，实际应该解析音频文件头信息
        switch (format.toLowerCase()) {
            case "mp3":
                return (int) (fileSize / 16000); // 假设128kbps
            case "wav":
                return (int) (fileSize / 176400); // 假设44.1kHz 16bit 立体声
            case "m4a":
            case "aac":
                return (int) (fileSize / 16000); // 假设128kbps
            default:
                return (int) (fileSize / 20000); // 默认估算
        }
    }

    /**
     * 获取默认采样率
     */
    private Integer getDefaultSampleRate(String format) {
        switch (format.toLowerCase()) {
            case "wav":
                return 44100;
            case "mp3":
            case "m4a":
            case "aac":
                return 44100;
            case "amr":
                return 8000;
            default:
                return 16000;
        }
    }

    /**
     * 估算比特率
     */
    private Integer estimateBitRate(long fileSize, Integer duration) {
        if (duration == null || duration <= 0) {
            return null;
        }
        return (int) ((fileSize * 8) / duration / 1000); // kbps
    }

    /**
     * 估算处理时间
     */
    private Integer estimateProcessingTime(Integer duration) {
        if (duration == null) {
            return 30; // 默认30秒
        }
        // 假设处理时间是音频时长的0.5倍，最少10秒
        return Math.max(10, duration / 2);
    }

    /**
     * 获取音频处理状态
     * 
     * @param audioFileId 音频文件ID
     * @return 处理状态信息
     */
    public Map<String, Object> getProcessingStatus(Long audioFileId) {
        // 这里应该查询实际的处理状态
        Map<String, Object> status = new HashMap<>();
        status.put("audioFileId", audioFileId);
        status.put("status", "completed"); // processing, completed, failed
        status.put("progress", 100);
        status.put("message", "处理完成");
        
        return status;
    }
}
