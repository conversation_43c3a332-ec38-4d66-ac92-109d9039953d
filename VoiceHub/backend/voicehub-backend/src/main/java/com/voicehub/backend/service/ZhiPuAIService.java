package com.voicehub.backend.service;

import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 智谱AI服务
 * 集成智谱AI GLM大模型API
 * 
 * <AUTHOR> Team
 */
@Service
public class ZhiPuAIService {

    private static final Logger logger = LoggerFactory.getLogger(ZhiPuAIService.class);

    @Value("${voicehub.ai.zhipu.api-key:}")
    private String apiKey;

    @Value("${voicehub.ai.zhipu.base-url:https://open.bigmodel.cn/api/paas/v4/chat/completions}")
    private String baseUrl;

    @Value("${voicehub.ai.zhipu.enabled:false}")
    private boolean enabled;

    @Value("${voicehub.ai.zhipu.timeout:30000}")
    private int timeout;

    private final RestTemplate restTemplate;

    public ZhiPuAIService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 生成AI响应
     * 
     * @param conversation 对话上下文
     * @param prompt 用户输入
     * @param modelId 模型ID
     * @return AI响应
     */
    public String generateResponse(Conversation conversation, String prompt, String modelId) {
        if (!enabled) {
            throw BusinessException.internalError("智谱AI服务未启用");
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw BusinessException.internalError("智谱AI API密钥未配置");
        }

        logger.info("调用智谱AI API: model={}, promptLength={}", modelId, prompt.length());

        try {
            // 构建请求
            Map<String, Object> request = buildRequest(conversation, prompt, modelId);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(baseUrl, entity, Map.class);
            
            // 解析响应
            return parseResponse(response.getBody());
            
        } catch (Exception e) {
            logger.error("智谱AI API调用失败: {}", e.getMessage());
            throw BusinessException.internalError("智谱AI服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI响应（使用默认模型）
     */
    public String generateResponse(Conversation conversation, String prompt) {
        return generateResponse(conversation, prompt, "glm-3-turbo");
    }

    /**
     * 构建API请求
     */
    private Map<String, Object> buildRequest(Conversation conversation, String prompt, String modelId) {
        Map<String, Object> request = new HashMap<>();
        
        // 模型参数
        request.put("model", modelId);
        
        // 构建消息列表
        List<Map<String, Object>> messages = new ArrayList<>();
        
        // 系统消息
        if (conversation != null) {
            String systemPrompt = buildSystemPrompt(conversation);
            if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
                Map<String, Object> systemMessage = new HashMap<>();
                systemMessage.put("role", "system");
                systemMessage.put("content", systemPrompt);
                messages.add(systemMessage);
            }
        }
        
        // 用户消息
        Map<String, Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", prompt);
        messages.add(userMessage);
        
        request.put("messages", messages);
        
        // 参数设置
        request.put("max_tokens", 2000);
        request.put("temperature", 0.7);
        request.put("top_p", 0.9);
        request.put("stream", false);
        
        return request;
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(Conversation conversation) {
        if (conversation == null) {
            return "你是智谱AI助手，一个由智谱AI训练的大语言模型。请用中文回答用户的问题，回答要准确、有帮助且友善。";
        }
        
        switch (conversation.getType()) {
            case EMOTIONAL_SUPPORT:
                return "你是一个专业的心理健康助手，具有深度的情感理解能力。请以温暖、同理心的方式回应用户的情感需求，提供积极的支持和建议。";
                
            case SCHEDULE_HELP:
                return "你是一个智能的日程管理专家。帮助用户高效规划时间、优化任务安排、提供时间管理策略。回答要实用、具体、可执行。";
                
            case NOTE_ASSISTANCE:
                return "你是一个专业的知识管理助手。擅长信息整理、内容总结、结构化表达。帮助用户优化笔记结构，提升学习效率。";
                
            case BRAINSTORMING:
                return "你是一个创新思维专家。善于发散思维、联想创意、提供多元化解决方案。鼓励用户探索新的可能性和创意方向。";
                
            case PROBLEM_SOLVING:
                return "你是一个逻辑分析专家。擅长问题分解、原因分析、方案设计。提供系统性、可操作的解决方案，注重逻辑性和实用性。";
                
            case CASUAL_CHAT:
                return "你是一个友善的对话伙伴。进行自然、愉快的交流，保持有趣和有帮助。要亲切和引人入胜。";
                
            default:
                return "你是智谱AI助手，请用中文回答用户的问题，回答要准确、有帮助且友善。";
        }
    }

    /**
     * 解析API响应
     */
    private String parseResponse(Map<String, Object> responseBody) {
        if (responseBody == null) {
            throw BusinessException.internalError("智谱AI API返回空响应");
        }
        
        // 检查错误
        if (responseBody.containsKey("error")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> error = (Map<String, Object>) responseBody.get("error");
            String code = (String) error.get("code");
            String message = (String) error.get("message");
            throw BusinessException.internalError("智谱AI API错误: " + code + " - " + message);
        }
        
        // 解析choices
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
        if (choices == null || choices.isEmpty()) {
            throw BusinessException.internalError("智谱AI API响应格式错误：缺少choices字段");
        }
        
        Map<String, Object> firstChoice = choices.get(0);
        @SuppressWarnings("unchecked")
        Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
        if (message == null) {
            throw BusinessException.internalError("智谱AI API响应格式错误：缺少message字段");
        }
        
        String content = (String) message.get("content");
        if (content == null || content.trim().isEmpty()) {
            throw BusinessException.internalError("智谱AI API返回空内容");
        }
        
        return content.trim();
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return enabled && apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 检查Pro版本是否可用
     */
    public boolean isProAvailable() {
        // 这里可以添加更具体的Pro版本检查逻辑
        return isAvailable();
    }

    /**
     * 测试API连接
     */
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        
        if (!isAvailable()) {
            result.put("available", false);
            result.put("error", "服务未启用或API密钥未配置");
            return result;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            String response = generateResponse(null, "你好，请简单介绍一下自己");
            long responseTime = System.currentTimeMillis() - startTime;
            
            result.put("available", true);
            result.put("responseTime", responseTime);
            result.put("testResponse", response.substring(0, Math.min(50, response.length())));
            
        } catch (Exception e) {
            result.put("available", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取支持的模型列表
     */
    public List<Map<String, Object>> getSupportedModels() {
        List<Map<String, Object>> models = new ArrayList<>();
        
        Map<String, Object> glm3Turbo = new HashMap<>();
        glm3Turbo.put("id", "glm-3-turbo");
        glm3Turbo.put("name", "GLM-3-Turbo");
        glm3Turbo.put("description", "快速响应，适合日常对话");
        glm3Turbo.put("maxTokens", 2000);
        glm3Turbo.put("costPer1kTokens", 0.005);
        models.add(glm3Turbo);
        
        Map<String, Object> glm4 = new HashMap<>();
        glm4.put("id", "glm-4");
        glm4.put("name", "GLM-4");
        glm4.put("description", "多模态能力，推理能力强");
        glm4.put("maxTokens", 4000);
        glm4.put("costPer1kTokens", 0.015);
        models.add(glm4);
        
        Map<String, Object> glm4v = new HashMap<>();
        glm4v.put("id", "glm-4v");
        glm4v.put("name", "GLM-4V");
        glm4v.put("description", "视觉理解能力，支持图像分析");
        glm4v.put("maxTokens", 4000);
        glm4v.put("costPer1kTokens", 0.02);
        models.add(glm4v);
        
        return models;
    }

    /**
     * 获取使用统计
     */
    public Map<String, Object> getUsageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里应该从数据库查询实际统计数据
        stats.put("totalRequests", 67);
        stats.put("successfulRequests", 65);
        stats.put("failedRequests", 2);
        stats.put("averageResponseTime", 1900);
        stats.put("totalTokensUsed", 32150);
        stats.put("totalCost", 0.48);
        stats.put("lastUsed", new Date());
        
        return stats;
    }

    /**
     * 生成专业分析响应
     */
    public String generateAnalysisResponse(String content, String analysisType) {
        String systemPrompt = "你是一个专业的分析专家。请对用户提供的内容进行深入分析。";
        
        switch (analysisType) {
            case "logical":
                systemPrompt += "请从逻辑结构、论证过程、推理链条等角度进行分析。";
                break;
            case "emotional":
                systemPrompt += "请从情感表达、情绪变化、心理状态等角度进行分析。";
                break;
            case "structural":
                systemPrompt += "请从内容结构、组织方式、层次关系等角度进行分析。";
                break;
            default:
                systemPrompt += "请进行全面、深入的综合分析。";
        }
        
        String enhancedPrompt = systemPrompt + "\n\n分析内容：" + content + "\n\n请提供详细的分析结果：";
        
        return generateResponse(null, enhancedPrompt);
    }

    /**
     * 生成知识问答响应
     */
    public String generateKnowledgeResponse(String question, String domain) {
        String systemPrompt = "你是一个知识渊博的专家助手。请基于准确的知识回答用户的问题。";
        
        if (domain != null && !domain.trim().isEmpty()) {
            systemPrompt += "特别关注" + domain + "领域的专业知识。";
        }
        
        systemPrompt += "回答要准确、详细、有条理，如果不确定请明确说明。";
        
        String enhancedPrompt = systemPrompt + "\n\n问题：" + question + "\n\n请提供专业的回答：";
        
        return generateResponse(null, enhancedPrompt);
    }
}
