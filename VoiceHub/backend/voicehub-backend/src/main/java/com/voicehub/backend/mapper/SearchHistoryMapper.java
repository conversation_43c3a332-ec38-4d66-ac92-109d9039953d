package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.SearchHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索历史Mapper接口
 */
@Mapper
public interface SearchHistoryMapper extends BaseMapper<SearchHistory> {

    /**
     * 根据用户ID查找搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<SearchHistory> findByUserId(@Param("userId") Long userId);

    /**
     * 根据搜索类型查找历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE search_type = #{searchType}")
    List<SearchHistory> findBySearchType(@Param("searchType") String searchType);

    /**
     * 查找用户的特定类型搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} AND search_type = #{searchType} ORDER BY created_at DESC")
    List<SearchHistory> findByUserIdAndSearchType(@Param("userId") Long userId, @Param("searchType") String searchType);

    /**
     * 根据搜索查询内容查找历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE search_query LIKE CONCAT('%', #{query}, '%')")
    List<SearchHistory> findBySearchQuery(@Param("query") String query);

    /**
     * 查找用户的搜索查询历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} AND search_query LIKE CONCAT('%', #{query}, '%') ORDER BY created_at DESC")
    List<SearchHistory> findByUserIdAndSearchQuery(@Param("userId") Long userId, @Param("query") String query);

    /**
     * 根据时间范围查找搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE created_at BETWEEN #{startDate} AND #{endDate} ORDER BY created_at DESC")
    List<SearchHistory> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户在时间范围内的搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} AND created_at BETWEEN #{startDate} AND #{endDate} ORDER BY created_at DESC")
    List<SearchHistory> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户最近的搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    List<SearchHistory> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查找用户最近的特定类型搜索历史
     */
    @Select("SELECT * FROM voicehub.search_history WHERE user_id = #{userId} AND search_type = #{searchType} ORDER BY created_at DESC LIMIT #{limit}")
    List<SearchHistory> findRecentByUserIdAndSearchType(@Param("userId") Long userId, @Param("searchType") String searchType, @Param("limit") int limit);

    /**
     * 统计用户的搜索次数
     */
    @Select("SELECT COUNT(*) FROM voicehub.search_history WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户特定类型的搜索次数
     */
    @Select("SELECT COUNT(*) FROM voicehub.search_history WHERE user_id = #{userId} AND search_type = #{searchType}")
    long countByUserIdAndSearchType(@Param("userId") Long userId, @Param("searchType") String searchType);

    /**
     * 查找热门搜索查询
     */
    @Select("SELECT search_query, COUNT(*) as search_count FROM voicehub.search_history GROUP BY search_query ORDER BY search_count DESC LIMIT #{limit}")
    List<Object[]> findPopularSearchQueries(@Param("limit") int limit);

    /**
     * 查找用户的热门搜索查询
     */
    @Select("SELECT search_query, COUNT(*) as search_count FROM voicehub.search_history WHERE user_id = #{userId} GROUP BY search_query ORDER BY search_count DESC LIMIT #{limit}")
    List<Object[]> findPopularSearchQueriesByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 删除用户的旧搜索历史
     */
    @Select("DELETE FROM voicehub.search_history WHERE user_id = #{userId} AND created_at < #{beforeDate}")
    void deleteOldHistoryByUserId(@Param("userId") Long userId, @Param("beforeDate") LocalDateTime beforeDate);
}
