package com.voicehub.backend.controller;

import com.voicehub.backend.entity.Schedule;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.service.ScheduleService;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.util.PageConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日程管理控制器
 * 处理日程相关的HTTP请求
 */
@RestController
@RequestMapping("/schedules")
@Tag(name = "日程管理", description = "日程管理相关接口")
@PreAuthorize("hasRole('USER')")
public class ScheduleController {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleController.class);

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private UserService userService;

    /**
     * 通过语音指令创建日程
     */
    @PostMapping("/voice")
    @Operation(summary = "语音创建日程", description = "通过语音指令创建日程")
    public ResponseEntity<?> createScheduleFromVoice(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        
        try {
            String voiceCommand = request.get("voiceCommand");
            if (voiceCommand == null || voiceCommand.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(createErrorResponse("语音指令不能为空"));
            }

            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule schedule = scheduleService.createScheduleFromVoice(voiceCommand, user);

            logger.info("Successfully created schedule from voice for user: {}", user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程创建成功", schedule));

        } catch (Exception e) {
            logger.error("Failed to create schedule from voice: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("创建日程失败: " + e.getMessage()));
        }
    }

    /**
     * 创建日程
     */
    @PostMapping
    @Operation(summary = "创建日程", description = "创建新的日程")
    public ResponseEntity<?> createSchedule(
            @Valid @RequestBody Schedule schedule,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule createdSchedule = scheduleService.createSchedule(schedule, user);

            logger.info("Successfully created schedule for user: {}", user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程创建成功", createdSchedule));

        } catch (Exception e) {
            logger.error("Failed to create schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("创建日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户所有日程
     */
    @GetMapping
    @Operation(summary = "获取日程列表", description = "获取用户的日程列表")
    public ResponseEntity<?> getSchedules(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Pageable pageable = PageRequest.of(page, size);
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<Schedule> mybatisPlusPage = PageConverter.fromPageable(pageable);
            com.baomidou.mybatisplus.core.metadata.IPage<Schedule> iPageSchedules = scheduleService.getUserSchedules(user, mybatisPlusPage);
            Page<Schedule> schedules = PageConverter.convertToSpringPage(iPageSchedules);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", schedules.getContent());
            response.put("totalElements", schedules.getTotalElements());
            response.put("totalPages", schedules.getTotalPages());
            response.put("currentPage", page);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to get schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取今天的日程
     */
    @GetMapping("/today")
    @Operation(summary = "获取今天日程", description = "获取用户今天的日程")
    public ResponseEntity<?> getTodaySchedules(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.getTodaySchedules(user);

            return ResponseEntity.ok(createSuccessResponse("获取今天日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to get today's schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取今天日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取即将到来的日程
     */
    @GetMapping("/upcoming")
    @Operation(summary = "获取即将到来的日程", description = "获取用户即将到来的日程")
    public ResponseEntity<?> getUpcomingSchedules(
            @RequestParam(defaultValue = "7") int days,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.getUpcomingSchedules(user, days);

            return ResponseEntity.ok(createSuccessResponse("获取即将到来的日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to get upcoming schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取即将到来的日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定时间范围的日程
     */
    @GetMapping("/range")
    @Operation(summary = "获取时间范围日程", description = "获取指定时间范围内的日程")
    public ResponseEntity<?> getSchedulesByRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.getSchedulesByTimeRange(user, startTime, endTime);

            return ResponseEntity.ok(createSuccessResponse("获取时间范围日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to get schedules by range: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取时间范围日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取本周日程
     */
    @GetMapping("/weekly")
    @Operation(summary = "获取本周日程", description = "获取用户本周的日程")
    public ResponseEntity<?> getWeeklySchedules(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.getWeeklySchedules(user);

            return ResponseEntity.ok(createSuccessResponse("获取本周日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to get weekly schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取本周日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取本月日程
     */
    @GetMapping("/monthly")
    @Operation(summary = "获取本月日程", description = "获取用户本月的日程")
    public ResponseEntity<?> getMonthlySchedules(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.getMonthlySchedules(user);

            return ResponseEntity.ok(createSuccessResponse("获取本月日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to get monthly schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取本月日程失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索日程
     */
    @GetMapping("/search")
    @Operation(summary = "搜索日程", description = "根据关键词搜索日程")
    public ResponseEntity<?> searchSchedules(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Schedule> schedules = scheduleService.searchSchedules(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("搜索日程成功", schedules));

        } catch (Exception e) {
            logger.error("Failed to search schedules: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("搜索日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取日程详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取日程详情", description = "根据ID获取日程详情")
    public ResponseEntity<?> getSchedule(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            // 这里需要在ScheduleService中添加getScheduleById方法
            // Schedule schedule = scheduleService.getScheduleById(id, user);

            return ResponseEntity.ok(createSuccessResponse("获取日程详情成功", null));

        } catch (Exception e) {
            logger.error("Failed to get schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取日程详情失败: " + e.getMessage()));
        }
    }

    /**
     * 更新日程
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新日程", description = "更新指定的日程")
    public ResponseEntity<?> updateSchedule(
            @PathVariable Long id,
            @Valid @RequestBody Schedule schedule,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule updatedSchedule = scheduleService.updateSchedule(id, schedule, user);

            logger.info("Successfully updated schedule: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程更新成功", updatedSchedule));

        } catch (Exception e) {
            logger.error("Failed to update schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("更新日程失败: " + e.getMessage()));
        }
    }

    /**
     * 删除日程
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除日程", description = "删除指定的日程")
    public ResponseEntity<?> deleteSchedule(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            scheduleService.deleteSchedule(id, user);

            logger.info("Successfully deleted schedule: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程删除成功", null));

        } catch (Exception e) {
            logger.error("Failed to delete schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("删除日程失败: " + e.getMessage()));
        }
    }

    /**
     * 完成日程
     */
    @PutMapping("/{id}/complete")
    @Operation(summary = "完成日程", description = "标记日程为已完成")
    public ResponseEntity<?> completeSchedule(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule schedule = scheduleService.completeSchedule(id, user);

            logger.info("Successfully completed schedule: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程已完成", schedule));

        } catch (Exception e) {
            logger.error("Failed to complete schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("完成日程失败: " + e.getMessage()));
        }
    }

    /**
     * 取消日程
     */
    @PutMapping("/{id}/cancel")
    @Operation(summary = "取消日程", description = "取消指定的日程")
    public ResponseEntity<?> cancelSchedule(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule schedule = scheduleService.cancelSchedule(id, user);

            logger.info("Successfully cancelled schedule: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程已取消", schedule));

        } catch (Exception e) {
            logger.error("Failed to cancel schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("取消日程失败: " + e.getMessage()));
        }
    }

    /**
     * 推迟日程
     */
    @PutMapping("/{id}/postpone")
    @Operation(summary = "推迟日程", description = "推迟指定的日程")
    public ResponseEntity<?> postponeSchedule(
            @PathVariable Long id,
            @RequestParam int minutes,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Schedule schedule = scheduleService.postponeSchedule(id, user, minutes);

            logger.info("Successfully postponed schedule: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("日程已推迟", schedule));

        } catch (Exception e) {
            logger.error("Failed to postpone schedule: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("推迟日程失败: " + e.getMessage()));
        }
    }

    /**
     * 获取日程统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取日程统计", description = "获取用户的日程统计信息")
    public ResponseEntity<?> getScheduleStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            ScheduleService.ScheduleStats stats = scheduleService.getUserScheduleStats(user);

            return ResponseEntity.ok(createSuccessResponse("获取日程统计成功", stats));

        } catch (Exception e) {
            logger.error("Failed to get schedule stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("获取日程统计失败: " + e.getMessage()));
        }
    }

    /**
     * 创建成功响应
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}