package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 搜索历史实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("search_history")
public class SearchHistory extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 搜索查询内容
     */
    @TableField("search_query")
    private String searchQuery;

    /**
     * 搜索类型：voice_notes/conversations/schedules/global
     */
    @TableField("search_type")
    private String searchType;

    /**
     * 搜索结果数量
     */
    @TableField("results_count")
    private Integer resultsCount;

    /**
     * 点击的结果ID
     */
    @TableField("clicked_result_id")
    private Long clickedResultId;

    /**
     * 点击的结果类型
     */
    @TableField("clicked_result_type")
    private String clickedResultType;

    public SearchHistory() {
        super();
    }

    public SearchHistory(Long userId, String searchQuery, String searchType) {
        this();
        this.userId = userId;
        this.searchQuery = searchQuery;
        this.searchType = searchType;
    }
}
