package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * 用户实体类 - 完全按照数据库表结构设计
 * 实现Spring Security的UserDetails接口
 * 手动实现getter/setter方法以确保Spring Boot 3.x兼容性
 */
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity implements UserDetails {

    /**
     * 用户名，唯一
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @TableField("username")
    private String username;

    /**
     * 邮箱地址，唯一
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @TableField("email")
    private String email;

    /**
     * 密码哈希值
     */
    @JsonIgnore
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码长度至少6个字符")
    @TableField("password_hash")
    private String passwordHash;

    /**
     * 用户全名
     */
    @TableField("full_name")
    private String fullName;

    /**
     * 头像URL地址
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 手机号码
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 首选语言，默认中文
     */
    @TableField("preferred_language")
    private String preferredLanguage = "zh-CN";

    /**
     * 时区设置，默认上海
     */
    @TableField("timezone")
    private String timezone = "Asia/Shanghai";

    /**
     * 语音相关设置（JSON格式）
     */
    @TableField(value = "voice_settings", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> voiceSettings;

    /**
     * 通知设置（JSON格式）
     */
    @TableField(value = "notification_settings", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> notificationSettings;

    /**
     * 订阅类型：FREE/PREMIUM/ENTERPRISE
     */
    @TableField("subscription_type")
    private String subscriptionType = "FREE";

    /**
     * 订阅到期时间
     */
    @TableField("subscription_expires_at")
    private LocalDateTime subscriptionExpiresAt;

    /**
     * 账户是否激活
     */
    @TableField("is_active")
    private Boolean isActive = true;

    /**
     * 邮箱是否已验证
     */
    @TableField("is_verified")
    private Boolean isVerified = false;

    /**
     * 最后登录时间
     */
    @TableField("last_login_at")
    private LocalDateTime lastLoginAt;

    // 注意：数据库表中没有role相关字段，这些字段仅用于Spring Security
    // 在实际应用中，角色信息可以通过其他方式管理（如用户类型、订阅类型等）

    /**
     * 用户角色（逻辑字段，不存储在数据库中）
     */
    @TableField(exist = false)
    private String role = "USER";

    /**
     * 账户是否启用（逻辑字段，基于is_active计算）
     */
    @TableField(exist = false)
    private Boolean enabledFlag = true;

    /**
     * 账户是否未过期（逻辑字段，基于订阅状态计算）
     */
    @TableField(exist = false)
    private Boolean accountNonExpiredFlag = true;

    /**
     * 账户是否未锁定（逻辑字段，基于is_active计算）
     */
    @TableField(exist = false)
    private Boolean accountNonLockedFlag = true;

    /**
     * 凭证是否未过期（逻辑字段，固定为true）
     */
    @TableField(exist = false)
    private Boolean credentialsNonExpiredFlag = true;

    // 构造函数
    public User() {
        super();
    }

    public User(String username, String email, String passwordHash) {
        this();
        this.username = username;
        this.email = email;
        this.passwordHash = passwordHash;
    }

    // 用户角色枚举
    public enum UserRole {
        USER, ADMIN
    }



    // 业务方法
    public boolean isAdmin() {
        return "ADMIN".equals(role);
    }

    public boolean isPremiumUser() {
        return "PREMIUM".equals(subscriptionType) || "ENTERPRISE".equals(subscriptionType);
    }

    public boolean isSubscriptionValid() {
        return subscriptionExpiresAt == null || subscriptionExpiresAt.isAfter(LocalDateTime.now());
    }

    // 兼容旧代码的方法
    public void setPassword(String password) {
        this.passwordHash = password;
    }

    // UserDetails接口实现
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 基于订阅类型和账户状态确定角色
        String userRole = isPremiumUser() ? "PREMIUM_USER" : "USER";
        return Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + userRole));
    }

    @Override
    public String getPassword() {
        return passwordHash;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        // 基于订阅状态判断账户是否过期
        return isSubscriptionValid();
    }

    @Override
    public boolean isAccountNonLocked() {
        // 基于is_active字段判断账户是否被锁定
        return isActive != null ? isActive : true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        // 凭证不过期（可以根据需要添加密码过期逻辑）
        return true;
    }

    @Override
    public boolean isEnabled() {
        // 基于is_active和is_verified字段判断账户是否启用
        return (isActive != null ? isActive : true) && (isVerified != null ? isVerified : false);
    }

    // ==================== Getter/Setter 方法 ====================

    // 基本字段 (getUsername已在UserDetails接口中实现)
    public void setUsername(String username) { this.username = username; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }

    public String getFullName() { return fullName; }
    public void setFullName(String fullName) { this.fullName = fullName; }



    public String getAvatarUrl() { return avatarUrl; }
    public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getPreferredLanguage() { return preferredLanguage; }
    public void setPreferredLanguage(String preferredLanguage) { this.preferredLanguage = preferredLanguage; }

    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }

    public String getSubscriptionType() { return subscriptionType; }
    public void setSubscriptionType(String subscriptionType) { this.subscriptionType = subscriptionType; }

    public LocalDateTime getSubscriptionExpiresAt() { return subscriptionExpiresAt; }
    public void setSubscriptionExpiresAt(LocalDateTime subscriptionExpiresAt) { this.subscriptionExpiresAt = subscriptionExpiresAt; }

    public LocalDateTime getLastLoginAt() { return lastLoginAt; }
    public void setLastLoginAt(LocalDateTime lastLoginAt) { this.lastLoginAt = lastLoginAt; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Boolean getIsVerified() { return isVerified; }
    public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }

    public Map<String, Object> getVoiceSettings() { return voiceSettings; }
    public void setVoiceSettings(Map<String, Object> voiceSettings) { this.voiceSettings = voiceSettings; }

    public Map<String, Object> getNotificationSettings() { return notificationSettings; }
    public void setNotificationSettings(Map<String, Object> notificationSettings) { this.notificationSettings = notificationSettings; }

    // UserDetails相关逻辑字段（不存储在数据库中）
    public String getRole() {
        // 动态计算角色
        return isPremiumUser() ? "PREMIUM_USER" : "USER";
    }
    public void setRole(String role) {
        this.role = role;
    }

    public Boolean getAccountNonExpiredFlag() {
        return isSubscriptionValid();
    }
    public void setAccountNonExpiredFlag(Boolean accountNonExpiredFlag) {
        this.accountNonExpiredFlag = accountNonExpiredFlag;
    }

    public Boolean getAccountNonLockedFlag() {
        return isActive != null ? isActive : true;
    }
    public void setAccountNonLockedFlag(Boolean accountNonLockedFlag) {
        this.accountNonLockedFlag = accountNonLockedFlag;
    }

    public Boolean getCredentialsNonExpiredFlag() {
        return true;
    }
    public void setCredentialsNonExpiredFlag(Boolean credentialsNonExpiredFlag) {
        this.credentialsNonExpiredFlag = credentialsNonExpiredFlag;
    }

    public Boolean getEnabledFlag() {
        return (isActive != null ? isActive : true) && (isVerified != null ? isVerified : false);
    }
    public void setEnabledFlag(Boolean enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    // 为MyBatis映射添加enabled属性的getter/setter
    public Boolean getEnabled() {
        return enabledFlag;
    }
    public void setEnabled(Boolean enabled) {
        this.enabledFlag = enabled;
    }

    // ==================== 业务方法 ====================
    // (业务方法已在UserDetails接口实现之前定义)

    // 生命周期回调
    public void preUpdate() {
        // 由BaseEntity处理updatedAt字段
    }
}