package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户会话实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_sessions")
public class UserSession extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 会话唯一标识
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 会话类型：VOICE_RECORDING/CONVERSATION/SCHEDULE_MANAGEMENT
     */
    @TableField("session_type")
    private String sessionType;

    /**
     * 会话开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 会话结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 会话时长（秒）
     */
    @TableField("duration_seconds")
    private Integer durationSeconds;

    /**
     * 活动次数
     */
    @TableField("activities_count")
    private Integer activitiesCount = 0;

    /**
     * 设备信息（JSON格式）
     */
    @TableField(value = "device_info", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> deviceInfo;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理字符串
     */
    @TableField("user_agent")
    private String userAgent;

    public UserSession() {
        super();
    }

    public UserSession(Long userId, String sessionId, String sessionType) {
        this();
        this.userId = userId;
        this.sessionId = sessionId;
        this.sessionType = sessionType;
        this.startTime = LocalDateTime.now();
    }
}
