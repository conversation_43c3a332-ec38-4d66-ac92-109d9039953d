package com.voicehub.backend.config;

import com.voicehub.backend.service.SystemSettingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 应用启动配置
 * 在应用启动时执行初始化操作
 */
@Component
public class StartupConfig implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(StartupConfig.class);

    @Autowired
    private SystemSettingService systemSettingService;

    @Value("${voicehub.file.upload-dir}")
    private String uploadDir;

    @Override
    public void run(String... args) throws Exception {
        logger.info("VoiceHub应用启动初始化开始...");

        // 1. 创建上传目录
        createUploadDirectories();

        // 2. 初始化系统设置
        initializeSystemSettings();

        // 3. 打印启动信息
        printStartupInfo();

        logger.info("VoiceHub应用启动初始化完成！");
    }

    /**
     * 创建上传目录
     */
    private void createUploadDirectories() {
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                logger.info("创建上传目录: {}", uploadPath.toAbsolutePath());
            } else {
                logger.info("上传目录已存在: {}", uploadPath.toAbsolutePath());
            }

            // 创建子目录
            String[] subDirs = {"temp", "processed", "backup"};
            for (String subDir : subDirs) {
                Path subPath = uploadPath.resolve(subDir);
                if (!Files.exists(subPath)) {
                    Files.createDirectories(subPath);
                    logger.info("创建子目录: {}", subPath.toAbsolutePath());
                }
            }
        } catch (Exception e) {
            logger.error("创建上传目录失败", e);
        }
    }

    /**
     * 初始化系统设置
     */
    private void initializeSystemSettings() {
        try {
            logger.info("初始化系统设置...");
            systemSettingService.initializeDefaultSettings();
            logger.info("系统设置初始化完成");
        } catch (Exception e) {
            logger.error("初始化系统设置失败", e);
        }
    }

    /**
     * 打印启动信息
     */
    private void printStartupInfo() {
        logger.info("=".repeat(60));
        logger.info("🎉 VoiceHub智能语音助手后端服务启动成功！");
        logger.info("📁 文件上传目录: {}", new File(uploadDir).getAbsolutePath());
        logger.info("🌐 API文档地址: http://localhost:8080/api/swagger-ui.html");
        logger.info("💾 数据库连接: PostgreSQL");
        logger.info("🔐 安全认证: JWT");
        logger.info("=".repeat(60));
    }
}
