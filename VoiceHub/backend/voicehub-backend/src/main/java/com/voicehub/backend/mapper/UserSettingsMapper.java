package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 用户设置数据访问层
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface UserSettingsMapper extends BaseMapper<UserSettings> {

    /**
     * 根据用户ID查找设置
     * 
     * @param userId 用户ID
     * @return 用户设置
     */
    @Select("SELECT * FROM user_settings WHERE user_id = #{userId} AND deleted = 0")
    UserSettings findByUserId(@Param("userId") Long userId);

    /**
     * 更新用户的AI偏好设置
     * 
     * @param userId 用户ID
     * @param aiPreferences AI偏好设置JSON
     * @return 更新行数
     */
    @Update("UPDATE user_settings SET ai_preferences = #{aiPreferences}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updateAIPreferences(@Param("userId") Long userId, @Param("aiPreferences") String aiPreferences);

    /**
     * 更新用户的语音设置
     * 
     * @param userId 用户ID
     * @param speechSettings 语音设置JSON
     * @return 更新行数
     */
    @Update("UPDATE user_settings SET speech_settings = #{speechSettings}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updateSpeechSettings(@Param("userId") Long userId, @Param("speechSettings") String speechSettings);

    /**
     * 更新用户的通知设置
     * 
     * @param userId 用户ID
     * @param notificationSettings 通知设置JSON
     * @return 更新行数
     */
    @Update("UPDATE user_settings SET notification_settings = #{notificationSettings}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updateNotificationSettings(@Param("userId") Long userId, @Param("notificationSettings") String notificationSettings);

    /**
     * 更新用户的界面设置
     * 
     * @param userId 用户ID
     * @param uiSettings 界面设置JSON
     * @return 更新行数
     */
    @Update("UPDATE user_settings SET ui_settings = #{uiSettings}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updateUISettings(@Param("userId") Long userId, @Param("uiSettings") String uiSettings);

    /**
     * 更新用户的隐私设置
     * 
     * @param userId 用户ID
     * @param privacySettings 隐私设置JSON
     * @return 更新行数
     */
    @Update("UPDATE user_settings SET privacy_settings = #{privacySettings}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updatePrivacySettings(@Param("userId") Long userId, @Param("privacySettings") String privacySettings);

    /**
     * 获取使用特定AI模型的用户数量统计
     * 
     * @return 统计结果
     */
    @Select("SELECT JSON_EXTRACT(ai_preferences, '$.defaultModel') as model, COUNT(*) as count " +
            "FROM user_settings WHERE deleted = 0 " +
            "GROUP BY JSON_EXTRACT(ai_preferences, '$.defaultModel')")
    List<Map<String, Object>> getAIModelUsageStats();

    /**
     * 获取使用特定语音引擎的用户数量统计
     * 
     * @return 统计结果
     */
    @Select("SELECT JSON_EXTRACT(speech_settings, '$.defaultEngine') as engine, COUNT(*) as count " +
            "FROM user_settings WHERE deleted = 0 " +
            "GROUP BY JSON_EXTRACT(speech_settings, '$.defaultEngine')")
    List<Map<String, Object>> getSpeechEngineUsageStats();

    /**
     * 获取主题使用统计
     * 
     * @return 统计结果
     */
    @Select("SELECT JSON_EXTRACT(ui_settings, '$.theme') as theme, COUNT(*) as count " +
            "FROM user_settings WHERE deleted = 0 " +
            "GROUP BY JSON_EXTRACT(ui_settings, '$.theme')")
    List<Map<String, Object>> getThemeUsageStats();

    /**
     * 获取启用通知的用户数量
     * 
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM user_settings " +
            "WHERE JSON_EXTRACT(notification_settings, '$.enableEmailNotifications') = true " +
            "AND deleted = 0")
    Long getNotificationEnabledUserCount();

    /**
     * 获取启用双因素认证的用户数量
     * 
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM user_settings " +
            "WHERE JSON_EXTRACT(privacy_settings, '$.enableTwoFactorAuth') = true " +
            "AND deleted = 0")
    Long getTwoFactorAuthEnabledUserCount();

    /**
     * 查找需要发送摘要通知的用户
     * 
     * @param currentHour 当前小时
     * @return 用户设置列表
     */
    @Select("SELECT * FROM user_settings " +
            "WHERE JSON_EXTRACT(notification_settings, '$.enableEmailNotifications') = true " +
            "AND JSON_EXTRACT(notification_settings, '$.digestFrequency') = #{currentHour} " +
            "AND deleted = 0")
    List<UserSettings> findUsersForDigestNotification(@Param("currentHour") int currentHour);

    /**
     * 查找在免打扰时间外的用户
     * 
     * @param currentTime 当前时间（HH:mm格式）
     * @return 用户设置列表
     */
    @Select("SELECT * FROM user_settings " +
            "WHERE (JSON_EXTRACT(notification_settings, '$.enableQuietHours') = false " +
            "OR (#{currentTime} < JSON_EXTRACT(notification_settings, '$.quietHoursStart') " +
            "OR #{currentTime} > JSON_EXTRACT(notification_settings, '$.quietHoursEnd'))) " +
            "AND deleted = 0")
    List<UserSettings> findUsersOutsideQuietHours(@Param("currentTime") String currentTime);

    /**
     * 批量更新用户设置的最后访问时间
     * 
     * @param userIds 用户ID列表
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE user_settings SET updated_at = NOW() " +
            "WHERE user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "AND deleted = 0" +
            "</script>")
    int batchUpdateLastAccess(@Param("userIds") List<Long> userIds);

    /**
     * 获取设置完整度统计
     * 
     * @return 统计结果
     */
    @Select("SELECT " +
            "COUNT(*) as total_users, " +
            "SUM(CASE WHEN ai_preferences IS NOT NULL THEN 1 ELSE 0 END) as ai_configured, " +
            "SUM(CASE WHEN speech_settings IS NOT NULL THEN 1 ELSE 0 END) as speech_configured, " +
            "SUM(CASE WHEN notification_settings IS NOT NULL THEN 1 ELSE 0 END) as notification_configured, " +
            "SUM(CASE WHEN ui_settings IS NOT NULL THEN 1 ELSE 0 END) as ui_configured, " +
            "SUM(CASE WHEN privacy_settings IS NOT NULL THEN 1 ELSE 0 END) as privacy_configured " +
            "FROM user_settings WHERE deleted = 0")
    Map<String, Object> getSettingsCompletionStats();

    /**
     * 查找使用默认设置的用户
     * 
     * @return 用户设置列表
     */
    @Select("SELECT * FROM user_settings " +
            "WHERE (ai_preferences IS NULL OR ai_preferences = '{}') " +
            "AND (speech_settings IS NULL OR speech_settings = '{}') " +
            "AND (notification_settings IS NULL OR notification_settings = '{}') " +
            "AND (ui_settings IS NULL OR ui_settings = '{}') " +
            "AND (privacy_settings IS NULL OR privacy_settings = '{}') " +
            "AND deleted = 0")
    List<UserSettings> findUsersWithDefaultSettings();

    /**
     * 根据特定设置条件查找用户
     * 
     * @param settingPath JSON路径
     * @param settingValue 设置值
     * @return 用户设置列表
     */
    @Select("SELECT * FROM user_settings " +
            "WHERE JSON_EXTRACT(COALESCE(ai_preferences, speech_settings, notification_settings, ui_settings, privacy_settings), #{settingPath}) = #{settingValue} " +
            "AND deleted = 0")
    List<UserSettings> findUsersBySettingValue(@Param("settingPath") String settingPath, @Param("settingValue") Object settingValue);

    /**
     * 获取用户设置的变更历史统计
     * 
     * @param days 统计天数
     * @return 统计结果
     */
    @Select("SELECT DATE(updated_at) as date, COUNT(*) as changes " +
            "FROM user_settings " +
            "WHERE updated_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND deleted = 0 " +
            "GROUP BY DATE(updated_at) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> getSettingsChangeStats(@Param("days") int days);
}
