package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.service.SpeechToTextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 语音处理控制器
 * 处理语音识别和转换相关请求
 */
@RestController
@RequestMapping("/speech")
@Tag(name = "语音处理", description = "语音识别和转换相关接口")
@PreAuthorize("hasRole('USER')")
public class SpeechController {

    private static final Logger logger = LoggerFactory.getLogger(SpeechController.class);

    @Autowired
    private SpeechToTextService speechToTextService;

    /**
     * 语音转文本接口
     * 将上传的音频文件转换为文本内容，支持多种音频格式
     *
     * @param audioFile 音频文件，支持wav、mp3、m4a等格式
     * @param format 音频格式，默认为wav
     * @param sampleRate 采样率，默认为16000Hz
     * @return 返回识别结果，包含文本内容、置信度、语言等信息
     */
    @PostMapping("/recognize")
    @Operation(summary = "语音识别", description = "将上传的音频文件转换为文本内容")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recognizeSpeech(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam(value = "format", defaultValue = "wav") String format,
            @RequestParam(value = "rate", defaultValue = "16000") int sampleRate) {

        logger.info("接收语音识别请求: {} bytes, 格式: {}, 采样率: {}",
                   audioFile.getSize(), format, sampleRate);

        // 验证文件
        if (audioFile.isEmpty()) {
            throw BusinessException.validationError("音频文件不能为空");
        }

        // 验证文件大小 (最大10MB)
        if (audioFile.getSize() > 10 * 1024 * 1024) {
            throw BusinessException.validationError("音频文件大小不能超过10MB");
        }

        // 验证音频格式
        if (!speechToTextService.isSupportedFormat(format)) {
            throw BusinessException.validationError("不支持的音频格式: " + format);
        }

        try {
            // 获取音频数据
            byte[] audioData = audioFile.getBytes();

            // 执行语音识别
            String recognizedText = speechToTextService.convertSpeechToText(audioData, format, sampleRate);

            if (recognizedText != null && !recognizedText.trim().isEmpty()) {
                Map<String, Object> data = new HashMap<>();
                data.put("text", recognizedText.trim());
                data.put("confidence", 0.95); // 模拟置信度
                data.put("language", "zh-CN");
                data.put("duration", estimateAudioDuration(audioData.length, sampleRate));

                logger.info("语音识别成功: {}", recognizedText);
                return ResponseEntity.ok(ApiResponse.success(data, "语音识别成功"));
            } else {
                throw BusinessException.internalError("未识别到语音内容");
            }

        } catch (Exception e) {
            logger.error("语音识别失败: {}", e.getMessage());
            throw BusinessException.internalError("语音识别失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的音频格式
     * 返回系统支持的音频格式列表和推荐的采样率配置
     *
     * @return 返回支持的音频格式、推荐采样率和文件大小限制
     */
    @GetMapping("/formats")
    @Operation(summary = "获取支持的音频格式", description = "返回系统支持的音频格式列表和推荐配置")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupportedFormats() {
        String[] formats = {"wav", "pcm", "opus", "amr", "m4a"};
        Map<String, Integer> formatRates = new HashMap<>();

        for (String format : formats) {
            formatRates.put(format, speechToTextService.getRecommendedSampleRate(format));
        }

        Map<String, Object> data = new HashMap<>();
        data.put("supportedFormats", formats);
        data.put("recommendedSampleRates", formatRates);
        data.put("maxFileSize", "10MB");

        return ResponseEntity.ok(ApiResponse.success(data, "获取支持格式成功"));
    }

    /**
     * 语音识别服务状态检查
     * 检查语音识别服务的运行状态和配置信息
     *
     * @return 返回服务状态、版本信息和支持的语言列表
     */
    @GetMapping("/status")
    @Operation(summary = "检查服务状态", description = "检查语音识别服务的运行状态和配置信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getServiceStatus() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "active");
        data.put("service", "Baidu Speech Recognition");
        data.put("version", "1.0");
        data.put("supportedLanguages", new String[]{"zh-CN", "en-US"});

        return ResponseEntity.ok(ApiResponse.success(data, "服务运行正常"));
    }

    /**
     * 获取实时语音识别WebSocket连接信息
     * 返回WebSocket连接的端点和协议信息，用于实时语音识别
     *
     * @return 返回WebSocket连接配置信息
     */
    @GetMapping("/websocket-info")
    @Operation(summary = "获取WebSocket连接信息", description = "返回实时语音识别WebSocket连接配置")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getWebSocketInfo() {
        Map<String, Object> data = new HashMap<>();
        data.put("endpoint", "/ws");
        data.put("destination", "/app/speech/recognize");
        data.put("subscribe", "/user/queue/speech/result");
        data.put("protocols", new String[]{"STOMP", "SockJS"});

        return ResponseEntity.ok(ApiResponse.success(data, "获取WebSocket信息成功"));
    }



    /**
     * 估算音频时长（秒）
     */
    private double estimateAudioDuration(int audioDataLength, int sampleRate) {
        // 简单估算：假设16位单声道
        int bytesPerSecond = sampleRate * 2; // 16位 = 2字节
        return (double) audioDataLength / bytesPerSecond;
    }
}