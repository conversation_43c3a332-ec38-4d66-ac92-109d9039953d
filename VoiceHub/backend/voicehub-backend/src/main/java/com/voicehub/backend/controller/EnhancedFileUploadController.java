package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.EnhancedAudioProcessingService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 增强的文件上传控制器
 * 提供音频文件上传、处理状态查询等功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/upload")
@Tag(name = "文件上传", description = "增强的文件上传和处理功能")
@PreAuthorize("hasRole('USER')")
public class EnhancedFileUploadController {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedFileUploadController.class);

    @Autowired
    private EnhancedAudioProcessingService audioProcessingService;

    @Autowired
    private UserService userService;

    /**
     * 上传音频文件
     * 支持多种音频格式，提供质量分析和异步处理选项
     * 
     * @param file 音频文件
     * @param enableTranscription 是否启用语音转写
     * @param enableQualityAnalysis 是否启用质量分析
     * @param authentication 当前用户认证信息
     * @return 返回上传结果和处理信息
     */
    @PostMapping(value = "/audio", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传音频文件", description = "上传音频文件并进行质量分析和可选的语音转写")
    public ResponseEntity<ApiResponse<Map<String, Object>>> uploadAudio(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "enableTranscription", defaultValue = "true") boolean enableTranscription,
            @RequestParam(value = "enableQualityAnalysis", defaultValue = "true") boolean enableQualityAnalysis,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        logger.info("音频文件上传请求: userId={}, fileName={}, size={}", 
                   user.getId(), file.getOriginalFilename(), file.getSize());
        
        // 构建处理选项
        Map<String, Object> options = new HashMap<>();
        options.put("enableTranscription", enableTranscription);
        options.put("enableQualityAnalysis", enableQualityAnalysis);
        
        // 处理音频上传
        Map<String, Object> result = audioProcessingService.processAudioUpload(file, user, options);
        
        return ResponseEntity.ok(ApiResponse.success(result, "音频文件上传成功"));
    }

    /**
     * 批量上传音频文件
     * 支持一次上传多个音频文件
     * 
     * @param files 音频文件数组
     * @param enableTranscription 是否启用语音转写
     * @param authentication 当前用户认证信息
     * @return 返回批量上传结果
     */
    @PostMapping(value = "/audio/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量上传音频文件", description = "一次上传多个音频文件")
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchUploadAudio(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "enableTranscription", defaultValue = "false") boolean enableTranscription,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        if (files == null || files.length == 0) {
            throw BusinessException.validationError("请选择要上传的文件");
        }
        
        if (files.length > 10) {
            throw BusinessException.validationError("批量上传最多支持10个文件");
        }
        
        logger.info("批量音频文件上传请求: userId={}, fileCount={}", user.getId(), files.length);
        
        Map<String, Object> options = new HashMap<>();
        options.put("enableTranscription", enableTranscription);
        options.put("enableQualityAnalysis", true);
        
        java.util.List<Map<String, Object>> results = new java.util.ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            try {
                Map<String, Object> result = audioProcessingService.processAudioUpload(file, user, options);
                result.put("index", i);
                result.put("fileName", file.getOriginalFilename());
                results.add(result);
                successCount++;
            } catch (Exception e) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("index", i);
                errorResult.put("fileName", file.getOriginalFilename());
                errorResult.put("success", false);
                errorResult.put("error", e.getMessage());
                results.add(errorResult);
                failureCount++;
                logger.error("批量上传文件失败: fileName={}, error={}", file.getOriginalFilename(), e.getMessage());
            }
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("results", results);
        data.put("totalCount", files.length);
        data.put("successCount", successCount);
        data.put("failureCount", failureCount);
        
        String message = String.format("批量上传完成，成功%d个，失败%d个", successCount, failureCount);
        logger.info("批量音频上传完成: userId={}, success={}, failure={}", user.getId(), successCount, failureCount);
        
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 获取音频处理状态
     * 查询指定音频文件的处理状态
     * 
     * @param audioFileId 音频文件ID
     * @param authentication 当前用户认证信息
     * @return 返回处理状态信息
     */
    @GetMapping("/audio/{audioFileId}/status")
    @Operation(summary = "获取处理状态", description = "查询音频文件的处理状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProcessingStatus(
            @PathVariable Long audioFileId,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> status = audioProcessingService.getProcessingStatus(audioFileId);
        
        return ResponseEntity.ok(ApiResponse.success(status, "获取处理状态成功"));
    }

    /**
     * 获取支持的音频格式
     * 返回系统支持的音频格式列表
     * 
     * @return 返回支持的格式信息
     */
    @GetMapping("/audio/formats")
    @Operation(summary = "获取支持格式", description = "获取系统支持的音频格式列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupportedFormats() {
        Map<String, Object> data = new HashMap<>();
        
        // 支持的文件扩展名
        java.util.List<String> extensions = java.util.Arrays.asList(
            "wav", "mp3", "m4a", "aac", "ogg", "flac", "opus", "amr", "pcm"
        );
        
        // 推荐的格式配置
        Map<String, Object> recommendations = new HashMap<>();
        recommendations.put("bestQuality", "wav (44.1kHz, 16bit)");
        recommendations.put("balanced", "mp3 (44.1kHz, 128kbps)");
        recommendations.put("speechRecognition", "wav (16kHz, 16bit, mono)");
        recommendations.put("smallSize", "aac (22kHz, 64kbps)");
        
        // 格式特性说明
        Map<String, Object> formatInfo = new HashMap<>();
        formatInfo.put("wav", "无损格式，质量最佳，文件较大");
        formatInfo.put("mp3", "有损压缩，兼容性好，文件适中");
        formatInfo.put("m4a", "高效压缩，质量较好，文件较小");
        formatInfo.put("aac", "高效压缩，适合语音，文件小");
        formatInfo.put("flac", "无损压缩，质量好，文件中等");
        
        data.put("supportedExtensions", extensions);
        data.put("recommendations", recommendations);
        data.put("formatInfo", formatInfo);
        data.put("maxFileSize", "50MB");
        data.put("maxBatchFiles", 10);
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取支持格式成功"));
    }

    /**
     * 获取上传统计信息
     * 返回用户的上传统计数据
     * 
     * @param authentication 当前用户认证信息
     * @return 返回统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取上传统计", description = "获取用户的文件上传统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUploadStats(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        // 这里是示例数据，实际应该从数据库查询
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUploads", 0);
        stats.put("totalSize", 0);
        stats.put("successRate", 100.0);
        stats.put("averageFileSize", 0);
        stats.put("mostUsedFormat", "mp3");
        stats.put("uploadHistory", new java.util.ArrayList<>());
        
        Map<String, Object> data = new HashMap<>();
        data.put("stats", stats);
        data.put("message", "上传统计功能开发中");
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取上传统计成功"));
    }

    /**
     * 验证音频文件
     * 在实际上传前验证文件是否符合要求
     * 
     * @param file 要验证的文件
     * @param authentication 当前用户认证信息
     * @return 返回验证结果
     */
    @PostMapping(value = "/audio/validate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "验证音频文件", description = "在上传前验证音频文件是否符合要求")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateAudioFile(
            @RequestParam("file") MultipartFile file,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> validation = new HashMap<>();
        
        try {
            // 这里可以调用音频处理服务的验证方法
            // audioProcessingService.validateAudioFile(file);
            
            validation.put("valid", true);
            validation.put("fileName", file.getOriginalFilename());
            validation.put("fileSize", file.getSize());
            validation.put("contentType", file.getContentType());
            validation.put("message", "文件验证通过");
            
        } catch (BusinessException e) {
            validation.put("valid", false);
            validation.put("error", e.getMessage());
            validation.put("fileName", file.getOriginalFilename());
        }
        
        return ResponseEntity.ok(ApiResponse.success(validation, "文件验证完成"));
    }
}
