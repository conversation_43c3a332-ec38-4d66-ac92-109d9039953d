package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.AiTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI任务Mapper接口
 */
@Mapper
public interface AiTaskMapper extends BaseMapper<AiTask> {

    /**
     * 根据用户ID查找AI任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<AiTask> findByUserId(@Param("userId") Long userId);

    /**
     * 根据任务类型查找AI任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE task_type = #{taskType}")
    List<AiTask> findByTaskType(@Param("taskType") String taskType);

    /**
     * 根据状态查找AI任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE status = #{status}")
    List<AiTask> findByStatus(@Param("status") String status);

    /**
     * 根据AI模型查找任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE ai_model = #{aiModel}")
    List<AiTask> findByAiModel(@Param("aiModel") String aiModel);

    /**
     * 查找用户的特定类型任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE user_id = #{userId} AND task_type = #{taskType} ORDER BY created_at DESC")
    List<AiTask> findByUserIdAndTaskType(@Param("userId") Long userId, @Param("taskType") String taskType);

    /**
     * 查找用户的特定状态任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE user_id = #{userId} AND status = #{status} ORDER BY created_at DESC")
    List<AiTask> findByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 查找待处理的任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE status = 'pending' ORDER BY created_at ASC LIMIT #{limit}")
    List<AiTask> findPendingTasks(@Param("limit") int limit);

    /**
     * 查找处理中的任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE status = 'processing'")
    List<AiTask> findProcessingTasks();

    /**
     * 统计用户的任务数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.ai_tasks WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的成功任务数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.ai_tasks WHERE user_id = #{userId} AND status = 'completed'")
    long countCompletedByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的失败任务数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.ai_tasks WHERE user_id = #{userId} AND status = 'error'")
    long countErrorByUserId(@Param("userId") Long userId);

    /**
     * 根据时间范围查找任务
     */
    @Select("SELECT * FROM voicehub.ai_tasks WHERE created_at BETWEEN #{startDate} AND #{endDate} ORDER BY created_at DESC")
    List<AiTask> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户的token使用量
     */
    @Select("SELECT COALESCE(SUM(tokens_used), 0) FROM voicehub.ai_tasks WHERE user_id = #{userId} AND status = 'completed'")
    long sumTokensUsedByUserId(@Param("userId") Long userId);
}
