package com.voicehub.backend.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.Conversation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OpenAI Service
 * Integration with OpenAI GPT API for conversational AI
 */
@Service
public class OpenAIService {

    private static final Logger logger = LoggerFactory.getLogger(OpenAIService.class);

    @Value("${openai.api.key}")
    private String apiKey;

    @Value("${openai.api.url:https://api.openai.com/v1}")
    private String apiUrl;

    @Value("${openai.model:gpt-3.5-turbo}")
    private String model;

    @Value("${openai.max-tokens:1000}")
    private Integer maxTokens;

    @Value("${openai.temperature:0.7}")
    private Double temperature;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public OpenAIService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Generate AI response for conversation
     */
    public String generateResponse(Conversation conversation, String userMessage) {
        try {
            logger.info("Generating AI response for conversation: {}", conversation.getId());

            // Build conversation context
            List<Map<String, String>> messages = buildConversationContext(conversation, userMessage);

            // Create request payload
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);
            requestBody.put("presence_penalty", 0.1);
            requestBody.put("frequency_penalty", 0.1);

            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // Make API call
            ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl + "/chat/completions", request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return parseOpenAIResponse(response.getBody());
            } else {
                logger.error("OpenAI API error: {}", response.getStatusCode());
                return "I'm sorry, I'm having trouble processing your request right now. Please try again.";
            }

        } catch (Exception e) {
            logger.error("Failed to generate AI response: {}", e.getMessage(), e);
            return "I apologize, but I encountered an error while processing your message. Please try again.";
        }
    }

    /**
     * Generate emotional support response
     */
    public String generateEmotionalSupportResponse(String userMessage, String detectedEmotion) {
        try {
            logger.info("Generating emotional support response for emotion: {}", detectedEmotion);

            List<Map<String, String>> messages = new ArrayList<>();
            
            // System prompt for emotional support
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", buildEmotionalSupportPrompt(detectedEmotion));
            messages.add(systemMessage);

            // User message
            Map<String, String> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", userMessage);
            messages.add(userMsg);

            // Create request
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", 0.8); // Higher temperature for more empathetic responses

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl + "/chat/completions", request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return parseOpenAIResponse(response.getBody());
            } else {
                return generateFallbackEmotionalResponse(detectedEmotion);
            }

        } catch (Exception e) {
            logger.error("Failed to generate emotional support response: {}", e.getMessage());
            return generateFallbackEmotionalResponse(detectedEmotion);
        }
    }

    /**
     * Analyze emotion from text
     */
    public EmotionAnalysis analyzeEmotion(String text) {
        try {
            logger.debug("Analyzing emotion for text: {}", text.substring(0, Math.min(50, text.length())));

            List<Map<String, String>> messages = new ArrayList<>();
            
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "You are an emotion analysis expert. Analyze the emotion in the given text and respond with a JSON object containing 'emotion' (one of: happy, sad, angry, anxious, excited, neutral, frustrated, content, worried, grateful) and 'confidence' (0.0 to 1.0). Only respond with valid JSON.");
            messages.add(systemMessage);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", "Analyze the emotion in this text: \"" + text + "\"");
            messages.add(userMessage);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 100);
            requestBody.put("temperature", 0.3);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl + "/chat/completions", request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String aiResponse = parseOpenAIResponse(response.getBody());
                return parseEmotionAnalysis(aiResponse);
            }

        } catch (Exception e) {
            logger.error("Failed to analyze emotion: {}", e.getMessage());
        }

        // Fallback to basic emotion detection
        return performBasicEmotionAnalysis(text);
    }

    /**
     * Generate conversation summary
     */
    public String generateConversationSummary(Conversation conversation) {
        try {
            logger.info("Generating summary for conversation: {}", conversation.getId());

            if (conversation.getMessages().isEmpty()) {
                return "Empty conversation";
            }

            // Get recent messages for summary
            List<ChatMessage> recentMessages = conversation.getMessages().stream()
                .filter(msg -> !ChatMessage.MessageRole.SYSTEM.equals(msg.getRole()))
                .sorted((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()))
                .collect(Collectors.toList());

            if (recentMessages.isEmpty()) {
                return "No user messages found";
            }

            // Build conversation text
            StringBuilder conversationText = new StringBuilder();
            for (ChatMessage message : recentMessages) {
                String role = message.getRole() == ChatMessage.MessageRole.USER ? "User" : "Assistant";
                conversationText.append(role).append(": ").append(message.getContent()).append("\n");
            }

            List<Map<String, String>> messages = new ArrayList<>();
            
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "You are a conversation summarizer. Create a brief, meaningful summary of the following conversation in 1-2 sentences. Focus on the main topics and outcomes.");
            messages.add(systemMessage);

            Map<String, String> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", "Summarize this conversation:\n" + conversationText.toString());
            messages.add(userMessage);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 150);
            requestBody.put("temperature", 0.5);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl + "/chat/completions", request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return parseOpenAIResponse(response.getBody());
            }

        } catch (Exception e) {
            logger.error("Failed to generate conversation summary: {}", e.getMessage());
        }

        return "Conversation about " + conversation.getType().toString().toLowerCase().replace("_", " ");
    }

    // Private helper methods

    private List<Map<String, String>> buildConversationContext(Conversation conversation, String userMessage) {
        List<Map<String, String>> messages = new ArrayList<>();

        // System prompt based on conversation type
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", buildSystemPrompt(conversation.getType()));
        messages.add(systemMessage);

        // Add recent conversation history (last 10 messages)
        List<ChatMessage> recentMessages = conversation.getMessages().stream()
            .filter(msg -> !ChatMessage.MessageRole.SYSTEM.equals(msg.getRole()))
            .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
            .limit(10)
            .sorted((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()))
            .collect(Collectors.toList());

        for (ChatMessage message : recentMessages) {
            Map<String, String> msg = new HashMap<>();
            msg.put("role", message.getRole().toString().toLowerCase());
            msg.put("content", message.getContent());
            messages.add(msg);
        }

        // Add current user message
        Map<String, String> currentMessage = new HashMap<>();
        currentMessage.put("role", "user");
        currentMessage.put("content", userMessage);
        messages.add(currentMessage);

        return messages;
    }

    private String buildSystemPrompt(Conversation.ConversationType type) {
        switch (type) {
            case EMOTIONAL_SUPPORT:
                return "You are a compassionate AI companion providing emotional support. Be empathetic, understanding, and offer gentle guidance. Listen actively and validate feelings while providing helpful perspectives.";
            case SCHEDULE_HELP:
                return "You are a helpful scheduling assistant. Help users organize their time, set reminders, and manage their calendar efficiently. Be practical and organized in your responses.";
            case NOTE_ASSISTANCE:
                return "You are a note-taking and organization assistant. Help users capture, organize, and retrieve their thoughts and information effectively. Be clear and structured in your responses.";
            case BRAINSTORMING:
                return "You are a creative brainstorming partner. Help users generate ideas, explore possibilities, and think outside the box. Be imaginative and encouraging.";
            case PROBLEM_SOLVING:
                return "You are a logical problem-solving assistant. Help users break down complex problems, analyze situations, and find practical solutions. Be methodical and clear.";
            case CASUAL_CHAT:
                return "You are a friendly conversational companion. Engage in natural, enjoyable conversations while being helpful and interesting. Be personable and engaging.";
            default:
                return "You are VoiceHub, a helpful AI assistant. Provide thoughtful, accurate, and helpful responses to user queries. Be friendly, professional, and supportive.";
        }
    }

    private String buildEmotionalSupportPrompt(String emotion) {
        String basePrompt = "You are a compassionate AI companion specializing in emotional support. ";
        
        switch (emotion.toLowerCase()) {
            case "sad":
            case "depressed":
                return basePrompt + "The user seems to be feeling sad. Provide gentle comfort, validate their feelings, and offer hope and perspective.";
            case "anxious":
            case "worried":
                return basePrompt + "The user appears anxious or worried. Help them feel calmer, offer grounding techniques, and provide reassurance.";
            case "angry":
            case "frustrated":
                return basePrompt + "The user seems angry or frustrated. Help them process these feelings constructively and find healthy ways to cope.";
            case "excited":
            case "happy":
                return basePrompt + "The user appears happy or excited. Share in their joy and help them savor the positive moment.";
            default:
                return basePrompt + "Provide empathetic support and understanding, adapting your response to the user's emotional state.";
        }
    }

    private String parseOpenAIResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode choices = root.get("choices");
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode message = choices.get(0).get("message");
                if (message != null) {
                    JsonNode content = message.get("content");
                    if (content != null) {
                        return content.asText().trim();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse OpenAI response: {}", e.getMessage());
        }
        
        return "I'm sorry, I couldn't process that request properly.";
    }

    private EmotionAnalysis parseEmotionAnalysis(String aiResponse) {
        try {
            JsonNode root = objectMapper.readTree(aiResponse);
            String emotion = root.get("emotion").asText();
            double confidence = root.get("confidence").asDouble();
            return new EmotionAnalysis(emotion, confidence);
        } catch (Exception e) {
            logger.debug("Failed to parse emotion analysis JSON, using fallback");
            return new EmotionAnalysis("neutral", 0.5);
        }
    }

    private EmotionAnalysis performBasicEmotionAnalysis(String text) {
        String lowerText = text.toLowerCase();
        
        // Simple keyword-based emotion detection
        if (lowerText.contains("happy") || lowerText.contains("joy") || lowerText.contains("excited")) {
            return new EmotionAnalysis("happy", 0.7);
        } else if (lowerText.contains("sad") || lowerText.contains("depressed") || lowerText.contains("down")) {
            return new EmotionAnalysis("sad", 0.7);
        } else if (lowerText.contains("angry") || lowerText.contains("mad") || lowerText.contains("furious")) {
            return new EmotionAnalysis("angry", 0.7);
        } else if (lowerText.contains("worried") || lowerText.contains("anxious") || lowerText.contains("nervous")) {
            return new EmotionAnalysis("anxious", 0.7);
        } else if (lowerText.contains("frustrated") || lowerText.contains("annoyed")) {
            return new EmotionAnalysis("frustrated", 0.7);
        } else {
            return new EmotionAnalysis("neutral", 0.5);
        }
    }

    private String generateFallbackEmotionalResponse(String emotion) {
        switch (emotion.toLowerCase()) {
            case "sad":
                return "I can sense that you're going through a difficult time. Remember that it's okay to feel sad, and these feelings will pass. You're not alone in this.";
            case "anxious":
                return "I understand you're feeling anxious. Try taking a few deep breaths. Focus on what you can control right now, and remember that you've overcome challenges before.";
            case "angry":
                return "I can see you're feeling frustrated. It's natural to feel angry sometimes. Take a moment to breathe and think about what might help you feel better.";
            case "happy":
                return "I'm so glad to hear you're feeling good! It's wonderful when we can appreciate the positive moments in life.";
            default:
                return "Thank you for sharing your feelings with me. I'm here to listen and support you however I can.";
        }
    }

    // Inner class for emotion analysis results
    public static class EmotionAnalysis {
        private final String emotion;
        private final double confidence;

        public EmotionAnalysis(String emotion, double confidence) {
            this.emotion = emotion;
            this.confidence = confidence;
        }

        public String getEmotion() {
            return emotion;
        }

        public double getConfidence() {
            return confidence;
        }
    }

    /**
     * 检查OpenAI服务是否可用
     */
    public boolean isAvailable() {
        return apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 检查GPT-4是否可用
     */
    public boolean isGPT4Available() {
        // 这里应该检查GPT-4的访问权限
        return isAvailable(); // 简化实现
    }

    /**
     * 获取使用统计
     */
    public Map<String, Object> getUsageStats() {
        Map<String, Object> stats = new HashMap<>();

        // 这里应该从数据库查询实际统计数据
        stats.put("totalRequests", 150);
        stats.put("successfulRequests", 148);
        stats.put("failedRequests", 2);
        stats.put("averageResponseTime", 2100);
        stats.put("totalTokensUsed", 75600);
        stats.put("totalCost", 1.51);
        stats.put("lastUsed", new Date());

        return stats;
    }
}