package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统信息控制器
 * 提供详细的系统运行信息和统计数据
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/system")
@Tag(name = "系统信息", description = "系统运行信息和统计数据接口")
@PreAuthorize("hasRole('ADMIN')")
public class SystemInfoController {

    /**
     * 获取系统详细信息
     * 返回系统运行状态、内存使用、JVM信息等
     * 
     * @return 返回系统详细信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取系统信息", description = "获取系统运行状态和详细信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // 基本信息
        systemInfo.put("serviceName", "VoiceHub Backend");
        systemInfo.put("version", "1.0.0");
        systemInfo.put("timestamp", LocalDateTime.now());
        systemInfo.put("status", "RUNNING");
        
        // JVM信息
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("javaVersion", System.getProperty("java.version"));
        jvmInfo.put("javaVendor", System.getProperty("java.vendor"));
        jvmInfo.put("jvmName", runtimeBean.getVmName());
        jvmInfo.put("jvmVersion", runtimeBean.getVmVersion());
        jvmInfo.put("uptime", formatUptime(runtimeBean.getUptime()));
        jvmInfo.put("startTime", new java.util.Date(runtimeBean.getStartTime()));
        systemInfo.put("jvm", jvmInfo);
        
        // 内存信息
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        Map<String, Object> memoryInfo = new HashMap<>();
        memoryInfo.put("heapUsed", formatBytes(memoryBean.getHeapMemoryUsage().getUsed()));
        memoryInfo.put("heapMax", formatBytes(memoryBean.getHeapMemoryUsage().getMax()));
        memoryInfo.put("heapCommitted", formatBytes(memoryBean.getHeapMemoryUsage().getCommitted()));
        memoryInfo.put("nonHeapUsed", formatBytes(memoryBean.getNonHeapMemoryUsage().getUsed()));
        memoryInfo.put("nonHeapMax", formatBytes(memoryBean.getNonHeapMemoryUsage().getMax()));
        systemInfo.put("memory", memoryInfo);
        
        // 系统属性
        Map<String, Object> systemProps = new HashMap<>();
        systemProps.put("osName", System.getProperty("os.name"));
        systemProps.put("osVersion", System.getProperty("os.version"));
        systemProps.put("osArch", System.getProperty("os.arch"));
        systemProps.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("system", systemProps);
        
        return ResponseEntity.ok(ApiResponse.success(systemInfo, "获取系统信息成功"));
    }

    /**
     * 获取系统统计信息
     * 返回业务相关的统计数据
     * 
     * @return 返回统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取系统统计", description = "获取业务相关的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里应该从数据库查询实际的统计数据
        stats.put("totalUsers", 0);
        stats.put("totalVoiceNotes", 0);
        stats.put("totalConversations", 0);
        stats.put("totalAudioFiles", 0);
        stats.put("totalStorageUsed", "0 MB");
        stats.put("todayActiveUsers", 0);
        stats.put("todayNewUsers", 0);
        stats.put("todayVoiceNotes", 0);
        
        // API调用统计
        Map<String, Object> apiStats = new HashMap<>();
        apiStats.put("totalRequests", 0);
        apiStats.put("successfulRequests", 0);
        apiStats.put("failedRequests", 0);
        apiStats.put("averageResponseTime", "0ms");
        stats.put("api", apiStats);
        
        // AI服务统计
        Map<String, Object> aiStats = new HashMap<>();
        aiStats.put("totalAIRequests", 0);
        aiStats.put("speechToTextRequests", 0);
        aiStats.put("contentProcessingRequests", 0);
        aiStats.put("totalTokensUsed", 0);
        aiStats.put("estimatedCost", "$0.00");
        stats.put("ai", aiStats);
        
        stats.put("lastUpdated", LocalDateTime.now());
        stats.put("note", "统计功能开发中，显示的是示例数据");
        
        return ResponseEntity.ok(ApiResponse.success(stats, "获取系统统计成功"));
    }

    /**
     * 获取系统配置信息
     * 返回系统配置参数（敏感信息已脱敏）
     * 
     * @return 返回配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取系统配置", description = "获取系统配置信息（敏感信息已脱敏）")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 应用配置
        Map<String, Object> appConfig = new HashMap<>();
        appConfig.put("profiles", java.util.Arrays.asList("dev")); // 从Spring获取
        appConfig.put("serverPort", "8080");
        appConfig.put("contextPath", "/");
        config.put("application", appConfig);
        
        // 数据库配置（脱敏）
        Map<String, Object> dbConfig = new HashMap<>();
        dbConfig.put("driver", "PostgreSQL");
        dbConfig.put("url", "*****************************************");
        dbConfig.put("maxPoolSize", 20);
        dbConfig.put("connectionTimeout", "30000ms");
        config.put("database", dbConfig);
        
        // Redis配置（脱敏）
        Map<String, Object> redisConfig = new HashMap<>();
        redisConfig.put("host", "localhost");
        redisConfig.put("port", 6379);
        redisConfig.put("database", 0);
        redisConfig.put("timeout", "2000ms");
        config.put("redis", redisConfig);
        
        // 文件上传配置
        Map<String, Object> fileConfig = new HashMap<>();
        fileConfig.put("uploadDir", "/tmp/voicehub/audio");
        fileConfig.put("maxFileSize", "50MB");
        fileConfig.put("allowedFormats", java.util.Arrays.asList("wav", "mp3", "m4a", "aac"));
        config.put("file", fileConfig);
        
        // AI服务配置（脱敏）
        Map<String, Object> aiConfig = new HashMap<>();
        aiConfig.put("openaiEnabled", true);
        aiConfig.put("baiduSpeechEnabled", true);
        aiConfig.put("defaultModel", "gpt-3.5-turbo");
        aiConfig.put("maxTokens", 4000);
        config.put("ai", aiConfig);
        
        config.put("lastUpdated", LocalDateTime.now());
        config.put("note", "敏感信息已脱敏处理");
        
        return ResponseEntity.ok(ApiResponse.success(config, "获取系统配置成功"));
    }

    /**
     * 系统健康检查（详细版）
     * 检查各个组件的健康状态
     * 
     * @return 返回详细健康检查结果
     */
    @GetMapping("/health-check")
    @Operation(summary = "详细健康检查", description = "检查各个系统组件的健康状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> detailedHealthCheck() {
        Map<String, Object> healthCheck = new HashMap<>();
        
        // 整体状态
        healthCheck.put("status", "UP");
        healthCheck.put("timestamp", LocalDateTime.now());
        
        // 各组件状态
        Map<String, Object> components = new HashMap<>();
        
        // 数据库状态
        Map<String, Object> dbStatus = new HashMap<>();
        dbStatus.put("status", "UP");
        dbStatus.put("responseTime", "5ms");
        dbStatus.put("connections", "5/20");
        components.put("database", dbStatus);
        
        // Redis状态
        Map<String, Object> redisStatus = new HashMap<>();
        redisStatus.put("status", "UP");
        redisStatus.put("responseTime", "1ms");
        redisStatus.put("memory", "2.5MB");
        components.put("redis", redisStatus);
        
        // AI服务状态
        Map<String, Object> aiStatus = new HashMap<>();
        aiStatus.put("openai", "UP");
        aiStatus.put("baiduSpeech", "UP");
        aiStatus.put("lastCheck", LocalDateTime.now());
        components.put("aiServices", aiStatus);
        
        // 文件系统状态
        Map<String, Object> fileSystemStatus = new HashMap<>();
        fileSystemStatus.put("status", "UP");
        fileSystemStatus.put("diskSpace", "85% available");
        fileSystemStatus.put("uploadDir", "/tmp/voicehub/audio");
        components.put("fileSystem", fileSystemStatus);
        
        healthCheck.put("components", components);
        healthCheck.put("note", "健康检查功能开发中，显示的是示例数据");
        
        return ResponseEntity.ok(ApiResponse.success(healthCheck, "详细健康检查完成"));
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeMs) {
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        }
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
