package com.voicehub.backend.exception;

import com.voicehub.backend.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理应用程序中的各种异常，返回标准化的错误响应
 * 
 * <AUTHOR> Team
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理参数验证异常（@Valid注解触发）
     * @param ex 方法参数验证异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        logger.warn("参数验证失败: {}", ex.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ApiResponse<Map<String, String>> response = ApiResponse.badRequest("参数验证失败");
        response.setData(errors);
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理绑定异常
     * @param ex 绑定异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleBindException(
            BindException ex, WebRequest request) {
        
        logger.warn("数据绑定失败: {}", ex.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ApiResponse<Map<String, String>> response = ApiResponse.badRequest("数据绑定失败");
        response.setData(errors);
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理约束违反异常
     * @param ex 约束违反异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<String>> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request) {
        
        logger.warn("约束验证失败: {}", ex.getMessage());
        
        String errors = ex.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));

        ApiResponse<String> response = ApiResponse.badRequest("约束验证失败: " + errors);
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理认证异常
     * @param ex 认证异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthenticationException(
            AuthenticationException ex, WebRequest request) {
        
        logger.warn("认证失败: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.unauthorized("认证失败: " + ex.getMessage());
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    /**
     * 处理错误凭据异常
     * @param ex 错误凭据异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentialsException(
            BadCredentialsException ex, WebRequest request) {
        
        logger.warn("凭据验证失败: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.unauthorized("用户名或密码错误");
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }

    /**
     * 处理访问拒绝异常
     * @param ex 访问拒绝异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException ex, WebRequest request) {
        
        logger.warn("访问被拒绝: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.forbidden("访问被拒绝: " + ex.getMessage());
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
    }

    /**
     * 处理文件上传大小超限异常
     * @param ex 文件上传大小超限异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException ex, WebRequest request) {
        
        logger.warn("文件上传大小超限: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.badRequest("文件大小超过限制，最大允许50MB");
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理业务异常
     * @param ex 业务异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(
            BusinessException ex, WebRequest request) {
        
        logger.warn("业务异常: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.error(ex.getCode(), ex.getMessage());
        response.setPath(request.getDescription(false));
        
        HttpStatus status = HttpStatus.valueOf(ex.getCode());
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理资源未找到异常
     * @param ex 资源未找到异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleResourceNotFoundException(
            ResourceNotFoundException ex, WebRequest request) {
        
        logger.warn("资源未找到: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.notFound(ex.getMessage());
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理非法参数异常
     * @param ex 非法参数异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        logger.warn("非法参数: {}", ex.getMessage());
        
        ApiResponse<Object> response = ApiResponse.badRequest("参数错误: " + ex.getMessage());
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理运行时异常
     * @param ex 运行时异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException ex, WebRequest request) {
        
        logger.error("运行时异常: ", ex);
        
        ApiResponse<Object> response = ApiResponse.internalServerError("系统内部错误: " + ex.getMessage());
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理所有其他异常
     * @param ex 异常
     * @param request Web请求
     * @return 标准化错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGlobalException(
            Exception ex, WebRequest request) {
        
        logger.error("未处理的异常: ", ex);
        
        ApiResponse<Object> response = ApiResponse.internalServerError("系统发生未知错误，请联系管理员");
        response.setPath(request.getDescription(false));
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
