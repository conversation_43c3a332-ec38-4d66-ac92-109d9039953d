package com.voicehub.backend.service;

import com.voicehub.backend.entity.AiTask;
import com.voicehub.backend.mapper.AiTaskMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AI任务服务实现
 */
@Service
@Transactional
public class AiTaskService extends BaseServiceImpl<AiTaskMapper, AiTask> {

    @Autowired
    private AiTaskMapper aiTaskMapper;

    /**
     * 创建AI任务
     */
    public AiTask createTask(Long userId, String taskType, String inputContent, String aiModel) {
        AiTask task = new AiTask();
        task.setUserId(userId);
        task.setTaskType(taskType);
        task.setInputContent(inputContent);
        task.setAiModel(aiModel);
        task.setStatus("pending");
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        aiTaskMapper.insert(task);
        return task;
    }

    /**
     * 开始处理任务
     */
    public boolean startProcessing(Long taskId) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null && "pending".equals(task.getStatus())) {
            task.setStatus("processing");
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 完成任务
     */
    public boolean completeTask(Long taskId, String outputContent, Integer tokensUsed, Long processingTimeMs, BigDecimal costEstimate) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null && "processing".equals(task.getStatus())) {
            task.setStatus("completed");
            task.setOutputContent(outputContent);
            task.setTokensUsed(tokensUsed);
            task.setProcessingTimeMs(processingTimeMs);
            task.setCostEstimate(costEstimate);
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 任务失败
     */
    public boolean failTask(Long taskId, String errorMessage) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null && ("pending".equals(task.getStatus()) || "processing".equals(task.getStatus()))) {
            task.setStatus("error");
            task.setErrorMessage(errorMessage);
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 根据用户ID获取任务列表
     */
    public List<AiTask> getByUserId(Long userId) {
        return aiTaskMapper.findByUserId(userId);
    }

    /**
     * 根据用户ID和任务类型获取任务
     */
    public List<AiTask> getByUserIdAndTaskType(Long userId, String taskType) {
        return aiTaskMapper.findByUserIdAndTaskType(userId, taskType);
    }

    /**
     * 根据用户ID和状态获取任务
     */
    public List<AiTask> getByUserIdAndStatus(Long userId, String status) {
        return aiTaskMapper.findByUserIdAndStatus(userId, status);
    }

    /**
     * 获取待处理的任务
     */
    public List<AiTask> getPendingTasks(int limit) {
        return aiTaskMapper.findPendingTasks(limit);
    }

    /**
     * 获取处理中的任务
     */
    public List<AiTask> getProcessingTasks() {
        return aiTaskMapper.findProcessingTasks();
    }

    /**
     * 获取用户任务统计
     */
    public AiTaskStats getUserTaskStats(Long userId) {
        long totalTasks = aiTaskMapper.countByUserId(userId);
        long completedTasks = aiTaskMapper.countCompletedByUserId(userId);
        long errorTasks = aiTaskMapper.countErrorByUserId(userId);
        long totalTokens = aiTaskMapper.sumTokensUsedByUserId(userId);
        
        AiTaskStats stats = new AiTaskStats();
        stats.setTotalTasks(totalTasks);
        stats.setCompletedTasks(completedTasks);
        stats.setErrorTasks(errorTasks);
        stats.setPendingTasks(totalTasks - completedTasks - errorTasks);
        stats.setTotalTokens(totalTokens);
        stats.setSuccessRate(totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0);
        
        return stats;
    }

    /**
     * 根据任务类型获取任务
     */
    public List<AiTask> getByTaskType(String taskType) {
        return aiTaskMapper.findByTaskType(taskType);
    }

    /**
     * 根据AI模型获取任务
     */
    public List<AiTask> getByAiModel(String aiModel) {
        return aiTaskMapper.findByAiModel(aiModel);
    }

    /**
     * 根据时间范围获取任务
     */
    public List<AiTask> getByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return aiTaskMapper.findByCreatedAtBetween(startDate, endDate);
    }

    /**
     * 更新任务元数据
     */
    public boolean updateMetadata(Long taskId, Map<String, Object> metadata) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null) {
            task.setMetadata(metadata);
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 重试失败的任务
     */
    public boolean retryTask(Long taskId) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null && "error".equals(task.getStatus())) {
            task.setStatus("pending");
            task.setErrorMessage(null);
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(Long taskId) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null && ("pending".equals(task.getStatus()) || "processing".equals(task.getStatus()))) {
            task.setStatus("cancelled");
            task.setUpdatedAt(LocalDateTime.now());
            aiTaskMapper.updateById(task);
            return true;
        }
        return false;
    }

    /**
     * 删除任务
     */
    public boolean deleteTask(Long taskId) {
        AiTask task = aiTaskMapper.selectById(taskId);
        if (task != null) {
            aiTaskMapper.deleteById(taskId);
            return true;
        }
        return false;
    }

    // 内部类：AI任务统计
    public static class AiTaskStats {
        private long totalTasks;
        private long completedTasks;
        private long errorTasks;
        private long pendingTasks;
        private long totalTokens;
        private double successRate;

        // Getters and Setters
        public long getTotalTasks() { return totalTasks; }
        public void setTotalTasks(long totalTasks) { this.totalTasks = totalTasks; }
        public long getCompletedTasks() { return completedTasks; }
        public void setCompletedTasks(long completedTasks) { this.completedTasks = completedTasks; }
        public long getErrorTasks() { return errorTasks; }
        public void setErrorTasks(long errorTasks) { this.errorTasks = errorTasks; }
        public long getPendingTasks() { return pendingTasks; }
        public void setPendingTasks(long pendingTasks) { this.pendingTasks = pendingTasks; }
        public long getTotalTokens() { return totalTokens; }
        public void setTotalTokens(long totalTokens) { this.totalTokens = totalTokens; }
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }
}
