package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Map;

/**
 * AI任务处理实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_tasks")
public class AiTask extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 任务类型：summary/translate/format/analyze/creative
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 输入内容
     */
    @TableField("input_content")
    private String inputContent;

    /**
     * 输出结果
     */
    @TableField("output_content")
    private String outputContent;

    /**
     * 任务状态：pending/processing/completed/error
     */
    @TableField("status")
    private String status = "pending";

    /**
     * 使用的AI模型：gpt-4/gpt-3.5-turbo/qwen-turbo等
     */
    @TableField("ai_model")
    private String aiModel;

    /**
     * 处理时间（毫秒）
     */
    @TableField("processing_time_ms")
    private Long processingTimeMs;

    /**
     * 消耗的token数量
     */
    @TableField("tokens_used")
    private Integer tokensUsed;

    /**
     * 预估成本（美元）
     */
    @TableField("cost_estimate")
    private BigDecimal costEstimate;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 元数据（JSON格式）
     */
    @TableField(value = "metadata", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    public AiTask() {
        super();
    }

    public AiTask(Long userId, String taskType, String inputContent) {
        this();
        this.userId = userId;
        this.taskType = taskType;
        this.inputContent = inputContent;
    }
}
