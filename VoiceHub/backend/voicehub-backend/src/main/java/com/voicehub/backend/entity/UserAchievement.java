package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户成就实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_achievements")
public class UserAchievement extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 成就类型：FIRST_RECORDING/STREAK_7_DAYS/QUALITY_IMPROVEMENT
     */
    @TableField("achievement_type")
    private String achievementType;

    /**
     * 成就名称
     */
    @TableField("achievement_name")
    private String achievementName;

    /**
     * 成就描述
     */
    @TableField("description")
    private String description;

    /**
     * 获得积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 徽章图标
     */
    @TableField("badge_icon")
    private String badgeIcon;

    /**
     * 解锁时间
     */
    @TableField("unlocked_at")
    private LocalDateTime unlockedAt;

    /**
     * 成就相关数据（JSON格式）
     */
    @TableField(value = "data", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> data;

    public UserAchievement() {
        super();
    }

    public UserAchievement(Long userId, String achievementType, String achievementName) {
        this();
        this.userId = userId;
        this.achievementType = achievementType;
        this.achievementName = achievementName;
        this.unlockedAt = LocalDateTime.now();
    }
}
