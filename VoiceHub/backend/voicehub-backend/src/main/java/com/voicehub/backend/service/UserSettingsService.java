package com.voicehub.backend.service;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.UserSettings;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.mapper.UserSettingsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户设置服务
 * 管理用户的个性化设置和偏好
 * 
 * <AUTHOR> Team
 */
@Service
@Transactional
public class UserSettingsService extends BaseServiceImpl<UserSettingsMapper, UserSettings> {

    private static final Logger logger = LoggerFactory.getLogger(UserSettingsService.class);

    @Autowired
    private UserSettingsMapper userSettingsMapper;

    /**
     * 获取用户设置
     * 如果用户没有设置，则创建默认设置
     * 
     * @param user 用户
     * @return 用户设置
     */
    @Cacheable(value = "userSettings", key = "#user.id")
    public UserSettings getUserSettings(User user) {
        UserSettings settings = userSettingsMapper.findByUserId(user.getId());
        
        if (settings == null) {
            logger.info("用户{}没有设置记录，创建默认设置", user.getUsername());
            settings = createDefaultSettings(user);
        }
        
        return settings;
    }

    /**
     * 更新用户设置
     * 
     * @param user 用户
     * @param newSettings 新设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updateUserSettings(User user, UserSettings newSettings) {
        UserSettings existingSettings = getUserSettings(user);
        
        // 验证新设置的有效性
        if (!newSettings.isValid()) {
            throw BusinessException.validationError("设置参数无效，请检查输入值");
        }
        
        // 合并设置
        existingSettings.mergeSettings(newSettings);
        
        // 保存更新
        boolean success = updateById(existingSettings);
        if (!success) {
            throw BusinessException.internalError("更新用户设置失败");
        }
        
        logger.info("用户{}的设置已更新", user.getUsername());
        return existingSettings;
    }

    /**
     * 更新AI偏好设置
     * 
     * @param user 用户
     * @param aiPreferences AI偏好设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updateAIPreferences(User user, UserSettings.AIPreferences aiPreferences) {
        UserSettings settings = getUserSettings(user);
        settings.setAiPreferences(aiPreferences);
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("更新AI偏好设置失败");
        }
        
        logger.info("用户{}的AI偏好设置已更新", user.getUsername());
        return settings;
    }

    /**
     * 更新语音设置
     * 
     * @param user 用户
     * @param speechSettings 语音设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updateSpeechSettings(User user, UserSettings.SpeechSettings speechSettings) {
        UserSettings settings = getUserSettings(user);
        settings.setSpeechSettings(speechSettings);
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("更新语音设置失败");
        }
        
        logger.info("用户{}的语音设置已更新", user.getUsername());
        return settings;
    }

    /**
     * 更新通知设置
     * 
     * @param user 用户
     * @param notificationSettings 通知设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updateNotificationSettings(User user, UserSettings.NotificationSettings notificationSettings) {
        UserSettings settings = getUserSettings(user);
        settings.setNotificationSettings(notificationSettings);
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("更新通知设置失败");
        }
        
        logger.info("用户{}的通知设置已更新", user.getUsername());
        return settings;
    }

    /**
     * 更新界面设置
     * 
     * @param user 用户
     * @param uiSettings 界面设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updateUISettings(User user, UserSettings.UISettings uiSettings) {
        UserSettings settings = getUserSettings(user);
        settings.setUiSettings(uiSettings);
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("更新界面设置失败");
        }
        
        logger.info("用户{}的界面设置已更新", user.getUsername());
        return settings;
    }

    /**
     * 更新隐私设置
     * 
     * @param user 用户
     * @param privacySettings 隐私设置
     * @return 更新后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings updatePrivacySettings(User user, UserSettings.PrivacySettings privacySettings) {
        UserSettings settings = getUserSettings(user);
        settings.setPrivacySettings(privacySettings);
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("更新隐私设置失败");
        }
        
        logger.info("用户{}的隐私设置已更新", user.getUsername());
        return settings;
    }

    /**
     * 重置用户设置为默认值
     * 
     * @param user 用户
     * @return 重置后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings resetToDefaults(User user) {
        UserSettings settings = getUserSettings(user);
        settings.resetToDefaults();
        
        boolean success = updateById(settings);
        if (!success) {
            throw BusinessException.internalError("重置用户设置失败");
        }
        
        logger.info("用户{}的设置已重置为默认值", user.getUsername());
        return settings;
    }

    /**
     * 导出用户设置
     * 
     * @param user 用户
     * @return 设置数据
     */
    public Map<String, Object> exportUserSettings(User user) {
        UserSettings settings = getUserSettings(user);
        
        Map<String, Object> exportData = new HashMap<>();
        exportData.put("userId", user.getId());
        exportData.put("username", user.getUsername());
        exportData.put("exportTime", System.currentTimeMillis());
        exportData.put("aiPreferences", settings.getAiPreferences());
        exportData.put("speechSettings", settings.getSpeechSettings());
        exportData.put("notificationSettings", settings.getNotificationSettings());
        exportData.put("uiSettings", settings.getUiSettings());
        exportData.put("privacySettings", settings.getPrivacySettings());
        exportData.put("extendedSettings", settings.getExtendedSettings());
        
        logger.info("用户{}的设置已导出", user.getUsername());
        return exportData;
    }

    /**
     * 导入用户设置
     * 
     * @param user 用户
     * @param importData 导入数据
     * @return 导入后的设置
     */
    @CacheEvict(value = "userSettings", key = "#user.id")
    public UserSettings importUserSettings(User user, Map<String, Object> importData) {
        try {
            UserSettings newSettings = new UserSettings(user.getId());
            
            // 解析导入数据
            if (importData.containsKey("aiPreferences")) {
                // 这里需要根据实际的JSON结构进行解析
                // 简化实现，实际项目中需要更复杂的JSON映射
            }
            
            // 验证导入的设置
            if (!newSettings.isValid()) {
                throw BusinessException.validationError("导入的设置数据无效");
            }
            
            // 更新设置
            UserSettings existingSettings = getUserSettings(user);
            existingSettings.mergeSettings(newSettings);
            
            boolean success = updateById(existingSettings);
            if (!success) {
                throw BusinessException.internalError("导入用户设置失败");
            }
            
            logger.info("用户{}的设置已导入", user.getUsername());
            return existingSettings;
            
        } catch (Exception e) {
            logger.error("导入用户设置失败: {}", e.getMessage());
            throw BusinessException.internalError("导入设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取设置统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getSettingsStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取各种统计数据
        stats.put("aiModelUsage", userSettingsMapper.getAIModelUsageStats());
        stats.put("speechEngineUsage", userSettingsMapper.getSpeechEngineUsageStats());
        stats.put("themeUsage", userSettingsMapper.getThemeUsageStats());
        stats.put("notificationEnabledUsers", userSettingsMapper.getNotificationEnabledUserCount());
        stats.put("twoFactorAuthUsers", userSettingsMapper.getTwoFactorAuthEnabledUserCount());
        stats.put("settingsCompletion", userSettingsMapper.getSettingsCompletionStats());
        stats.put("recentChanges", userSettingsMapper.getSettingsChangeStats(30));
        
        return stats;
    }

    /**
     * 检查用户是否启用了特定功能
     * 
     * @param user 用户
     * @param feature 功能名称
     * @return 是否启用
     */
    public boolean isFeatureEnabled(User user, String feature) {
        UserSettings settings = getUserSettings(user);
        return settings.isFeatureEnabled(feature);
    }

    /**
     * 获取用户的首选AI模型
     * 
     * @param user 用户
     * @return AI模型名称
     */
    public String getPreferredAIModel(User user) {
        UserSettings settings = getUserSettings(user);
        return settings.getAiPreferences().getDefaultModel();
    }

    /**
     * 获取用户的首选语音引擎
     * 
     * @param user 用户
     * @return 语音引擎名称
     */
    public String getPreferredSpeechEngine(User user) {
        UserSettings settings = getUserSettings(user);
        return settings.getSpeechSettings().getDefaultEngine();
    }

    /**
     * 创建默认设置
     * 
     * @param user 用户
     * @return 默认设置
     */
    private UserSettings createDefaultSettings(User user) {
        UserSettings settings = new UserSettings(user.getId());
        
        boolean success = save(settings);
        if (!success) {
            throw BusinessException.internalError("创建默认用户设置失败");
        }
        
        return settings;
    }

    /**
     * 批量更新用户设置
     * 
     * @param userIds 用户ID列表
     * @param settingUpdates 设置更新
     * @return 更新数量
     */
    public int batchUpdateSettings(List<Long> userIds, Map<String, Object> settingUpdates) {
        // 这里可以实现批量更新逻辑
        // 简化实现，实际项目中需要更复杂的批量操作
        
        int updateCount = 0;
        for (Long userId : userIds) {
            try {
                // 清除缓存
                // cacheManager.evict("userSettings", userId);
                updateCount++;
            } catch (Exception e) {
                logger.error("批量更新用户{}设置失败: {}", userId, e.getMessage());
            }
        }
        
        return updateCount;
    }

    /**
     * 获取需要发送通知的用户列表
     * 
     * @param notificationType 通知类型
     * @return 用户设置列表
     */
    public List<UserSettings> getUsersForNotification(String notificationType) {
        switch (notificationType.toLowerCase()) {
            case "digest":
                int currentHour = java.time.LocalTime.now().getHour();
                return userSettingsMapper.findUsersForDigestNotification(currentHour);
            case "immediate":
                String currentTime = java.time.LocalTime.now().toString().substring(0, 5);
                return userSettingsMapper.findUsersOutsideQuietHours(currentTime);
            default:
                return List.of();
        }
    }
}
