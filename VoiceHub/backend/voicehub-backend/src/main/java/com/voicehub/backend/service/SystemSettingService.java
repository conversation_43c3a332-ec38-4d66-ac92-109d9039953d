package com.voicehub.backend.service;

import com.voicehub.backend.entity.SystemSetting;
import com.voicehub.backend.mapper.SystemSettingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系统设置服务实现
 */
@Service
@Transactional
public class SystemSettingService extends BaseServiceImpl<SystemSettingMapper, SystemSetting> {

    @Autowired
    private SystemSettingMapper systemSettingMapper;

    // 缓存系统设置
    private final Map<String, String> settingsCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存

    /**
     * 获取设置值
     */
    public String getSettingValue(String settingKey) {
        return getSettingValue(settingKey, null);
    }

    /**
     * 获取设置值（带默认值）
     */
    public String getSettingValue(String settingKey, String defaultValue) {
        // 先从缓存获取
        String cachedValue = getCachedValue(settingKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        
        // 从数据库获取
        String value = systemSettingMapper.getValueByKey(settingKey);
        if (value != null) {
            settingsCache.put(settingKey, value);
            return value;
        }
        
        return defaultValue;
    }

    /**
     * 获取布尔类型设置值
     */
    public boolean getBooleanSetting(String settingKey, boolean defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }

    /**
     * 获取整数类型设置值
     */
    public int getIntSetting(String settingKey, int defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                // 记录日志
            }
        }
        return defaultValue;
    }

    /**
     * 获取长整数类型设置值
     */
    public long getLongSetting(String settingKey, long defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                // 记录日志
            }
        }
        return defaultValue;
    }

    /**
     * 获取双精度类型设置值
     */
    public double getDoubleSetting(String settingKey, double defaultValue) {
        String value = getSettingValue(settingKey);
        if (value != null) {
            try {
                return Double.parseDouble(value);
            } catch (NumberFormatException e) {
                // 记录日志
            }
        }
        return defaultValue;
    }

    /**
     * 设置值
     */
    public boolean setSetting(String settingKey, String settingValue) {
        return setSetting(settingKey, settingValue, null, null, true);
    }

    /**
     * 设置值（完整参数）
     */
    public boolean setSetting(String settingKey, String settingValue, String settingType, String description, boolean isPublic) {
        SystemSetting setting = systemSettingMapper.findBySettingKey(settingKey);
        
        if (setting != null) {
            // 更新现有设置
            setting.setSettingValue(settingValue);
            setting.setUpdatedAt(LocalDateTime.now());
            systemSettingMapper.updateById(setting);
        } else {
            // 创建新设置
            setting = new SystemSetting();
            setting.setSettingKey(settingKey);
            setting.setSettingValue(settingValue);
            setting.setSettingType(settingType != null ? settingType : "GENERAL");
            setting.setDescription(description);
            setting.setIsPublic(isPublic);
            setting.setCreatedAt(LocalDateTime.now());
            setting.setUpdatedAt(LocalDateTime.now());
            systemSettingMapper.insert(setting);
        }
        
        // 更新缓存
        settingsCache.put(settingKey, settingValue);
        
        return true;
    }

    /**
     * 根据设置键获取设置对象
     */
    public SystemSetting getBySettingKey(String settingKey) {
        return systemSettingMapper.findBySettingKey(settingKey);
    }

    /**
     * 根据设置类型获取设置列表
     */
    public List<SystemSetting> getBySettingType(String settingType) {
        return systemSettingMapper.findBySettingType(settingType);
    }

    /**
     * 获取公开设置
     */
    public List<SystemSetting> getPublicSettings() {
        return systemSettingMapper.findPublicSettings();
    }

    /**
     * 获取私有设置
     */
    public List<SystemSetting> getPrivateSettings() {
        return systemSettingMapper.findPrivateSettings();
    }

    /**
     * 检查设置是否存在
     */
    public boolean existsSetting(String settingKey) {
        return systemSettingMapper.existsBySettingKey(settingKey);
    }

    /**
     * 搜索设置
     */
    public List<SystemSetting> searchSettings(String keyword) {
        return systemSettingMapper.findByKeyword(keyword);
    }

    /**
     * 删除设置
     */
    public boolean deleteSetting(String settingKey) {
        SystemSetting setting = systemSettingMapper.findBySettingKey(settingKey);
        if (setting != null) {
            systemSettingMapper.deleteById(setting.getId());
            settingsCache.remove(settingKey);
            return true;
        }
        return false;
    }

    /**
     * 批量设置
     */
    public boolean setBatchSettings(Map<String, String> settings) {
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            setSetting(entry.getKey(), entry.getValue());
        }
        return true;
    }

    /**
     * 获取系统设置统计
     */
    public SystemSettingStats getSystemSettingStats() {
        long totalSettings = systemSettingMapper.countAllSettings();
        long publicSettings = systemSettingMapper.countPublicSettings();
        long privateSettings = systemSettingMapper.countPrivateSettings();
        
        SystemSettingStats stats = new SystemSettingStats();
        stats.setTotalSettings(totalSettings);
        stats.setPublicSettings(publicSettings);
        stats.setPrivateSettings(privateSettings);
        
        return stats;
    }

    /**
     * 刷新缓存
     */
    public void refreshCache() {
        settingsCache.clear();
        lastCacheUpdate = 0;
    }

    /**
     * 初始化默认设置
     */
    public void initializeDefaultSettings() {
        // 系统基础设置
        setDefaultSetting("system.name", "VoiceHub", "SYSTEM", "系统名称", true);
        setDefaultSetting("system.version", "1.0.0", "SYSTEM", "系统版本", true);
        setDefaultSetting("system.maintenance", "false", "SYSTEM", "维护模式", false);
        
        // 用户相关设置
        setDefaultSetting("user.max_voice_notes", "1000", "USER", "用户最大语音笔记数", false);
        setDefaultSetting("user.max_file_size", "50", "USER", "最大文件大小(MB)", false);
        setDefaultSetting("user.session_timeout", "30", "USER", "会话超时时间(分钟)", false);
        
        // AI相关设置
        setDefaultSetting("ai.default_model", "gpt-3.5-turbo", "AI", "默认AI模型", false);
        setDefaultSetting("ai.max_tokens", "4000", "AI", "最大token数", false);
        setDefaultSetting("ai.temperature", "0.7", "AI", "AI温度参数", false);
        
        // 通知相关设置
        setDefaultSetting("notification.enabled", "true", "NOTIFICATION", "通知功能启用", false);
        setDefaultSetting("notification.email_enabled", "true", "NOTIFICATION", "邮件通知启用", false);
        setDefaultSetting("notification.push_enabled", "true", "NOTIFICATION", "推送通知启用", false);
    }

    // 私有辅助方法
    private String getCachedValue(String settingKey) {
        if (System.currentTimeMillis() - lastCacheUpdate > CACHE_EXPIRE_TIME) {
            settingsCache.clear();
            lastCacheUpdate = System.currentTimeMillis();
            return null;
        }
        return settingsCache.get(settingKey);
    }

    private void setDefaultSetting(String key, String value, String type, String description, boolean isPublic) {
        if (!existsSetting(key)) {
            setSetting(key, value, type, description, isPublic);
        }
    }

    // 内部类：系统设置统计
    public static class SystemSettingStats {
        private long totalSettings;
        private long publicSettings;
        private long privateSettings;

        // Getters and Setters
        public long getTotalSettings() { return totalSettings; }
        public void setTotalSettings(long totalSettings) { this.totalSettings = totalSettings; }
        public long getPublicSettings() { return publicSettings; }
        public void setPublicSettings(long publicSettings) { this.publicSettings = publicSettings; }
        public long getPrivateSettings() { return privateSettings; }
        public void setPrivateSettings(long privateSettings) { this.privateSettings = privateSettings; }
    }
}
