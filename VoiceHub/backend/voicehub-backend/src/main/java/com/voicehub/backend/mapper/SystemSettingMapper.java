package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.SystemSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 系统设置Mapper接口
 */
@Mapper
public interface SystemSettingMapper extends BaseMapper<SystemSetting> {

    /**
     * 根据设置键查找设置
     */
    @Select("SELECT * FROM voicehub.system_settings WHERE setting_key = #{settingKey}")
    SystemSetting findBySettingKey(@Param("settingKey") String settingKey);

    /**
     * 根据设置类型查找设置
     */
    @Select("SELECT * FROM voicehub.system_settings WHERE setting_type = #{settingType}")
    List<SystemSetting> findBySettingType(@Param("settingType") String settingType);

    /**
     * 查找公开的设置
     */
    @Select("SELECT * FROM voicehub.system_settings WHERE is_public = true")
    List<SystemSetting> findPublicSettings();

    /**
     * 查找私有的设置
     */
    @Select("SELECT * FROM voicehub.system_settings WHERE is_public = false")
    List<SystemSetting> findPrivateSettings();

    /**
     * 检查设置键是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM voicehub.system_settings WHERE setting_key = #{settingKey}")
    boolean existsBySettingKey(@Param("settingKey") String settingKey);

    /**
     * 根据设置键获取设置值
     */
    @Select("SELECT setting_value FROM voicehub.system_settings WHERE setting_key = #{settingKey}")
    String getValueByKey(@Param("settingKey") String settingKey);

    /**
     * 更新设置值
     */
    @Update("UPDATE voicehub.system_settings SET setting_value = #{settingValue}, updated_at = NOW() WHERE setting_key = #{settingKey}")
    void updateValueByKey(@Param("settingKey") String settingKey, @Param("settingValue") String settingValue);

    /**
     * 查找包含特定关键词的设置
     */
    @Select("SELECT * FROM voicehub.system_settings WHERE setting_key LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')")
    List<SystemSetting> findByKeyword(@Param("keyword") String keyword);

    /**
     * 统计设置总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.system_settings")
    long countAllSettings();

    /**
     * 统计公开设置数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.system_settings WHERE is_public = true")
    long countPublicSettings();

    /**
     * 统计私有设置数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.system_settings WHERE is_public = false")
    long countPrivateSettings();

    /**
     * 根据设置类型统计数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.system_settings WHERE setting_type = #{settingType}")
    long countBySettingType(@Param("settingType") String settingType);
}
