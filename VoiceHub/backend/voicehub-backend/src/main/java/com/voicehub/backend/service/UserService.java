package com.voicehub.backend.service;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.mapper.UserMapper;
import com.voicehub.backend.dto.RegisterRequest;
import com.voicehub.backend.dto.LoginRequest;
import com.voicehub.backend.dto.AuthResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import com.voicehub.backend.util.JwtUtil;

/**
 * 用户服务实现
 * 提供用户相关的业务逻辑处理
 */
@Service
@Transactional
public class UserService extends BaseServiceImpl<UserMapper, User> implements UserDetailsService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * Spring Security UserDetailsService实现
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            user = userMapper.findByEmail(username);
        }

        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        return user;
    }

    /**
     * 用户注册
     */
    public User registerUser(String username, String email, String password, String fullName) {
        // 检查用户名是否已存在
        if (userMapper.existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userMapper.existsByEmail(email)) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        user.setFullName(fullName);
        user.setRole("USER");
        user.setEnabledFlag(true);
        user.setAccountNonExpiredFlag(true);
        user.setAccountNonLockedFlag(true);
        user.setCredentialsNonExpiredFlag(true);
        user.setCreatedAt(LocalDateTime.now());
        
        userMapper.insert(user);
        return user;
    }

    /**
     * 根据用户名查找用户
     */
    public Optional<User> findByUsername(String username) {
        User user = userMapper.findByUsername(username);
        return Optional.ofNullable(user);
    }

    /**
     * 根据邮箱查找用户
     */
    public Optional<User> findByEmail(String email) {
        User user = userMapper.findByEmail(email);
        return Optional.ofNullable(user);
    }

    /**
     * 根据用户名或邮箱查找用户
     */
    public Optional<User> findByUsernameOrEmail(String username, String email) {
        User user = userMapper.findByUsernameOrEmail(username, email);
        return Optional.ofNullable(user);
    }

    /**
     * 检查用户名是否存在
     */
    public boolean existsByUsername(String username) {
        return userMapper.existsByUsername(username);
    }

    /**
     * 检查邮箱是否存在
     */
    public boolean existsByEmail(String email) {
        return userMapper.existsByEmail(email);
    }

    /**
     * 更新用户最后登录时间
     */
    public void updateLastLoginTime(Long userId) {
        userMapper.updateLastLoginTime(userId, LocalDateTime.now());
    }

    /**
     * 根据角色查找用户
     */
    public List<User> findByRole(User.UserRole role) {
        return userMapper.findByRole(role.name());
    }

    /**
     * 查找启用状态的用户
     */
    public List<User> findByEnabled(Boolean enabled) {
        return userMapper.findByEnabled(enabled);
    }

    /**
     * 根据创建时间范围查找用户
     */
    public List<User> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return userMapper.findByCreatedAtBetween(startDate, endDate);
    }

    /**
     * 统计用户总数
     */
    public long countAllUsers() {
        return userMapper.countAllUsers();
    }

    /**
     * 统计启用用户数
     */
    public long countEnabledUsers() {
        return userMapper.countEnabledUsers();
    }

    /**
     * 更新用户信息
     */
    public User updateUser(User user) {
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.updateById(user);
        return user;
    }

    /**
     * 更改用户密码
     */
    public void changePassword(Long userId, String newPassword) {
        User user = userMapper.selectById(userId);
        if (user != null) {
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 启用/禁用用户
     */
    public void toggleUserStatus(Long userId, boolean enabled) {
        User user = userMapper.selectById(userId);
        if (user != null) {
            user.setEnabledFlag(enabled);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);
        }
    }

    /**
     * 验证用户密码
     */
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 用户注册 - Controller需要的方法
     */
    public AuthResponse register(RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("密码确认不匹配");
        }

        // 注册用户
        User user = registerUser(request.getUsername(), request.getEmail(),
                                request.getPassword(), request.getFullName());

        // 生成JWT token
        String token = jwtUtil.generateToken(user);

        // 返回认证响应
        return new AuthResponse(token, user);
    }

    /**
     * 用户登录 - Controller需要的方法
     */
    public AuthResponse login(LoginRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(request.getUsernameOrEmail());
        if (user == null) {
            user = userMapper.findByEmail(request.getUsernameOrEmail());
        }

        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 更新最后登录时间
        updateLastLoginTime(user.getId());

        // 生成JWT token
        String token = jwtUtil.generateToken(user);

        // 返回认证响应
        return new AuthResponse(token, user);
    }

    /**
     * 检查用户名是否可用 - Controller需要的方法
     */
    public boolean isUsernameAvailable(String username) {
        return !existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用 - Controller需要的方法
     */
    public boolean isEmailAvailable(String email) {
        return !existsByEmail(email);
    }
}
