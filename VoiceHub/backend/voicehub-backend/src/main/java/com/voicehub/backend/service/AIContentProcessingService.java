package com.voicehub.backend.service;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * AI内容处理服务
 * 提供各种AI内容处理功能，包括摘要、翻译、情感分析、关键词提取等
 * 
 * <AUTHOR> Team
 */
@Service
public class AIContentProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(AIContentProcessingService.class);

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private AIModelService aiModelService;

    /**
     * 处理类型枚举
     */
    public enum ProcessingType {
        SUMMARY("摘要生成"),
        TRANSLATION("翻译"),
        SENTIMENT_ANALYSIS("情感分析"),
        KEYWORD_EXTRACTION("关键词提取"),
        CREATIVE_EXPANSION("创意扩展"),
        GRAMMAR_CHECK("语法检查"),
        STYLE_IMPROVEMENT("文风优化"),
        TEXT_CLASSIFICATION("文本分类"),
        CONTENT_GENERATION("内容生成"),
        QUESTION_ANSWERING("问答生成"),
        TEXT_COMPARISON("文本对比"),
        OUTLINE_GENERATION("大纲生成"),
        MEETING_MINUTES("会议纪要"),
        EMAIL_DRAFT("邮件起草"),
        SOCIAL_MEDIA_POST("社交媒体文案");

        private final String description;

        ProcessingType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 处理AI内容请求
     * 根据不同的处理类型调用相应的AI处理方法
     * 
     * @param content 要处理的内容
     * @param type 处理类型
     * @param options 处理选项（可选参数）
     * @param user 用户信息
     * @return 处理结果
     */
    public Map<String, Object> processContent(String content, ProcessingType type, 
                                            Map<String, Object> options, User user) {
        if (content == null || content.trim().isEmpty()) {
            throw BusinessException.validationError("处理内容不能为空");
        }

        logger.info("开始AI内容处理: type={}, userId={}, contentLength={}", 
                   type, user.getId(), content.length());

        try {
            Map<String, Object> result = new HashMap<>();
            result.put("originalContent", content);
            result.put("processingType", type.name());
            result.put("processingDescription", type.getDescription());

            switch (type) {
                case SUMMARY:
                    result.put("processedContent", generateSummary(content, options));
                    break;
                case TRANSLATION:
                    result.put("processedContent", translateContent(content, options));
                    break;
                case SENTIMENT_ANALYSIS:
                    result.putAll(analyzeSentiment(content));
                    break;
                case KEYWORD_EXTRACTION:
                    result.put("keywords", extractKeywords(content, options));
                    break;
                case CREATIVE_EXPANSION:
                    result.put("processedContent", expandCreatively(content, options));
                    break;
                case GRAMMAR_CHECK:
                    result.putAll(checkGrammar(content));
                    break;
                case STYLE_IMPROVEMENT:
                    result.put("processedContent", improveStyle(content, options));
                    break;
                case TEXT_CLASSIFICATION:
                    result.putAll(classifyText(content, options));
                    break;
                case CONTENT_GENERATION:
                    result.put("processedContent", generateContent(content, options));
                    break;
                case QUESTION_ANSWERING:
                    result.put("processedContent", generateQA(content, options));
                    break;
                case TEXT_COMPARISON:
                    result.putAll(compareTexts(content, options));
                    break;
                case OUTLINE_GENERATION:
                    result.put("processedContent", generateOutline(content, options));
                    break;
                case MEETING_MINUTES:
                    result.put("processedContent", generateMeetingMinutes(content, options));
                    break;
                case EMAIL_DRAFT:
                    result.put("processedContent", draftEmail(content, options));
                    break;
                case SOCIAL_MEDIA_POST:
                    result.put("processedContent", createSocialMediaPost(content, options));
                    break;
                default:
                    throw BusinessException.validationError("不支持的处理类型: " + type);
            }

            result.put("success", true);
            result.put("timestamp", System.currentTimeMillis());

            logger.info("AI内容处理完成: type={}, userId={}", type, user.getId());
            return result;

        } catch (Exception e) {
            logger.error("AI内容处理失败: type={}, userId={}, error={}", type, user.getId(), e.getMessage());
            throw BusinessException.internalError("AI内容处理失败: " + e.getMessage());
        }
    }

    /**
     * 生成内容摘要
     * 
     * @param content 原始内容
     * @param options 摘要选项（长度、风格等）
     * @return 摘要内容
     */
    private String generateSummary(String content, Map<String, Object> options) {
        String length = options != null ? (String) options.getOrDefault("length", "medium") : "medium";
        String style = options != null ? (String) options.getOrDefault("style", "professional") : "professional";
        
        String prompt = String.format(
            "请为以下内容生成一个%s长度的%s风格摘要：\n\n%s", 
            getLengthDescription(length), 
            getStyleDescription(style), 
            content
        );
        
        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 翻译内容
     *
     * @param content 原始内容
     * @param options 翻译选项（目标语言等）
     * @return 翻译结果
     */
    private String translateContent(String content, Map<String, Object> options) {
        String targetLanguage = options != null ? (String) options.getOrDefault("targetLanguage", "English") : "English";

        String prompt = String.format(
            "请将以下内容翻译成%s，保持原意和语调：\n\n%s",
            targetLanguage,
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 情感分析
     * 
     * @param content 要分析的内容
     * @return 情感分析结果
     */
    private Map<String, Object> analyzeSentiment(String content) {
        String prompt = String.format(
            "请对以下内容进行情感分析，返回情感倾向（积极/消极/中性）、情感强度（1-10）和主要情感词汇：\n\n%s", 
            content
        );
        
        String analysis = aiModelService.generateResponse(null, prompt);

        Map<String, Object> result = new HashMap<>();
        result.put("sentimentAnalysis", analysis);
        // 这里可以进一步解析AI返回的结果，提取结构化数据
        result.put("sentiment", "positive"); // 示例值
        result.put("intensity", 7); // 示例值

        return result;
    }

    /**
     * 提取关键词
     *
     * @param content 要分析的内容
     * @param options 提取选项（关键词数量等）
     * @return 关键词列表
     */
    private String extractKeywords(String content, Map<String, Object> options) {
        Integer count = options != null ? (Integer) options.getOrDefault("count", 10) : 10;

        String prompt = String.format(
            "请从以下内容中提取%d个最重要的关键词，按重要性排序：\n\n%s",
            count,
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 创意扩展
     * 
     * @param content 原始内容
     * @param options 扩展选项
     * @return 扩展后的内容
     */
    private String expandCreatively(String content, Map<String, Object> options) {
        String direction = options != null ? (String) options.getOrDefault("direction", "general") : "general";
        
        String prompt = String.format(
            "请基于以下内容进行创意扩展，方向：%s\n\n%s", 
            direction, 
            content
        );
        
        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 语法检查
     *
     * @param content 要检查的内容
     * @return 语法检查结果
     */
    private Map<String, Object> checkGrammar(String content) {
        String prompt = String.format(
            "请检查以下内容的语法错误，并提供修正建议：\n\n%s",
            content
        );

        String correction = aiModelService.generateResponse(null, prompt);

        Map<String, Object> result = new HashMap<>();
        result.put("grammarCheck", correction);
        result.put("hasErrors", true); // 示例值，实际应该解析AI返回结果

        return result;
    }

    /**
     * 文风优化
     *
     * @param content 原始内容
     * @param options 优化选项
     * @return 优化后的内容
     */
    private String improveStyle(String content, Map<String, Object> options) {
        String targetStyle = options != null ? (String) options.getOrDefault("targetStyle", "professional") : "professional";

        String prompt = String.format(
            "请将以下内容改写为%s风格，保持原意：\n\n%s",
            getStyleDescription(targetStyle),
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 获取长度描述
     */
    private String getLengthDescription(String length) {
        switch (length.toLowerCase()) {
            case "short": return "简短";
            case "medium": return "中等";
            case "long": return "详细";
            default: return "中等";
        }
    }

    /**
     * 获取风格描述
     */
    private String getStyleDescription(String style) {
        switch (style.toLowerCase()) {
            case "professional": return "专业";
            case "casual": return "轻松";
            case "academic": return "学术";
            case "creative": return "创意";
            default: return "专业";
        }
    }

    /**
     * 文本分类
     *
     * @param content 要分类的内容
     * @param options 分类选项
     * @return 分类结果
     */
    private Map<String, Object> classifyText(String content, Map<String, Object> options) {
        String categories = options != null ? (String) options.getOrDefault("categories", "新闻,科技,娱乐,体育,财经,教育,健康,旅游") : "新闻,科技,娱乐,体育,财经,教育,健康,旅游";

        String prompt = String.format(
            "请将以下内容分类到这些类别中：%s\n\n内容：%s\n\n请返回最匹配的类别和置信度（0-1）。",
            categories,
            content
        );

        String classification = aiModelService.generateResponse(null, prompt);

        Map<String, Object> result = new HashMap<>();
        result.put("classification", classification);
        result.put("categories", categories.split(","));
        result.put("confidence", 0.85); // 示例值

        return result;
    }

    /**
     * 内容生成
     *
     * @param content 生成提示
     * @param options 生成选项
     * @return 生成的内容
     */
    private String generateContent(String content, Map<String, Object> options) {
        String contentType = options != null ? (String) options.getOrDefault("contentType", "article") : "article";
        Integer length = options != null ? (Integer) options.getOrDefault("length", 500) : 500;

        String prompt = String.format(
            "请根据以下提示生成一篇%s，长度约%d字：\n\n%s",
            getContentTypeDescription(contentType),
            length,
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 问答生成
     *
     * @param content 基础内容
     * @param options 生成选项
     * @return 问答对
     */
    private String generateQA(String content, Map<String, Object> options) {
        Integer questionCount = options != null ? (Integer) options.getOrDefault("questionCount", 5) : 5;
        String difficulty = options != null ? (String) options.getOrDefault("difficulty", "medium") : "medium";

        String prompt = String.format(
            "请基于以下内容生成%d个%s难度的问答对：\n\n%s",
            questionCount,
            getDifficultyDescription(difficulty),
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 文本对比
     *
     * @param content 包含两个文本的内容
     * @param options 对比选项
     * @return 对比结果
     */
    private Map<String, Object> compareTexts(String content, Map<String, Object> options) {
        String[] texts = content.split("\\|\\|\\|"); // 使用|||分隔两个文本
        if (texts.length != 2) {
            throw BusinessException.validationError("文本对比需要两个文本，请用|||分隔");
        }

        String prompt = String.format(
            "请对比以下两个文本的异同点：\n\n文本1：%s\n\n文本2：%s\n\n请分析它们的相似性、差异性和各自特点。",
            texts[0].trim(),
            texts[1].trim()
        );

        String comparison = aiModelService.generateResponse(null, prompt);

        Map<String, Object> result = new HashMap<>();
        result.put("comparison", comparison);
        result.put("text1Length", texts[0].trim().length());
        result.put("text2Length", texts[1].trim().length());
        result.put("similarity", 0.75); // 示例值

        return result;
    }

    /**
     * 大纲生成
     *
     * @param content 主题内容
     * @param options 大纲选项
     * @return 生成的大纲
     */
    private String generateOutline(String content, Map<String, Object> options) {
        String outlineType = options != null ? (String) options.getOrDefault("outlineType", "article") : "article";
        Integer levels = options != null ? (Integer) options.getOrDefault("levels", 3) : 3;

        String prompt = String.format(
            "请为以下主题生成一个%s大纲，包含%d个层级：\n\n%s",
            getOutlineTypeDescription(outlineType),
            levels,
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 会议纪要生成
     *
     * @param content 会议记录
     * @param options 纪要选项
     * @return 会议纪要
     */
    private String generateMeetingMinutes(String content, Map<String, Object> options) {
        String format = options != null ? (String) options.getOrDefault("format", "standard") : "standard";

        String prompt = String.format(
            "请将以下会议记录整理成%s格式的会议纪要，包括会议主题、参与人员、讨论要点、决议事项和后续行动：\n\n%s",
            getMinutesFormatDescription(format),
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 邮件起草
     *
     * @param content 邮件要点
     * @param options 邮件选项
     * @return 邮件草稿
     */
    private String draftEmail(String content, Map<String, Object> options) {
        String emailType = options != null ? (String) options.getOrDefault("emailType", "business") : "business";
        String tone = options != null ? (String) options.getOrDefault("tone", "professional") : "professional";

        String prompt = String.format(
            "请根据以下要点起草一封%s邮件，语调%s：\n\n%s",
            getEmailTypeDescription(emailType),
            getToneDescription(tone),
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    /**
     * 社交媒体文案
     *
     * @param content 文案主题
     * @param options 文案选项
     * @return 社交媒体文案
     */
    private String createSocialMediaPost(String content, Map<String, Object> options) {
        String platform = options != null ? (String) options.getOrDefault("platform", "general") : "general";
        String style = options != null ? (String) options.getOrDefault("style", "engaging") : "engaging";

        String prompt = String.format(
            "请为%s平台创作一条%s风格的社交媒体文案：\n\n%s",
            getPlatformDescription(platform),
            getPostStyleDescription(style),
            content
        );

        return aiModelService.generateResponse(null, prompt);
    }

    // 辅助方法
    private String getContentTypeDescription(String type) {
        switch (type.toLowerCase()) {
            case "article": return "文章";
            case "blog": return "博客文章";
            case "story": return "故事";
            case "report": return "报告";
            case "review": return "评论";
            default: return "文章";
        }
    }

    private String getDifficultyDescription(String difficulty) {
        switch (difficulty.toLowerCase()) {
            case "easy": return "简单";
            case "medium": return "中等";
            case "hard": return "困难";
            default: return "中等";
        }
    }

    private String getOutlineTypeDescription(String type) {
        switch (type.toLowerCase()) {
            case "article": return "文章";
            case "presentation": return "演示";
            case "research": return "研究报告";
            case "proposal": return "提案";
            default: return "文章";
        }
    }

    private String getMinutesFormatDescription(String format) {
        switch (format.toLowerCase()) {
            case "standard": return "标准";
            case "detailed": return "详细";
            case "summary": return "摘要";
            default: return "标准";
        }
    }

    private String getEmailTypeDescription(String type) {
        switch (type.toLowerCase()) {
            case "business": return "商务";
            case "formal": return "正式";
            case "informal": return "非正式";
            case "marketing": return "营销";
            default: return "商务";
        }
    }

    private String getToneDescription(String tone) {
        switch (tone.toLowerCase()) {
            case "professional": return "专业";
            case "friendly": return "友好";
            case "formal": return "正式";
            case "casual": return "随意";
            default: return "专业";
        }
    }

    private String getPlatformDescription(String platform) {
        switch (platform.toLowerCase()) {
            case "weibo": return "微博";
            case "wechat": return "微信";
            case "linkedin": return "LinkedIn";
            case "twitter": return "Twitter";
            case "facebook": return "Facebook";
            default: return "通用";
        }
    }

    private String getPostStyleDescription(String style) {
        switch (style.toLowerCase()) {
            case "engaging": return "吸引人的";
            case "informative": return "信息性的";
            case "humorous": return "幽默的";
            case "inspirational": return "励志的";
            default: return "吸引人的";
        }
    }
}
