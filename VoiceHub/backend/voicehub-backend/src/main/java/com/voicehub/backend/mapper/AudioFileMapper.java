package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.AudioFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 音频文件Mapper接口
 */
@Mapper
public interface AudioFileMapper extends BaseMapper<AudioFile> {

    /**
     * 根据用户ID查找音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<AudioFile> findByUserId(@Param("userId") Long userId);

    /**
     * 根据文件哈希查找音频文件（去重）
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE file_hash = #{fileHash}")
    AudioFile findByFileHash(@Param("fileHash") String fileHash);

    /**
     * 根据上传来源查找音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE upload_source = #{uploadSource}")
    List<AudioFile> findByUploadSource(@Param("uploadSource") String uploadSource);

    /**
     * 根据处理状态查找音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE processing_status = #{status}")
    List<AudioFile> findByProcessingStatus(@Param("status") String status);

    /**
     * 根据转写状态查找音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE transcription_status = #{status}")
    List<AudioFile> findByTranscriptionStatus(@Param("status") String status);

    /**
     * 根据音频格式查找文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE audio_format = #{format}")
    List<AudioFile> findByAudioFormat(@Param("format") String format);

    /**
     * 统计用户的音频文件总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.audio_files WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的音频文件总大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM voicehub.audio_files WHERE user_id = #{userId}")
    long sumFileSizeByUserId(@Param("userId") Long userId);

    /**
     * 查找待处理的音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE processing_status = 'PENDING' ORDER BY created_at ASC LIMIT #{limit}")
    List<AudioFile> findPendingFiles(@Param("limit") int limit);

    /**
     * 查找待转写的音频文件
     */
    @Select("SELECT * FROM voicehub.audio_files WHERE transcription_status = 'PENDING' ORDER BY created_at ASC LIMIT #{limit}")
    List<AudioFile> findPendingTranscriptionFiles(@Param("limit") int limit);
}
