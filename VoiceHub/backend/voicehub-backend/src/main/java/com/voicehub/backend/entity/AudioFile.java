package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 音频文件实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("audio_files")
public class AudioFile extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 存储文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 原始文件名
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 文件存储路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * MIME类型
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 音频时长（秒）
     */
    @TableField("duration_seconds")
    private Integer durationSeconds;

    /**
     * 音频格式
     */
    @TableField("audio_format")
    private String audioFormat;

    /**
     * 采样率（Hz）
     */
    @TableField("sample_rate")
    private Integer sampleRate;

    /**
     * 比特率（kbps）
     */
    @TableField("bit_rate")
    private Integer bitRate;

    /**
     * 声道数，默认单声道
     */
    @TableField("channels")
    private Integer channels = 1;

    /**
     * SHA-256文件哈希值，用于去重
     */
    @TableField("file_hash")
    private String fileHash;

    /**
     * 上传来源：VOICE_NOTE/CONVERSATION/UPLOAD
     */
    @TableField("upload_source")
    private String uploadSource;

    /**
     * 处理状态：PENDING/PROCESSING/COMPLETED/FAILED
     */
    @TableField("processing_status")
    private String processingStatus = "PENDING";

    /**
     * 转写状态：PENDING/PROCESSING/COMPLETED/FAILED
     */
    @TableField("transcription_status")
    private String transcriptionStatus = "PENDING";

    public AudioFile() {
        super();
    }

    public AudioFile(Long userId, String fileName, String filePath) {
        this();
        this.userId = userId;
        this.fileName = fileName;
        this.filePath = filePath;
    }
}
