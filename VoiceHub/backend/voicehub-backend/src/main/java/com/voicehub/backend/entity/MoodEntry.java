package com.voicehub.backend.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Mood Entry Entity
 * Tracks user emotional states and mood patterns over time
 */
@TableName("mood_entries")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class MoodEntry extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField(exist = false)
    private User user;

    @TableField("mood")
    private MoodType mood;

    @TableField("intensity")
    private Integer intensity; // 1-10 scale

    @TableField("note")
    private String note;

    @TableField("triggers")
    private List<String> triggers;

    @TableField("context")
    private String context; // VOICE_SESSION, MANUAL, CONVERSATION

    @TableField("related_session_id")
    private UUID relatedSessionId;

    @TableField("location")
    private String location;

    @TableField("weather")
    private String weather;

    // Enums
    public enum MoodType {
        HAPPY, SAD, ANGRY, SURPRISED, NEUTRAL, EXCITED, CALM, FRUSTRATED, ANXIOUS, CONTENT
    }

    public enum ContextType {
        VOICE_SESSION, MANUAL, CONVERSATION, SCHEDULE_REMINDER, SYSTEM_PROMPT
    }

    // Helper methods
    public ContextType getContextEnum() {
        try {
            return ContextType.valueOf(context.toUpperCase());
        } catch (Exception e) {
            return ContextType.MANUAL;
        }
    }

    public void setContextEnum(ContextType contextType) {
        this.context = contextType.name();
    }

    public boolean isPositiveMood() {
        return mood == MoodType.HAPPY || mood == MoodType.EXCITED || mood == MoodType.CONTENT || mood == MoodType.CALM;
    }

    public boolean isNegativeMood() {
        return mood == MoodType.SAD || mood == MoodType.ANGRY || mood == MoodType.FRUSTRATED || mood == MoodType.ANXIOUS;
    }

    public boolean isHighIntensity() {
        return intensity != null && intensity >= 8;
    }

    public boolean isLowIntensity() {
        return intensity != null && intensity <= 3;
    }

    public String getIntensityLevel() {
        if (intensity == null) return "UNKNOWN";
        
        if (intensity >= 8) return "HIGH";
        if (intensity >= 6) return "MEDIUM";
        if (intensity >= 4) return "LOW";
        return "VERY_LOW";
    }

    public String getMoodCategory() {
        if (isPositiveMood()) return "POSITIVE";
        if (isNegativeMood()) return "NEGATIVE";
        return "NEUTRAL";
    }

    // Validation
    public void validate() {
        if (intensity != null && (intensity < 1 || intensity > 10)) {
            throw new IllegalArgumentException("Intensity must be between 1 and 10");
        }
    }
}