package com.voicehub.backend.service;

import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * AI模型统一管理服务
 * 管理多个AI模型的调用、选择和切换
 * 
 * <AUTHOR> Team
 */
@Service
public class AIModelService {

    private static final Logger logger = LoggerFactory.getLogger(AIModelService.class);

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private TongYiQianWenService tongYiQianWenService;

    @Autowired
    private ZhiPuAIService zhiPuAIService;

    @Value("${voicehub.ai.default-model:openai}")
    private String defaultModel;

    @Value("${voicehub.ai.fallback-enabled:true}")
    private boolean fallbackEnabled;

    /**
     * AI模型类型枚举
     */
    public enum AIModelType {
        OPENAI("OpenAI", "gpt-3.5-turbo", "OpenAI GPT模型，通用性强"),
        OPENAI_GPT4("OpenAI GPT-4", "gpt-4", "OpenAI GPT-4模型，能力更强"),
        TONGYI_QIANWEN("通义千问", "qwen-turbo", "阿里云通义千问，中文理解优秀"),
        TONGYI_QIANWEN_PLUS("通义千问Plus", "qwen-plus", "通义千问增强版，推理能力更强"),
        ZHIPU_AI("智谱AI", "glm-3-turbo", "智谱AI GLM模型，中文对话专业"),
        ZHIPU_AI_PRO("智谱AI Pro", "glm-4", "智谱AI GLM-4，多模态能力");

        private final String displayName;
        private final String modelId;
        private final String description;

        AIModelType(String displayName, String modelId, String description) {
            this.displayName = displayName;
            this.modelId = modelId;
            this.description = description;
        }

        public String getDisplayName() { return displayName; }
        public String getModelId() { return modelId; }
        public String getDescription() { return description; }
    }

    /**
     * AI模型性能信息
     */
    public static class ModelPerformance {
        private final AIModelType modelType;
        private final boolean available;
        private final int averageResponseTime; // 毫秒
        private final BigDecimal costPer1kTokens; // 每1000token成本
        private final double qualityScore; // 质量评分 1-10
        private final String[] strengths; // 优势领域

        public ModelPerformance(AIModelType modelType, boolean available, 
                              int averageResponseTime, BigDecimal costPer1kTokens, 
                              double qualityScore, String[] strengths) {
            this.modelType = modelType;
            this.available = available;
            this.averageResponseTime = averageResponseTime;
            this.costPer1kTokens = costPer1kTokens;
            this.qualityScore = qualityScore;
            this.strengths = strengths;
        }

        // Getters
        public AIModelType getModelType() { return modelType; }
        public boolean isAvailable() { return available; }
        public int getAverageResponseTime() { return averageResponseTime; }
        public BigDecimal getCostPer1kTokens() { return costPer1kTokens; }
        public double getQualityScore() { return qualityScore; }
        public String[] getStrengths() { return strengths; }
    }

    /**
     * 生成AI响应（智能模型选择）
     * 根据对话类型和内容自动选择最适合的AI模型
     * 
     * @param conversation 对话信息
     * @param prompt 用户输入
     * @param preferredModel 首选模型（可选）
     * @return AI响应
     */
    public String generateResponse(Conversation conversation, String prompt, AIModelType preferredModel) {
        AIModelType selectedModel = preferredModel != null ? preferredModel : selectBestModel(conversation, prompt);
        
        logger.info("使用AI模型生成响应: model={}, conversationId={}", 
                   selectedModel.getDisplayName(), conversation != null ? conversation.getId() : "null");
        
        try {
            return callAIModel(selectedModel, conversation, prompt);
        } catch (Exception e) {
            logger.warn("首选模型调用失败: {}, 尝试降级", e.getMessage());
            
            if (fallbackEnabled && preferredModel != null) {
                // 尝试降级到默认模型
                AIModelType fallbackModel = AIModelType.valueOf(defaultModel.toUpperCase());
                if (fallbackModel != selectedModel) {
                    try {
                        return callAIModel(fallbackModel, conversation, prompt);
                    } catch (Exception fallbackException) {
                        logger.error("降级模型也调用失败: {}", fallbackException.getMessage());
                    }
                }
            }
            
            throw BusinessException.internalError("AI模型调用失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI响应（简化版本）
     */
    public String generateResponse(Conversation conversation, String prompt) {
        return generateResponse(conversation, prompt, null);
    }

    /**
     * 智能选择最佳AI模型
     * 根据对话类型、内容特征和模型性能选择最适合的模型
     * 
     * @param conversation 对话信息
     * @param prompt 用户输入
     * @return 推荐的AI模型
     */
    private AIModelType selectBestModel(Conversation conversation, String prompt) {
        // 根据对话类型选择模型
        if (conversation != null) {
            switch (conversation.getType()) {
                case EMOTIONAL_SUPPORT:
                    return AIModelType.TONGYI_QIANWEN; // 中文情感理解更好
                case SCHEDULE_HELP:
                case NOTE_ASSISTANCE:
                    return AIModelType.ZHIPU_AI; // 结构化任务处理好
                case BRAINSTORMING:
                    return AIModelType.OPENAI_GPT4; // 创意能力强
                case PROBLEM_SOLVING:
                    return AIModelType.ZHIPU_AI_PRO; // 逻辑推理能力强
                default:
                    break;
            }
        }
        
        // 根据内容特征选择
        if (containsChinese(prompt)) {
            return AIModelType.TONGYI_QIANWEN; // 中文内容优先使用通义千问
        }
        
        if (isComplexTask(prompt)) {
            return AIModelType.OPENAI_GPT4; // 复杂任务使用GPT-4
        }
        
        // 默认使用配置的模型
        try {
            return AIModelType.valueOf(defaultModel.toUpperCase());
        } catch (IllegalArgumentException e) {
            return AIModelType.OPENAI; // 最终降级
        }
    }

    /**
     * 调用指定的AI模型
     */
    private String callAIModel(AIModelType modelType, Conversation conversation, String prompt) {
        switch (modelType) {
            case OPENAI:
            case OPENAI_GPT4:
                return openAIService.generateResponse(conversation, prompt);
            case TONGYI_QIANWEN:
            case TONGYI_QIANWEN_PLUS:
                return tongYiQianWenService.generateResponse(conversation, prompt, modelType.getModelId());
            case ZHIPU_AI:
            case ZHIPU_AI_PRO:
                return zhiPuAIService.generateResponse(conversation, prompt, modelType.getModelId());
            default:
                throw BusinessException.internalError("不支持的AI模型: " + modelType);
        }
    }

    /**
     * 获取所有可用的AI模型信息
     * 
     * @return 模型性能信息列表
     */
    public List<ModelPerformance> getAvailableModels() {
        List<ModelPerformance> models = new ArrayList<>();
        
        // OpenAI模型
        models.add(new ModelPerformance(
            AIModelType.OPENAI, 
            openAIService.isAvailable(),
            2000, 
            new BigDecimal("0.002"), 
            8.5,
            new String[]{"通用对话", "创意写作", "代码生成"}
        ));
        
        models.add(new ModelPerformance(
            AIModelType.OPENAI_GPT4, 
            openAIService.isGPT4Available(),
            5000, 
            new BigDecimal("0.03"), 
            9.2,
            new String[]{"复杂推理", "专业分析", "高质量创作"}
        ));
        
        // 通义千问模型
        models.add(new ModelPerformance(
            AIModelType.TONGYI_QIANWEN, 
            tongYiQianWenService.isAvailable(),
            1500, 
            new BigDecimal("0.001"), 
            8.0,
            new String[]{"中文理解", "文化背景", "本土化服务"}
        ));
        
        models.add(new ModelPerformance(
            AIModelType.TONGYI_QIANWEN_PLUS, 
            tongYiQianWenService.isPlusAvailable(),
            2500, 
            new BigDecimal("0.008"), 
            8.8,
            new String[]{"高级推理", "专业知识", "复杂中文任务"}
        ));
        
        // 智谱AI模型
        models.add(new ModelPerformance(
            AIModelType.ZHIPU_AI, 
            zhiPuAIService.isAvailable(),
            1800, 
            new BigDecimal("0.005"), 
            8.3,
            new String[]{"中文对话", "知识问答", "逻辑推理"}
        ));
        
        models.add(new ModelPerformance(
            AIModelType.ZHIPU_AI_PRO, 
            zhiPuAIService.isProAvailable(),
            3000, 
            new BigDecimal("0.015"), 
            9.0,
            new String[]{"多模态理解", "高级推理", "专业分析"}
        ));
        
        return models;
    }

    /**
     * 获取模型使用统计
     * 
     * @return 使用统计信息
     */
    public Map<String, Object> getModelUsageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 这里应该从数据库查询实际的使用统计
        Map<String, Integer> usageCount = new HashMap<>();
        usageCount.put("openai", 150);
        usageCount.put("tongyi_qianwen", 89);
        usageCount.put("zhipu_ai", 67);
        
        Map<String, Double> successRate = new HashMap<>();
        successRate.put("openai", 98.5);
        successRate.put("tongyi_qianwen", 97.2);
        successRate.put("zhipu_ai", 96.8);
        
        Map<String, Integer> avgResponseTime = new HashMap<>();
        avgResponseTime.put("openai", 2100);
        avgResponseTime.put("tongyi_qianwen", 1600);
        avgResponseTime.put("zhipu_ai", 1900);
        
        stats.put("usageCount", usageCount);
        stats.put("successRate", successRate);
        stats.put("averageResponseTime", avgResponseTime);
        stats.put("totalRequests", 306);
        stats.put("totalCost", new BigDecimal("15.67"));
        stats.put("lastUpdated", new Date());
        
        return stats;
    }

    /**
     * 检查文本是否包含中文
     */
    private boolean containsChinese(String text) {
        if (text == null) return false;
        return text.matches(".*[\\u4e00-\\u9fa5].*");
    }

    /**
     * 判断是否为复杂任务
     */
    private boolean isComplexTask(String prompt) {
        if (prompt == null) return false;
        
        // 简单的复杂度判断逻辑
        String[] complexKeywords = {"分析", "推理", "解释", "比较", "评估", "设计", "规划", "策略"};
        String lowerPrompt = prompt.toLowerCase();
        
        for (String keyword : complexKeywords) {
            if (lowerPrompt.contains(keyword)) {
                return true;
            }
        }
        
        return prompt.length() > 200; // 长文本认为是复杂任务
    }

    /**
     * 测试所有AI模型的可用性
     * 
     * @return 测试结果
     */
    public Map<String, Object> testAllModels() {
        Map<String, Object> testResults = new HashMap<>();
        String testPrompt = "你好，请简单介绍一下自己。";
        
        for (AIModelType modelType : AIModelType.values()) {
            Map<String, Object> result = new HashMap<>();
            try {
                long startTime = System.currentTimeMillis();
                String response = callAIModel(modelType, null, testPrompt);
                long responseTime = System.currentTimeMillis() - startTime;
                
                result.put("available", true);
                result.put("responseTime", responseTime);
                result.put("response", response.substring(0, Math.min(100, response.length())) + "...");
                
            } catch (Exception e) {
                result.put("available", false);
                result.put("error", e.getMessage());
            }
            
            testResults.put(modelType.name().toLowerCase(), result);
        }
        
        return testResults;
    }
}
