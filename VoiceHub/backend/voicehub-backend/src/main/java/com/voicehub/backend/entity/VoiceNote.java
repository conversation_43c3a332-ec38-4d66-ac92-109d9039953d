package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 语音笔记实体类 - 完全按照数据库表结构设计
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("voice_notes")
public class VoiceNote extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 笔记标题
     */
    @NotBlank(message = "标题不能为空")
    @TableField("title")
    private String title;

    /**
     * 语音转文字结果
     */
    @TableField("transcription")
    private String transcription;

    /**
     * 音频文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 录音时长（秒）
     */
    @TableField("duration_seconds")
    private Integer durationSeconds;

    /**
     * 音频格式：mp3/wav/m4a/ogg
     */
    @TableField("audio_format")
    private String audioFormat;

    /**
     * 采样率（Hz）
     */
    @TableField("sample_rate")
    private Integer sampleRate;

    /**
     * 比特率（kbps）
     */
    @TableField("bit_rate")
    private Integer bitRate;

    /**
     * 笔记分类：GENERAL/MEETING/PERSONAL/STUDY/OTHER
     */
    @TableField("category")
    private String category = "GENERAL";

    /**
     * 标签数组
     */
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 转写置信度（0.0000-1.0000）
     */
    @TableField("transcription_confidence")
    private BigDecimal transcriptionConfidence;

    /**
     * 语言代码
     */
    @TableField("language")
    private String language;

    /**
     * AI生成的摘要
     */
    @TableField("ai_summary")
    private String aiSummary;

    /**
     * AI提取的关键词数组
     */
    @TableField(value = "ai_keywords", typeHandler = JacksonTypeHandler.class)
    private List<String> aiKeywords;

    /**
     * AI自动分类结果
     */
    @TableField("ai_category")
    private String aiCategory;

    /**
     * AI处理状态：pending/processing/completed/error
     */
    @TableField("ai_processing_status")
    private String aiProcessingStatus = "pending";

    /**
     * 是否收藏
     */
    @TableField("is_favorite")
    private Boolean isFavorite = false;

    /**
     * 是否归档
     */
    @TableField("is_archived")
    private Boolean isArchived = false;

    // 枚举定义 - 为了兼容现有代码
    public enum Category {
        GENERAL, MEETING, PERSONAL, STUDY, OTHER
    }

    public enum Priority {
        LOW, MEDIUM, HIGH, URGENT
    }

    // 构造函数
    public VoiceNote() {
        super();
    }

    public VoiceNote(Long userId, String title) {
        this();
        this.userId = userId;
        this.title = title;
    }

    // 兼容旧代码的方法
    public String getAudioFilePath() {
        return filePath;
    }

    public void setAudioFilePath(String audioFilePath) {
        this.filePath = audioFilePath;
    }

    public String getAudioFileName() {
        return filePath != null ? filePath.substring(filePath.lastIndexOf('/') + 1) : null;
    }

    public void setAudioFileName(String audioFileName) {
        // 这个方法保留为兼容性，但实际不设置值
    }

    public Long getAudioFileSize() {
        return fileSize;
    }

    public void setAudioFileSize(Long audioFileSize) {
        this.fileSize = audioFileSize;
    }

    public Integer getAudioDurationSeconds() {
        return durationSeconds;
    }

    public void setAudioDurationSeconds(Integer audioDurationSeconds) {
        this.durationSeconds = audioDurationSeconds;
    }

    public String getLanguageDetected() {
        return language;
    }

    public void setLanguageDetected(String languageDetected) {
        this.language = languageDetected;
    }

    public Category getCategory() {
        if (category == null) return Category.GENERAL;
        try {
            return Category.valueOf(category.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Category.GENERAL;
        }
    }

    public void setCategory(Category category) {
        this.category = category != null ? category.name() : "GENERAL";
    }

    // 工具方法
    public String getFormattedDuration() {
        if (durationSeconds == null || durationSeconds == 0) {
            return "0:00";
        }
        
        int minutes = durationSeconds / 60;
        int seconds = durationSeconds % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    public String getFormattedFileSize() {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    public boolean isRecent() {
        if (getCreatedAt() == null) return false;
        return getCreatedAt().isAfter(java.time.LocalDateTime.now().minusDays(7));
    }

    // 更多兼容性方法
    public void setDescription(String description) {
        // 兼容性方法，不实际设置值
    }

    public String getDescription() {
        return aiSummary; // 使用AI摘要作为描述
    }

    public void setPriority(Priority priority) {
        // 兼容性方法，不实际设置值
    }

    public Priority getPriority() {
        return Priority.MEDIUM; // 默认返回中等优先级
    }

    public void setUser(User user) {
        if (user != null) {
            this.userId = user.getId();
        }
    }

    public User getUser() {
        // 返回null，需要通过关联查询获取
        return null;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    public Double getTranscriptionConfidence() {
        return transcriptionConfidence != null ? transcriptionConfidence.doubleValue() : null;
    }

    public void setTranscriptionConfidence(Double confidence) {
        this.transcriptionConfidence = confidence != null ? BigDecimal.valueOf(confidence) : null;
    }

    public java.util.Set<String> getTags() {
        return tags != null ? new java.util.HashSet<>(tags) : new java.util.HashSet<>();
    }

    public void setTags(java.util.Set<String> tags) {
        this.tags = tags != null ? new java.util.ArrayList<>(tags) : null;
    }
}
