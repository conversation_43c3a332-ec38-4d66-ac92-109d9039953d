package com.voicehub.backend.service;

import com.voicehub.backend.entity.UserProfile;
import com.voicehub.backend.mapper.UserProfileMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详细资料服务实现
 */
@Service
@Transactional
public class UserProfileService extends BaseServiceImpl<UserProfileMapper, UserProfile> {

    @Autowired
    private UserProfileMapper userProfileMapper;

    /**
     * 根据用户ID获取用户资料
     */
    public UserProfile getByUserId(Long userId) {
        return userProfileMapper.findByUserId(userId);
    }

    /**
     * 创建或更新用户资料
     */
    public UserProfile createOrUpdateProfile(Long userId, UserProfile profile) {
        UserProfile existingProfile = userProfileMapper.findByUserId(userId);
        
        if (existingProfile != null) {
            // 更新现有资料
            existingProfile.setBio(profile.getBio());
            existingProfile.setOccupation(profile.getOccupation());
            existingProfile.setCompany(profile.getCompany());
            existingProfile.setLocation(profile.getLocation());
            existingProfile.setBirthDate(profile.getBirthDate());
            existingProfile.setGender(profile.getGender());
            existingProfile.setInterests(profile.getInterests());
            existingProfile.setGoals(profile.getGoals());
            existingProfile.setPrivacySettings(profile.getPrivacySettings());
            existingProfile.setUpdatedAt(LocalDateTime.now());
            
            userProfileMapper.updateById(existingProfile);
            return existingProfile;
        } else {
            // 创建新资料
            profile.setUserId(userId);
            profile.setCreatedAt(LocalDateTime.now());
            profile.setUpdatedAt(LocalDateTime.now());
            
            userProfileMapper.insert(profile);
            return profile;
        }
    }

    /**
     * 检查用户是否已有资料
     */
    public boolean hasProfile(Long userId) {
        return userProfileMapper.existsByUserId(userId);
    }

    /**
     * 根据职业查找用户资料
     */
    public List<UserProfile> findByOccupation(String occupation) {
        return userProfileMapper.findByOccupation(occupation);
    }

    /**
     * 根据公司查找用户资料
     */
    public List<UserProfile> findByCompany(String company) {
        return userProfileMapper.findByCompany(company);
    }

    /**
     * 根据地区查找用户资料
     */
    public List<UserProfile> findByLocation(String location) {
        return userProfileMapper.findByLocation(location);
    }

    /**
     * 根据性别查找用户资料
     */
    public List<UserProfile> findByGender(String gender) {
        return userProfileMapper.findByGender(gender);
    }

    /**
     * 删除用户资料
     */
    public boolean deleteByUserId(Long userId) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            userProfileMapper.deleteById(profile.getId());
            return true;
        }
        return false;
    }

    /**
     * 更新用户隐私设置
     */
    public boolean updatePrivacySettings(Long userId, java.util.Map<String, Object> privacySettings) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            profile.setPrivacySettings(privacySettings);
            profile.setUpdatedAt(LocalDateTime.now());
            userProfileMapper.updateById(profile);
            return true;
        }
        return false;
    }

    /**
     * 添加用户兴趣
     */
    public boolean addInterest(Long userId, String interest) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            List<String> interests = profile.getInterests();
            if (interests == null) {
                interests = new java.util.ArrayList<>();
            }
            if (!interests.contains(interest)) {
                interests.add(interest);
                profile.setInterests(interests);
                profile.setUpdatedAt(LocalDateTime.now());
                userProfileMapper.updateById(profile);
                return true;
            }
        }
        return false;
    }

    /**
     * 移除用户兴趣
     */
    public boolean removeInterest(Long userId, String interest) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            List<String> interests = profile.getInterests();
            if (interests != null && interests.contains(interest)) {
                interests.remove(interest);
                profile.setInterests(interests);
                profile.setUpdatedAt(LocalDateTime.now());
                userProfileMapper.updateById(profile);
                return true;
            }
        }
        return false;
    }

    /**
     * 添加用户目标
     */
    public boolean addGoal(Long userId, String goal) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            List<String> goals = profile.getGoals();
            if (goals == null) {
                goals = new java.util.ArrayList<>();
            }
            if (!goals.contains(goal)) {
                goals.add(goal);
                profile.setGoals(goals);
                profile.setUpdatedAt(LocalDateTime.now());
                userProfileMapper.updateById(profile);
                return true;
            }
        }
        return false;
    }

    /**
     * 移除用户目标
     */
    public boolean removeGoal(Long userId, String goal) {
        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile != null) {
            List<String> goals = profile.getGoals();
            if (goals != null && goals.contains(goal)) {
                goals.remove(goal);
                profile.setGoals(goals);
                profile.setUpdatedAt(LocalDateTime.now());
                userProfileMapper.updateById(profile);
                return true;
            }
        }
        return false;
    }
}
