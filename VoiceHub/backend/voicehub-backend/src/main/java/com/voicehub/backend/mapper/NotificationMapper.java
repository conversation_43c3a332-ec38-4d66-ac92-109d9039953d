package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知Mapper接口
 */
@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 根据用户ID查找通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Notification> findByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找未读通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE user_id = #{userId} AND is_read = false ORDER BY created_at DESC")
    List<Notification> findUnreadByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找已读通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE user_id = #{userId} AND is_read = true ORDER BY created_at DESC")
    List<Notification> findReadByUserId(@Param("userId") Long userId);

    /**
     * 根据通知类型查找通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE type = #{type}")
    List<Notification> findByType(@Param("type") String type);

    /**
     * 根据优先级查找通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE priority = #{priority}")
    List<Notification> findByPriority(@Param("priority") String priority);

    /**
     * 查找用户的特定类型通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE user_id = #{userId} AND type = #{type} ORDER BY created_at DESC")
    List<Notification> findByUserIdAndType(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 查找用户的特定优先级通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE user_id = #{userId} AND priority = #{priority} ORDER BY created_at DESC")
    List<Notification> findByUserIdAndPriority(@Param("userId") Long userId, @Param("priority") String priority);

    /**
     * 查找计划发送的通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE scheduled_for <= #{now} AND scheduled_for IS NOT NULL ORDER BY scheduled_for ASC")
    List<Notification> findScheduledNotifications(@Param("now") LocalDateTime now);

    /**
     * 查找已过期的通知
     */
    @Select("SELECT * FROM voicehub.notifications WHERE expires_at <= #{now} AND expires_at IS NOT NULL")
    List<Notification> findExpiredNotifications(@Param("now") LocalDateTime now);

    /**
     * 统计用户的未读通知数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.notifications WHERE user_id = #{userId} AND is_read = false")
    long countUnreadByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的通知总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.notifications WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 标记通知为已读
     */
    @Update("UPDATE voicehub.notifications SET is_read = true, updated_at = NOW() WHERE id = #{notificationId}")
    void markAsRead(@Param("notificationId") Long notificationId);

    /**
     * 标记用户所有通知为已读
     */
    @Update("UPDATE voicehub.notifications SET is_read = true, updated_at = NOW() WHERE user_id = #{userId} AND is_read = false")
    void markAllAsReadByUserId(@Param("userId") Long userId);

    /**
     * 标记用户特定类型通知为已读
     */
    @Update("UPDATE voicehub.notifications SET is_read = true, updated_at = NOW() WHERE user_id = #{userId} AND type = #{type} AND is_read = false")
    void markAsReadByUserIdAndType(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 删除过期的通知
     */
    @Update("DELETE FROM voicehub.notifications WHERE expires_at <= #{now} AND expires_at IS NOT NULL")
    void deleteExpiredNotifications(@Param("now") LocalDateTime now);
}
