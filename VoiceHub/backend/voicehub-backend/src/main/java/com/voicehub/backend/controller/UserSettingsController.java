package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.UserSettings;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.service.UserSettingsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户设置控制器
 * 提供用户个性化设置和偏好管理功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/user/settings")
@Tag(name = "用户设置", description = "用户个性化设置和偏好管理接口")
@PreAuthorize("hasRole('USER')")
public class UserSettingsController {

    private static final Logger logger = LoggerFactory.getLogger(UserSettingsController.class);

    @Autowired
    private UserSettingsService userSettingsService;

    @Autowired
    private UserService userService;

    /**
     * 获取用户设置
     * 返回用户的完整设置信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回用户设置
     */
    @GetMapping
    @Operation(summary = "获取用户设置", description = "获取当前用户的完整设置信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserSettings(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings settings = userSettingsService.getUserSettings(user);
        
        Map<String, Object> data = new HashMap<>();
        data.put("settings", settings);
        data.put("summary", settings.getSummary());
        data.put("lastUpdated", settings.getUpdatedAt());
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取用户设置成功"));
    }

    /**
     * 更新用户设置
     * 更新用户的完整设置信息
     * 
     * @param newSettings 新的设置信息
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping
    @Operation(summary = "更新用户设置", description = "更新用户的完整设置信息")
    public ResponseEntity<ApiResponse<UserSettings>> updateUserSettings(
            @RequestBody UserSettings newSettings,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updateUserSettings(user, newSettings);
        
        logger.info("用户{}更新了设置", user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "用户设置更新成功"));
    }

    /**
     * 更新AI偏好设置
     * 单独更新AI相关的偏好设置
     * 
     * @param aiPreferences AI偏好设置
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping("/ai-preferences")
    @Operation(summary = "更新AI偏好设置", description = "更新用户的AI模型偏好和相关设置")
    public ResponseEntity<ApiResponse<UserSettings>> updateAIPreferences(
            @RequestBody UserSettings.AIPreferences aiPreferences,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updateAIPreferences(user, aiPreferences);
        
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "AI偏好设置更新成功"));
    }

    /**
     * 更新语音设置
     * 单独更新语音识别相关设置
     * 
     * @param speechSettings 语音设置
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping("/speech-settings")
    @Operation(summary = "更新语音设置", description = "更新用户的语音识别和处理设置")
    public ResponseEntity<ApiResponse<UserSettings>> updateSpeechSettings(
            @RequestBody UserSettings.SpeechSettings speechSettings,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updateSpeechSettings(user, speechSettings);
        
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "语音设置更新成功"));
    }

    /**
     * 更新通知设置
     * 单独更新通知相关设置
     * 
     * @param notificationSettings 通知设置
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping("/notification-settings")
    @Operation(summary = "更新通知设置", description = "更新用户的通知偏好和免打扰设置")
    public ResponseEntity<ApiResponse<UserSettings>> updateNotificationSettings(
            @RequestBody UserSettings.NotificationSettings notificationSettings,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updateNotificationSettings(user, notificationSettings);
        
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "通知设置更新成功"));
    }

    /**
     * 更新界面设置
     * 单独更新用户界面相关设置
     * 
     * @param uiSettings 界面设置
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping("/ui-settings")
    @Operation(summary = "更新界面设置", description = "更新用户的界面主题、语言和显示设置")
    public ResponseEntity<ApiResponse<UserSettings>> updateUISettings(
            @RequestBody UserSettings.UISettings uiSettings,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updateUISettings(user, uiSettings);
        
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "界面设置更新成功"));
    }

    /**
     * 更新隐私设置
     * 单独更新隐私和安全相关设置
     * 
     * @param privacySettings 隐私设置
     * @param authentication 当前用户认证信息
     * @return 返回更新后的设置
     */
    @PutMapping("/privacy-settings")
    @Operation(summary = "更新隐私设置", description = "更新用户的隐私保护和安全设置")
    public ResponseEntity<ApiResponse<UserSettings>> updatePrivacySettings(
            @RequestBody UserSettings.PrivacySettings privacySettings,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings updatedSettings = userSettingsService.updatePrivacySettings(user, privacySettings);
        
        return ResponseEntity.ok(ApiResponse.success(updatedSettings, "隐私设置更新成功"));
    }

    /**
     * 重置设置为默认值
     * 将用户设置重置为系统默认值
     * 
     * @param authentication 当前用户认证信息
     * @return 返回重置后的设置
     */
    @PostMapping("/reset")
    @Operation(summary = "重置设置", description = "将用户设置重置为系统默认值")
    public ResponseEntity<ApiResponse<UserSettings>> resetToDefaults(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings resetSettings = userSettingsService.resetToDefaults(user);
        
        logger.info("用户{}重置了设置", user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(resetSettings, "设置已重置为默认值"));
    }

    /**
     * 导出用户设置
     * 导出用户的完整设置数据
     * 
     * @param authentication 当前用户认证信息
     * @return 返回设置导出数据
     */
    @GetMapping("/export")
    @Operation(summary = "导出设置", description = "导出用户的完整设置数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> exportSettings(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> exportData = userSettingsService.exportUserSettings(user);
        
        return ResponseEntity.ok(ApiResponse.success(exportData, "设置导出成功"));
    }

    /**
     * 导入用户设置
     * 从导出数据中导入用户设置
     * 
     * @param importData 导入数据
     * @param authentication 当前用户认证信息
     * @return 返回导入后的设置
     */
    @PostMapping("/import")
    @Operation(summary = "导入设置", description = "从导出数据中导入用户设置")
    public ResponseEntity<ApiResponse<UserSettings>> importSettings(
            @RequestBody Map<String, Object> importData,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        UserSettings importedSettings = userSettingsService.importUserSettings(user, importData);
        
        logger.info("用户{}导入了设置", user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(importedSettings, "设置导入成功"));
    }

    /**
     * 获取设置统计信息
     * 返回系统级别的设置使用统计（管理员功能）
     * 
     * @param authentication 当前用户认证信息
     * @return 返回统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取设置统计", description = "获取系统级别的用户设置统计信息")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSettingsStatistics(
            Authentication authentication) {
        
        Map<String, Object> statistics = userSettingsService.getSettingsStatistics();
        
        return ResponseEntity.ok(ApiResponse.success(statistics, "获取设置统计成功"));
    }

    /**
     * 检查功能是否启用
     * 检查用户是否启用了特定功能
     * 
     * @param feature 功能名称
     * @param authentication 当前用户认证信息
     * @return 返回功能启用状态
     */
    @GetMapping("/feature/{feature}")
    @Operation(summary = "检查功能状态", description = "检查用户是否启用了特定功能")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkFeatureEnabled(
            @PathVariable String feature,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        boolean enabled = userSettingsService.isFeatureEnabled(user, feature);
        
        Map<String, Object> data = new HashMap<>();
        data.put("feature", feature);
        data.put("enabled", enabled);
        data.put("userId", user.getId());
        
        return ResponseEntity.ok(ApiResponse.success(data, "功能状态检查完成"));
    }

    /**
     * 获取用户偏好摘要
     * 返回用户关键偏好设置的摘要信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回偏好摘要
     */
    @GetMapping("/preferences-summary")
    @Operation(summary = "获取偏好摘要", description = "获取用户关键偏好设置的摘要信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPreferencesSummary(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("preferredAIModel", userSettingsService.getPreferredAIModel(user));
        summary.put("preferredSpeechEngine", userSettingsService.getPreferredSpeechEngine(user));
        summary.put("notificationsEnabled", userSettingsService.isFeatureEnabled(user, "email_notifications"));
        summary.put("realtimeSpeechEnabled", userSettingsService.isFeatureEnabled(user, "realtime_speech"));
        summary.put("twoFactorAuthEnabled", userSettingsService.isFeatureEnabled(user, "two_factor_auth"));
        
        UserSettings settings = userSettingsService.getUserSettings(user);
        summary.put("theme", settings.getUiSettings().getTheme());
        summary.put("language", settings.getUiSettings().getLanguage());
        summary.put("privacyLevel", settings.getSummary().get("privacyLevel"));
        
        return ResponseEntity.ok(ApiResponse.success(summary, "获取偏好摘要成功"));
    }
}
