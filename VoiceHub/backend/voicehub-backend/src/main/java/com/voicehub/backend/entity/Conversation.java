package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Conversation Entity
 * Represents a conversation session with the AI companion
 */
@TableName("conversations")
public class Conversation extends BaseEntity {

    @TableField("title")
    private String title;

    @TableField(exist = false)
    private String summary;

    @TableField("type")
    private ConversationType type = ConversationType.GENERAL;

    @TableField("status")
    private ConversationStatus status = ConversationStatus.ACTIVE;

    @TableField(value = "user_id", fill = FieldFill.INSERT)
    private Long userId;

    // 移除关联关系，使用MyBatis-Plus时需要手动处理
    @TableField(exist = false)
    private User user;

    @TableField(exist = false)
    private List<ChatMessage> messages = new ArrayList<>();

    @TableField("message_count")
    private Integer messageCount = 0;

    @TableField("last_activity_at")
    private LocalDateTime lastActivityAt;

    @TableField(value = "emotion_analysis", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> emotionAnalysis;

    @TableField("mood_score")
    private Double moodScore;

    @TableField("is_favorite")
    private Boolean isFavorite = false;

    @TableField("is_archived")
    private Boolean isArchived = false;

    // 新增的getter/setter方法（只添加缺失的）
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }



    // Enums
    public enum ConversationType {
        GENERAL,
        ASSISTANT,
        EMOTIONAL_SUPPORT,
        SCHEDULE_HELP,
        NOTE_ASSISTANCE,
        BRAINSTORMING,
        PROBLEM_SOLVING,
        CASUAL_CHAT
    }

    public enum ConversationStatus {
        ACTIVE,
        PAUSED,
        COMPLETED,
        ARCHIVED
    }

    // Constructors
    public Conversation() {}

    public Conversation(String title, ConversationType type, User user) {
        this.title = title;
        this.type = type;
        this.user = user;
        this.lastActivityAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public ConversationType getType() {
        return type;
    }

    public void setType(ConversationType type) {
        this.type = type;
    }

    public ConversationStatus getStatus() {
        return status;
    }

    public void setStatus(ConversationStatus status) {
        this.status = status;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<ChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }

    public Integer getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(Integer messageCount) {
        this.messageCount = messageCount;
    }

    public LocalDateTime getLastActivityAt() {
        return lastActivityAt;
    }

    public void setLastActivityAt(LocalDateTime lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }

    public Map<String, Object> getEmotionAnalysis() {
        return emotionAnalysis;
    }

    public void setEmotionAnalysis(Map<String, Object> emotionAnalysis) {
        this.emotionAnalysis = emotionAnalysis;
    }

    public Double getMoodScore() {
        return moodScore;
    }

    public void setMoodScore(Double moodScore) {
        this.moodScore = moodScore;
    }

    public Boolean getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(Boolean isFavorite) {
        this.isFavorite = isFavorite;
    }

    public Boolean getIsArchived() {
        return isArchived;
    }

    public void setIsArchived(Boolean isArchived) {
        this.isArchived = isArchived;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Utility methods
    public void addMessage(ChatMessage message) {
        this.messages.add(message);
        message.setConversation(this);
        this.messageCount = this.messages.size();
        this.lastActivityAt = LocalDateTime.now();
    }

    public void updateMoodAnalysis(Map<String, Object> emotionAnalysis, Double moodScore) {
        this.emotionAnalysis = emotionAnalysis;
        this.moodScore = moodScore;
    }

    public boolean isActive() {
        return ConversationStatus.ACTIVE.equals(this.status);
    }

    public String getFormattedTitle() {
        return title != null ? title : "Conversation " + id;
    }

    @Override
    public String toString() {
        return "Conversation{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", messageCount=" + messageCount +
                ", createdAt=" + createdAt +
                '}';
    }
}