package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.ApiUsageLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API使用日志Mapper接口
 */
@Mapper
public interface ApiUsageLogMapper extends BaseMapper<ApiUsageLog> {

    /**
     * 根据用户ID查找API使用日志
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<ApiUsageLog> findByUserId(@Param("userId") Long userId);

    /**
     * 根据API提供商查找日志
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE api_provider = #{apiProvider}")
    List<ApiUsageLog> findByApiProvider(@Param("apiProvider") String apiProvider);

    /**
     * 根据请求类型查找日志
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE request_type = #{requestType}")
    List<ApiUsageLog> findByRequestType(@Param("requestType") String requestType);

    /**
     * 根据模型名称查找日志
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE model_name = #{modelName}")
    List<ApiUsageLog> findByModelName(@Param("modelName") String modelName);

    /**
     * 查找用户的特定提供商使用记录
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND api_provider = #{apiProvider} ORDER BY created_at DESC")
    List<ApiUsageLog> findByUserIdAndApiProvider(@Param("userId") Long userId, @Param("apiProvider") String apiProvider);

    /**
     * 查找用户的特定请求类型记录
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND request_type = #{requestType} ORDER BY created_at DESC")
    List<ApiUsageLog> findByUserIdAndRequestType(@Param("userId") Long userId, @Param("requestType") String requestType);

    /**
     * 根据时间范围查找日志
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE created_at BETWEEN #{startDate} AND #{endDate} ORDER BY created_at DESC")
    List<ApiUsageLog> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户在时间范围内的使用记录
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND created_at BETWEEN #{startDate} AND #{endDate} ORDER BY created_at DESC")
    List<ApiUsageLog> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户的API调用次数
     */
    @Select("SELECT COUNT(*) FROM voicehub.api_usage_logs WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的成功调用次数
     */
    @Select("SELECT COUNT(*) FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND status_code = 200")
    long countSuccessByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的失败调用次数
     */
    @Select("SELECT COUNT(*) FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND status_code != 200")
    long countErrorByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总token使用量
     */
    @Select("SELECT COALESCE(SUM(tokens_input + tokens_output), 0) FROM voicehub.api_usage_logs WHERE user_id = #{userId}")
    long sumTokensByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的总成本
     */
    @Select("SELECT COALESCE(SUM(cost_usd), 0) FROM voicehub.api_usage_logs WHERE user_id = #{userId}")
    BigDecimal sumCostByUserId(@Param("userId") Long userId);

    /**
     * 统计用户在时间范围内的成本
     */
    @Select("SELECT COALESCE(SUM(cost_usd), 0) FROM voicehub.api_usage_logs WHERE user_id = #{userId} AND created_at BETWEEN #{startDate} AND #{endDate}")
    BigDecimal sumCostByUserIdAndDateRange(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找错误的API调用
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE status_code != 200 ORDER BY created_at DESC")
    List<ApiUsageLog> findErrorLogs();

    /**
     * 查找响应时间超过阈值的调用
     */
    @Select("SELECT * FROM voicehub.api_usage_logs WHERE response_time_ms > #{threshold} ORDER BY response_time_ms DESC")
    List<ApiUsageLog> findSlowRequests(@Param("threshold") long threshold);
}
