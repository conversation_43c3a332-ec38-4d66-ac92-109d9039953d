package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统状态检查和服务监控接口
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/health")
@Tag(name = "系统健康检查", description = "系统状态监控和健康检查接口")
public class HealthController {

    /**
     * 系统健康检查
     * 返回系统运行状态和基本信息
     *
     * @return 返回系统健康状态
     */
    @GetMapping
    @Operation(summary = "系统健康检查", description = "检查系统运行状态和服务可用性")
    public ResponseEntity<ApiResponse<Map<String, Object>>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("service", "VoiceHub Backend");
        healthInfo.put("version", "1.0.0");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("uptime", getUptime());
        healthInfo.put("environment", "development");

        return ResponseEntity.ok(ApiResponse.success(healthInfo, "🎤 VoiceHub服务运行正常"));
    }

    /**
     * 获取系统运行时间（简化实现）
     */
    private String getUptime() {
        long uptimeMs = System.currentTimeMillis() - getStartTime();
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
    }

    /**
     * 获取启动时间（简化实现）
     */
    private long getStartTime() {
        // 这里应该记录实际的启动时间，这里用一个固定值作为示例
        return System.currentTimeMillis() - 3600000; // 假设运行了1小时
    }
}