package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.StatisticsService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 统计数据控制器
 * 提供用户和系统的统计数据查询功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/statistics")
@Tag(name = "统计数据", description = "用户和系统统计数据查询接口")
@PreAuthorize("hasRole('USER')")
public class StatisticsController {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private UserService userService;

    /**
     * 获取用户个人统计数据
     * 返回当前用户的详细使用统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回用户统计数据
     */
    @GetMapping("/user")
    @Operation(summary = "获取用户统计", description = "获取当前用户的详细使用统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserStatistics(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> statistics = statisticsService.getUserStatistics(user);
        
        return ResponseEntity.ok(ApiResponse.success(statistics, "获取用户统计数据成功"));
    }

    /**
     * 获取用户对话统计
     * 返回用户的对话相关统计数据
     * 
     * @param authentication 当前用户认证信息
     * @return 返回对话统计数据
     */
    @GetMapping("/user/conversations")
    @Operation(summary = "获取对话统计", description = "获取用户的对话使用统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserConversationStatistics(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> fullStats = statisticsService.getUserStatistics(user);
        Map<String, Object> conversationStats = (Map<String, Object>) fullStats.get("conversations");
        
        Map<String, Object> data = new HashMap<>();
        data.put("conversationStats", conversationStats);
        data.put("userId", user.getId());
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取对话统计数据成功"));
    }

    /**
     * 获取用户语音笔记统计
     * 返回用户的语音笔记相关统计数据
     * 
     * @param authentication 当前用户认证信息
     * @return 返回语音笔记统计数据
     */
    @GetMapping("/user/voice-notes")
    @Operation(summary = "获取语音笔记统计", description = "获取用户的语音笔记使用统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserVoiceNoteStatistics(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> fullStats = statisticsService.getUserStatistics(user);
        Map<String, Object> voiceNoteStats = (Map<String, Object>) fullStats.get("voiceNotes");
        
        Map<String, Object> data = new HashMap<>();
        data.put("voiceNoteStats", voiceNoteStats);
        data.put("userId", user.getId());
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取语音笔记统计数据成功"));
    }

    /**
     * 获取用户活跃度统计
     * 返回用户的活跃度和使用习惯统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回活跃度统计数据
     */
    @GetMapping("/user/activity")
    @Operation(summary = "获取活跃度统计", description = "获取用户的活跃度和使用习惯统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserActivityStatistics(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> fullStats = statisticsService.getUserStatistics(user);
        Map<String, Object> activityStats = (Map<String, Object>) fullStats.get("activity");
        
        Map<String, Object> data = new HashMap<>();
        data.put("activityStats", activityStats);
        data.put("userId", user.getId());
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取活跃度统计数据成功"));
    }

    /**
     * 获取用户偏好统计
     * 返回用户的使用偏好和设置统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回偏好统计数据
     */
    @GetMapping("/user/preferences")
    @Operation(summary = "获取偏好统计", description = "获取用户的使用偏好和设置统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserPreferenceStatistics(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> fullStats = statisticsService.getUserStatistics(user);
        Map<String, Object> preferenceStats = (Map<String, Object>) fullStats.get("preferences");
        
        Map<String, Object> data = new HashMap<>();
        data.put("preferenceStats", preferenceStats);
        data.put("userId", user.getId());
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取偏好统计数据成功"));
    }

    /**
     * 获取系统总体统计数据（管理员功能）
     * 返回系统级别的统计信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回系统统计数据
     */
    @GetMapping("/system")
    @Operation(summary = "获取系统统计", description = "获取系统级别的统计数据（管理员功能）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStatistics(
            Authentication authentication) {
        
        Map<String, Object> statistics = statisticsService.getSystemStatistics();
        
        logger.info("管理员{}查看了系统统计数据", authentication.getName());
        
        return ResponseEntity.ok(ApiResponse.success(statistics, "获取系统统计数据成功"));
    }

    /**
     * 获取系统用户统计（管理员功能）
     * 返回用户相关的系统统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回用户统计数据
     */
    @GetMapping("/system/users")
    @Operation(summary = "获取系统用户统计", description = "获取系统用户相关统计数据（管理员功能）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemUserStatistics(
            Authentication authentication) {
        
        Map<String, Object> fullStats = statisticsService.getSystemStatistics();
        Map<String, Object> userStats = (Map<String, Object>) fullStats.get("users");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userStats", userStats);
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取系统用户统计数据成功"));
    }

    /**
     * 获取系统AI使用统计（管理员功能）
     * 返回AI服务使用相关统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回AI使用统计数据
     */
    @GetMapping("/system/ai-usage")
    @Operation(summary = "获取AI使用统计", description = "获取系统AI服务使用统计数据（管理员功能）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemAIUsageStatistics(
            Authentication authentication) {
        
        Map<String, Object> fullStats = statisticsService.getSystemStatistics();
        Map<String, Object> aiUsageStats = (Map<String, Object>) fullStats.get("aiUsage");
        
        Map<String, Object> data = new HashMap<>();
        data.put("aiUsageStats", aiUsageStats);
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取AI使用统计数据成功"));
    }

    /**
     * 获取系统存储统计（管理员功能）
     * 返回存储使用相关统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回存储统计数据
     */
    @GetMapping("/system/storage")
    @Operation(summary = "获取存储统计", description = "获取系统存储使用统计数据（管理员功能）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStorageStatistics(
            Authentication authentication) {
        
        Map<String, Object> fullStats = statisticsService.getSystemStatistics();
        Map<String, Object> storageStats = (Map<String, Object>) fullStats.get("storage");
        
        Map<String, Object> data = new HashMap<>();
        data.put("storageStats", storageStats);
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取存储统计数据成功"));
    }

    /**
     * 获取系统性能统计（管理员功能）
     * 返回系统性能相关统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回性能统计数据
     */
    @GetMapping("/system/performance")
    @Operation(summary = "获取性能统计", description = "获取系统性能统计数据（管理员功能）")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemPerformanceStatistics(
            Authentication authentication) {
        
        Map<String, Object> fullStats = statisticsService.getSystemStatistics();
        Map<String, Object> performanceStats = (Map<String, Object>) fullStats.get("performance");
        
        Map<String, Object> data = new HashMap<>();
        data.put("performanceStats", performanceStats);
        data.put("lastUpdated", fullStats.get("lastUpdated"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取性能统计数据成功"));
    }

    /**
     * 获取指定日期范围的统计数据
     * 返回指定时间段内的统计信息
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param authentication 当前用户认证信息
     * @return 返回日期范围统计数据
     */
    @GetMapping("/date-range")
    @Operation(summary = "获取日期范围统计", description = "获取指定日期范围内的统计数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatisticsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        // 验证日期范围
        if (startDate.isAfter(endDate)) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("开始日期不能晚于结束日期"));
        }
        
        if (startDate.isBefore(LocalDate.now().minusYears(1))) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("查询范围不能超过一年"));
        }
        
        Map<String, Object> statistics = statisticsService.getStatisticsByDateRange(startDate, endDate);
        
        return ResponseEntity.ok(ApiResponse.success(statistics, "获取日期范围统计数据成功"));
    }

    /**
     * 获取统计数据摘要
     * 返回用户的关键统计指标摘要
     * 
     * @param authentication 当前用户认证信息
     * @return 返回统计摘要
     */
    @GetMapping("/summary")
    @Operation(summary = "获取统计摘要", description = "获取用户的关键统计指标摘要")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStatisticsSummary(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> fullStats = statisticsService.getUserStatistics(user);
        
        // 提取关键指标
        Map<String, Object> summary = new HashMap<>();
        
        // 从各个统计中提取关键数据
        Map<String, Object> conversations = (Map<String, Object>) fullStats.get("conversations");
        Map<String, Object> voiceNotes = (Map<String, Object>) fullStats.get("voiceNotes");
        Map<String, Object> activity = (Map<String, Object>) fullStats.get("activity");
        
        summary.put("totalConversations", conversations.get("totalConversations"));
        summary.put("totalVoiceNotes", voiceNotes.get("totalNotes"));
        summary.put("totalActiveDays", activity.get("totalActiveDays"));
        summary.put("activityScore", activity.get("activityScore"));
        summary.put("memberSince", fullStats.get("memberSince"));
        summary.put("lastActiveAt", activity.get("lastActiveAt"));
        
        return ResponseEntity.ok(ApiResponse.success(summary, "获取统计摘要成功"));
    }
}
