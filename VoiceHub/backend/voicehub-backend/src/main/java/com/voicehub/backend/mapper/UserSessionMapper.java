package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.UserSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会话Mapper接口
 */
@Mapper
public interface UserSessionMapper extends BaseMapper<UserSession> {

    /**
     * 根据用户ID查找会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE user_id = #{userId} ORDER BY start_time DESC")
    List<UserSession> findByUserId(@Param("userId") Long userId);

    /**
     * 根据会话ID查找会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE session_id = #{sessionId}")
    UserSession findBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据会话类型查找会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE session_type = #{sessionType}")
    List<UserSession> findBySessionType(@Param("sessionType") String sessionType);

    /**
     * 查找用户的特定类型会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE user_id = #{userId} AND session_type = #{sessionType} ORDER BY start_time DESC")
    List<UserSession> findByUserIdAndSessionType(@Param("userId") Long userId, @Param("sessionType") String sessionType);

    /**
     * 查找活跃会话（未结束的会话）
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE end_time IS NULL")
    List<UserSession> findActiveSessions();

    /**
     * 查找用户的活跃会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE user_id = #{userId} AND end_time IS NULL")
    List<UserSession> findActiveSessionsByUserId(@Param("userId") Long userId);

    /**
     * 根据时间范围查找会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE start_time BETWEEN #{startDate} AND #{endDate} ORDER BY start_time DESC")
    List<UserSession> findByStartTimeBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户在时间范围内的会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE user_id = #{userId} AND start_time BETWEEN #{startDate} AND #{endDate} ORDER BY start_time DESC")
    List<UserSession> findByUserIdAndStartTimeBetween(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 根据IP地址查找会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE ip_address = #{ipAddress}")
    List<UserSession> findByIpAddress(@Param("ipAddress") String ipAddress);

    /**
     * 查找用户最近的会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE user_id = #{userId} ORDER BY start_time DESC LIMIT #{limit}")
    List<UserSession> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 统计用户的会话总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.user_sessions WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户特定类型的会话数量
     */
    @Select("SELECT COUNT(*) FROM voicehub.user_sessions WHERE user_id = #{userId} AND session_type = #{sessionType}")
    long countByUserIdAndSessionType(@Param("userId") Long userId, @Param("sessionType") String sessionType);

    /**
     * 统计用户的总会话时长
     */
    @Select("SELECT COALESCE(SUM(duration_seconds), 0) FROM voicehub.user_sessions WHERE user_id = #{userId} AND duration_seconds IS NOT NULL")
    long sumDurationByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的平均会话时长
     */
    @Select("SELECT COALESCE(AVG(duration_seconds), 0) FROM voicehub.user_sessions WHERE user_id = #{userId} AND duration_seconds IS NOT NULL")
    double avgDurationByUserId(@Param("userId") Long userId);

    /**
     * 更新会话结束时间和时长
     */
    @Update("UPDATE voicehub.user_sessions SET end_time = #{endTime}, duration_seconds = #{durationSeconds}, updated_at = NOW() WHERE session_id = #{sessionId}")
    void updateEndTime(@Param("sessionId") String sessionId, @Param("endTime") LocalDateTime endTime, @Param("durationSeconds") Integer durationSeconds);

    /**
     * 增加会话活动次数
     */
    @Update("UPDATE voicehub.user_sessions SET activities_count = activities_count + 1, updated_at = NOW() WHERE session_id = #{sessionId}")
    void incrementActivitiesCount(@Param("sessionId") String sessionId);

    /**
     * 查找长时间未活动的会话
     */
    @Select("SELECT * FROM voicehub.user_sessions WHERE end_time IS NULL AND updated_at < #{threshold}")
    List<UserSession> findInactiveSessions(@Param("threshold") LocalDateTime threshold);

    /**
     * 查找今日活跃用户
     */
    @Select("SELECT DISTINCT user_id FROM voicehub.user_sessions WHERE DATE(start_time) = CURRENT_DATE")
    List<Long> findTodayActiveUserIds();
}
