package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.ChatMessage;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.ConversationService;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.util.PageConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 对话管理控制器
 * 处理AI对话会话的创建、消息发送、历史查询等功能
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/conversations")
@Tag(name = "对话管理", description = "AI对话会话和消息管理相关接口")
@PreAuthorize("hasRole('USER')")
public class ConversationController {

    private static final Logger logger = LoggerFactory.getLogger(ConversationController.class);

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private UserService userService;

    /**
     * 创建新对话
     * 创建一个新的AI对话会话，可以指定对话标题和类型
     *
     * @param request 创建对话请求，包含标题和类型信息
     * @param authentication 当前用户认证信息
     * @return 返回创建的对话信息
     */
    @PostMapping
    @Operation(summary = "创建新对话", description = "创建一个新的AI对话会话")
    public ResponseEntity<ApiResponse<Conversation>> createConversation(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        String title = (String) request.get("title");
        String typeStr = (String) request.getOrDefault("type", "GENERAL");

        if (title == null || title.trim().isEmpty()) {
            throw BusinessException.validationError("对话标题不能为空");
        }

        Conversation.ConversationType type;
        try {
            type = Conversation.ConversationType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw BusinessException.validationError("无效的对话类型: " + typeStr);
        }

        Conversation conversation = conversationService.createConversation(user, title, type);

        logger.info("成功创建对话: {} for user: {}", conversation.getId(), user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(conversation, "对话创建成功"));
    }

    /**
     * 发送消息到对话
     * 向指定对话发送消息并获取AI回复，支持文本和语音消息
     *
     * @param id 对话ID
     * @param request 消息请求，包含消息内容、是否为语音消息等信息
     * @param authentication 当前用户认证信息
     * @return 返回AI回复的消息信息
     */
    @PostMapping("/{id}/messages")
    @Operation(summary = "发送消息", description = "向指定对话发送消息并获取AI回复")
    public ResponseEntity<ApiResponse<ChatMessage>> sendMessage(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        String content = (String) request.get("content");
        Boolean isVoice = (Boolean) request.getOrDefault("isVoice", false);
        String voiceFilePath = (String) request.get("voiceFilePath");

        if (content == null || content.trim().isEmpty()) {
            throw BusinessException.validationError("消息内容不能为空");
        }

        ChatMessage response;
        if (Boolean.TRUE.equals(isVoice) && voiceFilePath != null) {
            response = conversationService.sendVoiceMessage(id, content, voiceFilePath, user);
        } else {
            response = conversationService.sendMessage(id, content, user);
        }

        logger.info("成功发送消息到对话: {} for user: {}", id, user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(response, "消息发送成功"));
    }

    /**
     * 获取用户的所有对话
     * 分页查询当前用户的对话列表，支持按时间排序
     *
     * @param page 页码，从0开始，默认为0
     * @param size 每页大小，默认为20
     * @param authentication 当前用户认证信息
     * @return 返回分页的对话列表
     */
    @GetMapping
    @Operation(summary = "获取对话列表", description = "分页查询当前用户的对话列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        Pageable pageable = PageRequest.of(page, size);
        com.baomidou.mybatisplus.core.metadata.IPage<Conversation> iPageConversations = conversationService.getUserConversations(user, pageable);
        Page<Conversation> conversations = PageConverter.convertToSpringPage(iPageConversations);

        Map<String, Object> data = new HashMap<>();
        data.put("conversations", conversations.getContent());
        data.put("totalElements", conversations.getTotalElements());
        data.put("totalPages", conversations.getTotalPages());
        data.put("currentPage", page);
        data.put("pageSize", size);

        return ResponseEntity.ok(ApiResponse.success(data, "获取对话列表成功"));
    }

    /**
     * 根据ID获取对话详情
     * 获取指定对话的详细信息，包括对话设置和统计数据
     *
     * @param id 对话ID
     * @param authentication 当前用户认证信息
     * @return 返回对话详细信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取对话详情", description = "根据ID获取指定对话的详细信息")
    public ResponseEntity<ApiResponse<Conversation>> getConversation(
            @PathVariable Long id,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        Conversation conversation = conversationService.getConversation(id, user);
        if (conversation == null) {
            throw ResourceNotFoundException.conversation(id);
        }

        return ResponseEntity.ok(ApiResponse.success(conversation, "获取对话详情成功"));
    }

    /**
     * 获取对话消息历史
     * 分页查询指定对话中的消息历史记录，按时间顺序排列
     *
     * @param id 对话ID
     * @param page 页码，从0开始，默认为0
     * @param size 每页大小，默认为50
     * @param authentication 当前用户认证信息
     * @return 返回分页的消息列表
     */
    @GetMapping("/{id}/messages")
    @Operation(summary = "获取消息历史", description = "分页查询指定对话中的消息历史记录")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMessages(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        // 验证对话是否存在且属于当前用户
        Conversation conversation = conversationService.getConversation(id, user);
        if (conversation == null) {
            throw ResourceNotFoundException.conversation(id);
        }

        if (page == 0 && size == 50) {
            // 返回所有消息（默认请求）
            List<ChatMessage> messages = conversationService.getConversationMessages(id, user);
            Map<String, Object> data = new HashMap<>();
            data.put("messages", messages);
            data.put("totalElements", messages.size());
            data.put("totalPages", 1);
            data.put("currentPage", 0);
            data.put("pageSize", messages.size());

            return ResponseEntity.ok(ApiResponse.success(data, "获取消息历史成功"));
        } else {
            // 返回分页消息
            Pageable pageable = PageRequest.of(page, size);
            com.baomidou.mybatisplus.core.metadata.IPage<ChatMessage> iPageMessages = conversationService.getConversationMessages(id, user, pageable);
            Page<ChatMessage> messages = PageConverter.convertToSpringPage(iPageMessages);

            Map<String, Object> data = new HashMap<>();
            data.put("messages", messages.getContent());
            data.put("totalElements", messages.getTotalElements());
            data.put("totalPages", messages.getTotalPages());
            data.put("currentPage", page);
            data.put("pageSize", size);

            return ResponseEntity.ok(ApiResponse.success(data, "获取消息历史成功"));
        }
    }

    /**
     * 更新对话
     *
     * @param id 对话ID
     * @param request 更新请求，包含标题和类型信息
     * @param authentication 当前用户认证信息
     * @return 返回更新后的对话信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新对话", description = "更新对话详情")
    public ResponseEntity<?> updateConversation(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String typeStr = (String) request.get("type");
            Conversation.ConversationType type = typeStr != null ? Conversation.ConversationType.valueOf(typeStr.toUpperCase()) : null;

            Conversation updatedConversation = conversationService.updateConversation(id, title, type, user);

            logger.info("Successfully updated conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Conversation updated successfully", updatedConversation));

        } catch (Exception e) {
            logger.error("Failed to update conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to update conversation: " + e.getMessage()));
        }
    }

    /**
     * 删除对话
     *
     * @param id 对话ID
     * @param authentication 当前用户认证信息
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除对话", description = "根据ID删除对话")
    public ResponseEntity<?> deleteConversation(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            conversationService.deleteConversation(id, user);

            logger.info("Successfully deleted conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Conversation deleted successfully", null));

        } catch (Exception e) {
            logger.error("Failed to delete conversation: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to delete conversation: " + e.getMessage()));
        }
    }

    /**
     * 搜索对话
     *
     * @param keyword 搜索关键词
     * @param authentication 当前用户认证信息
     * @return 返回匹配的对话列表
     */
    @GetMapping("/search")
    @Operation(summary = "搜索对话", description = "根据关键词搜索对话")
    public ResponseEntity<?> searchConversations(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.searchConversations(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("Search completed successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to search conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to search conversations: " + e.getMessage()));
        }
    }

    /**
     * 根据类型获取对话
     *
     * @param type 对话类型
     * @param authentication 当前用户认证信息
     * @return 返回指定类型的对话列表
     */
    @GetMapping("/type/{type}")
    @Operation(summary = "根据类型获取对话", description = "根据类型筛选对话")
    public ResponseEntity<?> getConversationsByType(
            @PathVariable Conversation.ConversationType type,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getConversationsByType(user, type);

            return ResponseEntity.ok(createSuccessResponse("Conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get conversations by type: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get conversations by type: " + e.getMessage()));
        }
    }

    /**
     * 获取收藏对话
     *
     * @param authentication 当前用户认证信息
     * @return 返回收藏的对话列表
     */
    @GetMapping("/favorites")
    @Operation(summary = "获取收藏对话", description = "获取用户收藏的对话")
    public ResponseEntity<?> getFavoriteConversations(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getFavoriteConversations(user);

            return ResponseEntity.ok(createSuccessResponse("Favorite conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get favorite conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get favorite conversations: " + e.getMessage()));
        }
    }

    /**
     * 获取归档对话
     *
     * @param authentication 当前用户认证信息
     * @return 返回归档的对话列表
     */
    @GetMapping("/archived")
    @Operation(summary = "获取归档对话", description = "获取用户归档的对话")
    public ResponseEntity<?> getArchivedConversations(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getArchivedConversations(user);

            return ResponseEntity.ok(createSuccessResponse("Archived conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get archived conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get archived conversations: " + e.getMessage()));
        }
    }

    /**
     * 获取最近对话
     *
     * @param days 天数
     * @param authentication 当前用户认证信息
     * @return 返回最近的对话列表
     */
    @GetMapping("/recent")
    @Operation(summary = "获取最近对话", description = "获取最近的对话")
    public ResponseEntity<?> getRecentConversations(
            @RequestParam(defaultValue = "7") int days,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<Conversation> conversations = conversationService.getRecentConversations(user, days);

            return ResponseEntity.ok(createSuccessResponse("Recent conversations retrieved successfully", conversations));

        } catch (Exception e) {
            logger.error("Failed to get recent conversations: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get recent conversations: " + e.getMessage()));
        }
    }

    /**
     * 切换收藏状态
     *
     * @param id 对话ID
     * @param authentication 当前用户认证信息
     * @return 返回更新后的对话信息
     */
    @PutMapping("/{id}/favorite")
    @Operation(summary = "切换收藏状态", description = "切换对话的收藏状态")
    public ResponseEntity<?> toggleFavorite(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Conversation conversation = conversationService.toggleFavorite(id, user);

            logger.info("Successfully toggled favorite for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Favorite status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to toggle favorite: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle favorite: " + e.getMessage()));
        }
    }

    /**
     * 切换归档状态
     *
     * @param id 对话ID
     * @param authentication 当前用户认证信息
     * @return 返回更新后的对话信息
     */
    @PutMapping("/{id}/archive")
    @Operation(summary = "切换归档状态", description = "切换对话的归档状态")
    public ResponseEntity<?> toggleArchive(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Conversation conversation = conversationService.toggleArchive(id, user);

            logger.info("Successfully toggled archive for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Archive status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to toggle archive: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle archive: " + e.getMessage()));
        }
    }

    /**
     * 更新对话状态
     *
     * @param id 对话ID
     * @param request 状态更新请求
     * @param authentication 当前用户认证信息
     * @return 返回更新后的对话信息
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新状态", description = "更新对话状态")
    public ResponseEntity<?> updateStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            String statusStr = (String) request.get("status");
            Conversation.ConversationStatus status = Conversation.ConversationStatus.valueOf(statusStr.toUpperCase());

            Conversation conversation = conversationService.updateConversationStatus(id, status, user);

            logger.info("Successfully updated status for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Status updated successfully", conversation));

        } catch (Exception e) {
            logger.error("Failed to update status: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to update status: " + e.getMessage()));
        }
    }

    /**
     * 生成对话摘要
     *
     * @param id 对话ID
     * @param authentication 当前用户认证信息
     * @return 返回生成的摘要
     */
    @PostMapping("/{id}/summary")
    @Operation(summary = "生成摘要", description = "为对话生成AI摘要")
    public ResponseEntity<?> generateSummary(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            String summary = conversationService.generateSummary(id, user);

            logger.info("Successfully generated summary for conversation: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Summary generated successfully", summary));

        } catch (Exception e) {
            logger.error("Failed to generate summary: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to generate summary: " + e.getMessage()));
        }
    }

    /**
     * 获取对话统计信息
     *
     * @param authentication 当前用户认证信息
     * @return 返回用户的对话统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取统计信息", description = "获取用户的对话统计信息")
    public ResponseEntity<?> getConversationStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            ConversationService.ConversationStats stats = conversationService.getUserConversationStats(user);

            return ResponseEntity.ok(createSuccessResponse("Statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get conversation stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get statistics: " + e.getMessage()));
        }
    }

    /**
     * 搜索消息
     *
     * @param keyword 搜索关键词
     * @param authentication 当前用户认证信息
     * @return 返回匹配的消息列表
     */
    @GetMapping("/messages/search")
    @Operation(summary = "搜索消息", description = "在所有对话中搜索消息")
    public ResponseEntity<?> searchMessages(
            @RequestParam String keyword,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<ChatMessage> messages = conversationService.searchMessages(user, keyword);

            return ResponseEntity.ok(createSuccessResponse("Message search completed successfully", messages));

        } catch (Exception e) {
            logger.error("Failed to search messages: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to search messages: " + e.getMessage()));
        }
    }

    /**
     * 根据情感获取消息
     *
     * @param emotion 情感类型
     * @param authentication 当前用户认证信息
     * @return 返回指定情感类型的消息列表
     */
    @GetMapping("/messages/emotion/{emotion}")
    @Operation(summary = "根据情感获取消息", description = "根据情感类型筛选消息")
    public ResponseEntity<?> getMessagesByEmotion(
            @PathVariable String emotion,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<ChatMessage> messages = conversationService.getMessagesByEmotion(user, emotion);

            return ResponseEntity.ok(createSuccessResponse("Messages retrieved successfully", messages));

        } catch (Exception e) {
            logger.error("Failed to get messages by emotion: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get messages by emotion: " + e.getMessage()));
        }
    }

    /**
     * 获取情感统计
     *
     * @param authentication 当前用户认证信息
     * @return 返回用户的情感统计信息
     */
    @GetMapping("/emotions/stats")
    @Operation(summary = "获取情感统计", description = "获取用户的情感统计信息")
    public ResponseEntity<?> getEmotionStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Map<String, Long> stats = conversationService.getEmotionStatistics(user);

            return ResponseEntity.ok(createSuccessResponse("Emotion statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get emotion stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get emotion statistics: " + e.getMessage()));
        }
    }

    /**
     * Create success response
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}