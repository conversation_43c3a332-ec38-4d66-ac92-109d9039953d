package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.VoiceNote;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.service.VoiceNoteService;
import com.voicehub.backend.util.PageConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 语音笔记控制器
 * 处理语音笔记的创建、查询、更新、删除等功能
 * 支持音频文件上传、转写、AI处理等完整流程
 *
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/voice-notes")
@Tag(name = "语音笔记管理", description = "语音笔记的增删改查和高级功能接口")
@PreAuthorize("hasRole('USER')")
public class VoiceNoteController {

    private static final Logger logger = LoggerFactory.getLogger(VoiceNoteController.class);

    @Autowired
    private VoiceNoteService voiceNoteService;

    @Autowired
    private UserService userService;

    /**
     * 创建语音笔记
     * 上传音频文件并创建语音笔记，支持自动语音转文字和AI处理
     *
     * @param audioFile 音频文件，支持wav、mp3、m4a等格式
     * @param title 笔记标题，可选
     * @param description 笔记描述，可选
     * @param category 笔记分类，默认为GENERAL
     * @param priority 优先级，默认为MEDIUM
     * @param tags 标签数组，可选
     * @param authentication 当前用户认证信息
     * @return 返回创建的语音笔记信息
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "创建语音笔记", description = "上传音频文件并创建语音笔记，支持自动转写和AI处理")
    public ResponseEntity<ApiResponse<VoiceNote>> createVoiceNote(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "category", defaultValue = "GENERAL") VoiceNote.Category category,
            @RequestParam(value = "priority", defaultValue = "MEDIUM") VoiceNote.Priority priority,
            @RequestParam(value = "tags", required = false) String[] tags,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        // 验证音频文件
        if (audioFile.isEmpty()) {
            throw BusinessException.validationError("音频文件不能为空");
        }

        Set<String> tagSet = new HashSet<>();
        if (tags != null) {
            tagSet.addAll(Arrays.asList(tags));
        }

        VoiceNote voiceNote = voiceNoteService.createVoiceNote(
            title, description, audioFile, category, priority, tagSet, user);

        logger.info("成功创建语音笔记: {} for user: {}", voiceNote.getId(), user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(voiceNote, "语音笔记创建成功"));
    }

    /**
     * Create voice note from audio data (for WebRTC integration)
     */
    @PostMapping("/from-audio")
    @Operation(summary = "Create Voice Note from Audio Data", description = "Create voice note from raw audio data")
    public ResponseEntity<?> createVoiceNoteFromAudio(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            
            String title = (String) request.get("title");
            String audioDataBase64 = (String) request.get("audioData");
            String audioFormat = (String) request.getOrDefault("audioFormat", "wav");
            String categoryStr = (String) request.getOrDefault("category", "GENERAL");
            
            VoiceNote.Category category = VoiceNote.Category.valueOf(categoryStr.toUpperCase());
            
            // Decode base64 audio data
            byte[] audioData = Base64.getDecoder().decode(audioDataBase64);
            
            VoiceNote voiceNote = voiceNoteService.createVoiceNoteFromAudio(title, audioData, audioFormat, category, user);

            logger.info("Successfully created voice note from audio data: {} for user: {}", voiceNote.getId(), user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Voice note created successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to create voice note from audio data: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to create voice note: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的语音笔记列表
     * 分页查询当前用户的语音笔记，支持按时间排序
     *
     * @param page 页码，从0开始，默认为0
     * @param size 每页大小，默认为20
     * @param authentication 当前用户认证信息
     * @return 返回分页的语音笔记列表
     */
    @GetMapping
    @Operation(summary = "获取语音笔记列表", description = "分页查询当前用户的语音笔记列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getVoiceNotes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        Pageable pageable = PageRequest.of(page, size);
        com.baomidou.mybatisplus.core.metadata.IPage<VoiceNote> iPageVoiceNotes = voiceNoteService.getUserVoiceNotes(user, pageable);
        Page<VoiceNote> voiceNotes = PageConverter.convertToSpringPage(iPageVoiceNotes);

        Map<String, Object> data = new HashMap<>();
        data.put("voiceNotes", voiceNotes.getContent());
        data.put("totalElements", voiceNotes.getTotalElements());
        data.put("totalPages", voiceNotes.getTotalPages());
        data.put("currentPage", page);
        data.put("pageSize", size);

        return ResponseEntity.ok(ApiResponse.success(data, "获取语音笔记列表成功"));
    }

    /**
     * 根据ID获取语音笔记详情
     * 获取指定语音笔记的详细信息，包括转写文本、AI分析结果等
     *
     * @param id 语音笔记ID
     * @param authentication 当前用户认证信息
     * @return 返回语音笔记详细信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取语音笔记详情", description = "根据ID获取指定语音笔记的详细信息")
    public ResponseEntity<ApiResponse<VoiceNote>> getVoiceNote(
            @PathVariable Long id,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        VoiceNote voiceNote = voiceNoteService.getVoiceNote(id, user);
        if (voiceNote == null) {
            throw ResourceNotFoundException.voiceNote(id);
        }

        return ResponseEntity.ok(ApiResponse.success(voiceNote, "获取语音笔记详情成功"));
    }

    /**
     * 更新语音笔记
     * 更新语音笔记的标题、描述、分类、优先级、标签等信息
     *
     * @param id 语音笔记ID
     * @param request 更新请求，包含要更新的字段
     * @param authentication 当前用户认证信息
     * @return 返回更新后的语音笔记信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新语音笔记", description = "更新语音笔记的标题、描述、分类等信息")
    public ResponseEntity<ApiResponse<VoiceNote>> updateVoiceNote(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        String title = (String) request.get("title");
        String description = (String) request.get("description");
        String categoryStr = (String) request.get("category");
        String priorityStr = (String) request.get("priority");

        VoiceNote.Category category = null;
        VoiceNote.Priority priority = null;

        try {
            if (categoryStr != null) {
                category = VoiceNote.Category.valueOf(categoryStr.toUpperCase());
            }
            if (priorityStr != null) {
                priority = VoiceNote.Priority.valueOf(priorityStr.toUpperCase());
            }
        } catch (IllegalArgumentException e) {
            throw BusinessException.validationError("无效的分类或优先级参数");
        }

        @SuppressWarnings("unchecked")
        List<String> tagsList = (List<String>) request.get("tags");
        Set<String> tags = tagsList != null ? new HashSet<>(tagsList) : null;

        VoiceNote updatedVoiceNote = voiceNoteService.updateVoiceNote(id, user, title, description, category, priority, tags);

        logger.info("成功更新语音笔记: {} for user: {}", id, user.getUsername());
        return ResponseEntity.ok(ApiResponse.success(updatedVoiceNote, "语音笔记更新成功"));
    }

    /**
     * 删除语音笔记
     * 删除指定的语音笔记及其关联的音频文件
     *
     * @param id 语音笔记ID
     * @param authentication 当前用户认证信息
     * @return 返回删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除语音笔记", description = "删除指定的语音笔记及其关联文件")
    public ResponseEntity<ApiResponse<Object>> deleteVoiceNote(
            @PathVariable Long id,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        voiceNoteService.deleteVoiceNote(id, user);

        logger.info("成功删除语音笔记: {} for user: {}", id, user.getUsername());
        return ResponseEntity.ok(ApiResponse.success("语音笔记删除成功"));
    }

    /**
     * 收藏/取消收藏语音笔记
     * 切换指定语音笔记的收藏状态
     *
     * @param id 语音笔记ID
     * @param authentication 当前用户认证信息
     * @return 返回收藏状态更新结果
     */
    @PostMapping("/{id}/favorite")
    @Operation(summary = "收藏/取消收藏", description = "切换语音笔记的收藏状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> toggleFavorite(
            @PathVariable Long id,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        boolean isFavorited = voiceNoteService.toggleFavorite(id, user);

        Map<String, Object> data = new HashMap<>();
        data.put("isFavorited", isFavorited);
        data.put("voiceNoteId", id);

        String message = isFavorited ? "已添加到收藏" : "已取消收藏";
        logger.info("用户 {} {} 语音笔记: {}", user.getUsername(), message, id);

        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 搜索语音笔记
     * 根据关键词搜索语音笔记的标题、内容、标签等信息
     *
     * @param keyword 搜索关键词
     * @param authentication 当前用户认证信息
     * @return 返回匹配的语音笔记列表
     */
    @GetMapping("/search")
    @Operation(summary = "搜索语音笔记", description = "根据关键词搜索语音笔记")
    public ResponseEntity<ApiResponse<List<VoiceNote>>> searchVoiceNotes(
            @RequestParam String keyword,
            Authentication authentication) {

        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));

        if (keyword == null || keyword.trim().isEmpty()) {
            throw BusinessException.validationError("搜索关键词不能为空");
        }

        List<VoiceNote> voiceNotes = voiceNoteService.searchVoiceNotes(user, keyword.trim());

        return ResponseEntity.ok(ApiResponse.success(voiceNotes, "搜索完成，找到 " + voiceNotes.size() + " 条结果"));
    }

    /**
     * Get voice notes by category
     */
    @GetMapping("/category/{category}")
    @Operation(summary = "Get Voice Notes by Category", description = "Get voice notes filtered by category")
    public ResponseEntity<?> getVoiceNotesByCategory(
            @PathVariable VoiceNote.Category category,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getVoiceNotesByCategory(user, category);

            return ResponseEntity.ok(createSuccessResponse("Voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get voice notes by category: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice notes by category: " + e.getMessage()));
        }
    }

    /**
     * Get favorite voice notes
     */
    @GetMapping("/favorites")
    @Operation(summary = "Get Favorite Voice Notes", description = "Get user's favorite voice notes")
    public ResponseEntity<?> getFavoriteVoiceNotes(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getFavoriteVoiceNotes(user);

            return ResponseEntity.ok(createSuccessResponse("Favorite voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get favorite voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get favorite voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get archived voice notes
     */
    @GetMapping("/archived")
    @Operation(summary = "Get Archived Voice Notes", description = "Get user's archived voice notes")
    public ResponseEntity<?> getArchivedVoiceNotes(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getArchivedVoiceNotes(user);

            return ResponseEntity.ok(createSuccessResponse("Archived voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get archived voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get archived voice notes: " + e.getMessage()));
        }
    }

    /**
     * Get voice notes by tag
     */
    @GetMapping("/tag/{tag}")
    @Operation(summary = "Get Voice Notes by Tag", description = "Get voice notes filtered by tag")
    public ResponseEntity<?> getVoiceNotesByTag(
            @PathVariable String tag,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getVoiceNotesByTag(user, tag);

            return ResponseEntity.ok(createSuccessResponse("Voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get voice notes by tag: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get voice notes by tag: " + e.getMessage()));
        }
    }

    /**
     * Get recent voice notes
     */
    @GetMapping("/recent")
    @Operation(summary = "Get Recent Voice Notes", description = "Get recent voice notes")
    public ResponseEntity<?> getRecentVoiceNotes(
            @RequestParam(defaultValue = "7") int days,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            List<VoiceNote> voiceNotes = voiceNoteService.getRecentVoiceNotes(user, days);

            return ResponseEntity.ok(createSuccessResponse("Recent voice notes retrieved successfully", voiceNotes));

        } catch (Exception e) {
            logger.error("Failed to get recent voice notes: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get recent voice notes: " + e.getMessage()));
        }
    }



    /**
     * Toggle archive status
     */
    @PutMapping("/{id}/archive")
    @Operation(summary = "Toggle Archive", description = "Toggle archive status of voice note")
    public ResponseEntity<?> toggleArchive(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.toggleArchive(id, user);

            logger.info("Successfully toggled archive for voice note: {} for user: {}", id, user.getUsername());
            return ResponseEntity.ok(createSuccessResponse("Archive status updated successfully", voiceNote));

        } catch (Exception e) {
            logger.error("Failed to toggle archive: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to toggle archive: " + e.getMessage()));
        }
    }

    /**
     * Get voice note statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get Voice Note Statistics", description = "Get user's voice note statistics")
    public ResponseEntity<?> getVoiceNoteStats(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNoteService.VoiceNoteStats stats = voiceNoteService.getUserVoiceNoteStats(user);

            return ResponseEntity.ok(createSuccessResponse("Statistics retrieved successfully", stats));

        } catch (Exception e) {
            logger.error("Failed to get voice note stats: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get statistics: " + e.getMessage()));
        }
    }

    /**
     * Get user tags
     */
    @GetMapping("/tags")
    @Operation(summary = "Get User Tags", description = "Get all tags used by user")
    public ResponseEntity<?> getUserTags(Authentication authentication) {
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            Set<String> tagsSet = voiceNoteService.getUserTags(user);
            List<String> tags = PageConverter.setToList(tagsSet);

            return ResponseEntity.ok(createSuccessResponse("Tags retrieved successfully", tags));

        } catch (Exception e) {
            logger.error("Failed to get user tags: {}", e.getMessage());
            return ResponseEntity.internalServerError().body(createErrorResponse("Failed to get tags: " + e.getMessage()));
        }
    }

    /**
     * Download audio file
     */
    @GetMapping("/{id}/audio")
    @Operation(summary = "Download Audio File", description = "Download voice note audio file")
    public ResponseEntity<Resource> downloadAudioFile(
            @PathVariable Long id,
            Authentication authentication) {
        
        try {
            User user = userService.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
            VoiceNote voiceNote = voiceNoteService.getVoiceNote(id, user);
            Path filePath = Paths.get(voiceNote.getAudioFilePath());
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                String contentType = "audio/" + voiceNote.getAudioFormat();
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                               "attachment; filename=\"" + voiceNote.getAudioFileName() + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (MalformedURLException e) {
            logger.error("Failed to download audio file: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        } catch (Exception e) {
            logger.error("Failed to download audio file: {}", e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Create success response
     */
    private Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    /**
     * Create error response
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}