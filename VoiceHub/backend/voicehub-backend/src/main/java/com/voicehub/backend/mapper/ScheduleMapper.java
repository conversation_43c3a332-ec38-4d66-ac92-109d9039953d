package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.Schedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日程Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface ScheduleMapper extends BaseMapper<Schedule> {

    /**
     * 根据用户查找所有日程，按开始时间升序
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} ORDER BY start_time ASC")
    List<Schedule> findByUserIdOrderByStartTimeAsc(@Param("userId") Long userId);

    /**
     * 根据用户和状态查找日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND status = #{status} ORDER BY start_time ASC")
    List<Schedule> findByUserIdAndStatusOrderByStartTimeAsc(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据用户查找指定时间范围内的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{startTime} AND start_time <= #{endTime} ORDER BY start_time ASC")
    List<Schedule> findByUserIdAndTimeRange(@Param("userId") Long userId, 
                                           @Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户和优先级查找日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND priority = #{priority} ORDER BY start_time ASC")
    List<Schedule> findByUserIdAndPriorityOrderByStartTimeAsc(@Param("userId") Long userId, @Param("priority") String priority);

    /**
     * 搜索日程（标题和描述）
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND (title ILIKE CONCAT('%', #{keyword}, '%') OR description ILIKE CONCAT('%', #{keyword}, '%')) ORDER BY start_time ASC")
    List<Schedule> searchByKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 查找今天的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND DATE(start_time) = CURRENT_DATE ORDER BY start_time ASC")
    List<Schedule> findTodaySchedules(@Param("userId") Long userId);

    /**
     * 查找即将到来的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= NOW() AND start_time <= #{endTime} ORDER BY start_time ASC")
    List<Schedule> findUpcomingSchedules(@Param("userId") Long userId, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找过期的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND end_time < NOW() AND status != 'COMPLETED' ORDER BY start_time DESC")
    List<Schedule> findOverdueSchedules(@Param("userId") Long userId);

    /**
     * 查找本周的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{weekStart} AND start_time < #{weekEnd} ORDER BY start_time ASC")
    List<Schedule> findThisWeekSchedules(@Param("userId") Long userId, 
                                        @Param("weekStart") LocalDateTime weekStart, 
                                        @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 查找本月的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{monthStart} AND start_time < #{monthEnd} ORDER BY start_time ASC")
    List<Schedule> findThisMonthSchedules(@Param("userId") Long userId, 
                                         @Param("monthStart") LocalDateTime monthStart, 
                                         @Param("monthEnd") LocalDateTime monthEnd);

    /**
     * 查找语音创建的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND created_by_voice = true ORDER BY start_time ASC")
    List<Schedule> findVoiceCreatedSchedules(@Param("userId") Long userId);

    /**
     * 查找需要提醒的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND reminder_minutes IS NOT NULL AND start_time - INTERVAL '1 minute' * reminder_minutes <= NOW() AND start_time > NOW() AND status = 'SCHEDULED'")
    List<Schedule> findSchedulesNeedingReminder(@Param("userId") Long userId);

    /**
     * 根据ID和用户查找日程（安全检查）
     */
    @Select("SELECT * FROM schedules WHERE id = #{id} AND user_id = #{userId}")
    Schedule findByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 统计用户日程总数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定状态的日程数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId} AND status = #{status}")
    long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 统计用户今天的日程数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId} AND DATE(start_time) = CURRENT_DATE")
    long countTodaySchedulesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户本周的日程数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId} AND start_time >= #{weekStart} AND start_time < #{weekEnd}")
    long countThisWeekSchedulesByUserId(@Param("userId") Long userId,
                                       @Param("weekStart") LocalDateTime weekStart,
                                       @Param("weekEnd") LocalDateTime weekEnd);

    // ========== Service层需要的额外方法 ==========

    /**
     * 查找冲突的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND " +
            "((start_time <= #{startTime} AND end_time > #{startTime}) OR " +
            "(start_time < #{endTime} AND end_time >= #{endTime}) OR " +
            "(start_time >= #{startTime} AND end_time <= #{endTime}))")
    List<Schedule> findConflictingSchedules(@Param("userId") Long userId,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户和状态统计日程数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId} AND status = #{status}")
    long countByUserAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据用户统计日程数
     */
    @Select("SELECT COUNT(*) FROM schedules WHERE user_id = #{userId}")
    long countByUser(@Param("userId") Long userId);

    /**
     * 查找用户按开始时间升序排列的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} ORDER BY start_time ASC")
    List<Schedule> findByUserOrderByStartTimeAsc(@Param("userId") Long userId);

    /**
     * 查找用户按开始时间降序排列的日程（分页）
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} ORDER BY start_time DESC")
    IPage<Schedule> findByUserOrderByStartTimeDesc(@Param("userId") Long userId, Page<Schedule> page);

    // ========== 补充缺失的方法 ==========

    /**
     * 查找用户指定时间范围内的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{startTime} AND start_time <= #{endTime}")
    List<Schedule> findByUserAndTimeRange(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找用户本周的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{weekStart} AND start_time < #{weekEnd}")
    List<Schedule> findWeeklySchedules(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart, @Param("weekEnd") LocalDateTime weekEnd);

    /**
     * 查找用户本月的日程
     */
    @Select("SELECT * FROM schedules WHERE user_id = #{userId} AND start_time >= #{monthStart} AND start_time < #{monthEnd}")
    List<Schedule> findMonthlySchedules(@Param("userId") Long userId, @Param("monthStart") LocalDateTime monthStart, @Param("monthEnd") LocalDateTime monthEnd);

    /**
     * 查找需要提醒的日程
     */
    @Select("SELECT * FROM schedules WHERE start_time - INTERVAL '1 minute' * reminder_minutes <= #{reminderTime} AND start_time > #{reminderTime} AND status = 'SCHEDULED'")
    List<Schedule> findSchedulesForReminder(@Param("reminderTime") LocalDateTime reminderTime);
}
