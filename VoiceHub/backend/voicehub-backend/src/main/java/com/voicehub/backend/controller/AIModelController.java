package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.AIModelService;
import com.voicehub.backend.service.TongYiQianWenService;
import com.voicehub.backend.service.UserService;
import com.voicehub.backend.service.ZhiPuAIService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型管理控制器
 * 提供AI模型选择、切换、性能监控等功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/ai/models")
@Tag(name = "AI模型管理", description = "AI模型选择、切换和监控相关接口")
@PreAuthorize("hasRole('USER')")
public class AIModelController {

    private static final Logger logger = LoggerFactory.getLogger(AIModelController.class);

    @Autowired
    private AIModelService aiModelService;

    @Autowired
    private TongYiQianWenService tongYiQianWenService;

    @Autowired
    private ZhiPuAIService zhiPuAIService;

    @Autowired
    private UserService userService;

    /**
     * 获取所有可用的AI模型
     * 返回系统支持的AI模型列表及其性能信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回可用模型列表
     */
    @GetMapping
    @Operation(summary = "获取可用AI模型", description = "获取系统支持的所有AI模型及其性能信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAvailableModels(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        List<AIModelService.ModelPerformance> models = aiModelService.getAvailableModels();
        
        Map<String, Object> data = new HashMap<>();
        data.put("models", models);
        data.put("totalCount", models.size());
        data.put("availableCount", models.stream().mapToInt(m -> m.isAvailable() ? 1 : 0).sum());
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取AI模型列表成功"));
    }

    /**
     * 获取AI模型使用统计
     * 返回各个AI模型的使用情况和性能统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回使用统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取模型使用统计", description = "获取各AI模型的使用情况和性能统计")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getModelStats(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> stats = aiModelService.getModelUsageStats();
        
        return ResponseEntity.ok(ApiResponse.success(stats, "获取模型统计成功"));
    }

    /**
     * 测试所有AI模型
     * 测试各个AI模型的可用性和响应性能
     * 
     * @param authentication 当前用户认证信息
     * @return 返回测试结果
     */
    @PostMapping("/test")
    @Operation(summary = "测试AI模型", description = "测试所有AI模型的可用性和响应性能")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testAllModels(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        logger.info("用户 {} 请求测试所有AI模型", user.getUsername());
        
        Map<String, Object> testResults = aiModelService.testAllModels();
        
        // 添加测试摘要
        int totalModels = testResults.size();
        int availableModels = 0;
        int totalResponseTime = 0;
        
        for (Object result : testResults.values()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> modelResult = (Map<String, Object>) result;
            if (Boolean.TRUE.equals(modelResult.get("available"))) {
                availableModels++;
                if (modelResult.get("responseTime") != null) {
                    totalResponseTime += (Integer) modelResult.get("responseTime");
                }
            }
        }
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalModels", totalModels);
        summary.put("availableModels", availableModels);
        summary.put("unavailableModels", totalModels - availableModels);
        summary.put("averageResponseTime", availableModels > 0 ? totalResponseTime / availableModels : 0);
        
        Map<String, Object> data = new HashMap<>();
        data.put("testResults", testResults);
        data.put("summary", summary);
        data.put("testTime", System.currentTimeMillis());
        
        return ResponseEntity.ok(ApiResponse.success(data, "AI模型测试完成"));
    }

    /**
     * 获取通义千问模型信息
     * 返回通义千问的详细配置和状态信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回通义千问模型信息
     */
    @GetMapping("/tongyi")
    @Operation(summary = "获取通义千问信息", description = "获取通义千问模型的详细信息和状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTongYiInfo(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> info = new HashMap<>();
        info.put("available", tongYiQianWenService.isAvailable());
        info.put("plusAvailable", tongYiQianWenService.isPlusAvailable());
        info.put("supportedModels", tongYiQianWenService.getSupportedModels());
        info.put("usageStats", tongYiQianWenService.getUsageStats());
        info.put("connectionTest", tongYiQianWenService.testConnection());
        
        return ResponseEntity.ok(ApiResponse.success(info, "获取通义千问信息成功"));
    }

    /**
     * 获取智谱AI模型信息
     * 返回智谱AI的详细配置和状态信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回智谱AI模型信息
     */
    @GetMapping("/zhipu")
    @Operation(summary = "获取智谱AI信息", description = "获取智谱AI模型的详细信息和状态")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getZhiPuInfo(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> info = new HashMap<>();
        info.put("available", zhiPuAIService.isAvailable());
        info.put("proAvailable", zhiPuAIService.isProAvailable());
        info.put("supportedModels", zhiPuAIService.getSupportedModels());
        info.put("usageStats", zhiPuAIService.getUsageStats());
        info.put("connectionTest", zhiPuAIService.testConnection());
        
        return ResponseEntity.ok(ApiResponse.success(info, "获取智谱AI信息成功"));
    }

    /**
     * 比较AI模型性能
     * 对比不同AI模型在相同任务上的表现
     * 
     * @param request 比较请求，包含测试内容和比较参数
     * @param authentication 当前用户认证信息
     * @return 返回模型比较结果
     */
    @PostMapping("/compare")
    @Operation(summary = "比较AI模型", description = "对比不同AI模型在相同任务上的表现")
    public ResponseEntity<ApiResponse<Map<String, Object>>> compareModels(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        String testPrompt = (String) request.get("prompt");
        if (testPrompt == null || testPrompt.trim().isEmpty()) {
            testPrompt = "请简单介绍一下人工智能的发展历程。";
        }
        
        @SuppressWarnings("unchecked")
        List<String> modelTypes = (List<String>) request.getOrDefault("models", 
            List.of("OPENAI", "TONGYI_QIANWEN", "ZHIPU_AI"));
        
        logger.info("用户 {} 请求比较AI模型: {}", user.getUsername(), modelTypes);
        
        Map<String, Object> comparison = new HashMap<>();
        
        for (String modelTypeStr : modelTypes) {
            try {
                AIModelService.AIModelType modelType = AIModelService.AIModelType.valueOf(modelTypeStr);
                
                long startTime = System.currentTimeMillis();
                String response = aiModelService.generateResponse(null, testPrompt, modelType);
                long responseTime = System.currentTimeMillis() - startTime;
                
                Map<String, Object> result = new HashMap<>();
                result.put("response", response);
                result.put("responseTime", responseTime);
                result.put("responseLength", response.length());
                result.put("success", true);
                
                comparison.put(modelType.name().toLowerCase(), result);
                
            } catch (Exception e) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("error", e.getMessage());
                
                comparison.put(modelTypeStr.toLowerCase(), result);
            }
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("testPrompt", testPrompt);
        data.put("comparison", comparison);
        data.put("comparisonTime", System.currentTimeMillis());
        
        return ResponseEntity.ok(ApiResponse.success(data, "AI模型比较完成"));
    }

    /**
     * 获取模型推荐
     * 根据任务类型推荐最适合的AI模型
     * 
     * @param taskType 任务类型
     * @param authentication 当前用户认证信息
     * @return 返回模型推荐结果
     */
    @GetMapping("/recommend")
    @Operation(summary = "获取模型推荐", description = "根据任务类型推荐最适合的AI模型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getModelRecommendation(
            @RequestParam(required = false) String taskType,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> recommendations = new HashMap<>();
        
        // 根据任务类型推荐模型
        if ("chinese".equals(taskType)) {
            recommendations.put("primary", "TONGYI_QIANWEN");
            recommendations.put("alternative", "ZHIPU_AI");
            recommendations.put("reason", "中文理解和表达能力强");
        } else if ("creative".equals(taskType)) {
            recommendations.put("primary", "OPENAI_GPT4");
            recommendations.put("alternative", "OPENAI");
            recommendations.put("reason", "创意能力和想象力出色");
        } else if ("analytical".equals(taskType)) {
            recommendations.put("primary", "ZHIPU_AI_PRO");
            recommendations.put("alternative", "OPENAI_GPT4");
            recommendations.put("reason", "逻辑推理和分析能力强");
        } else if ("fast".equals(taskType)) {
            recommendations.put("primary", "TONGYI_QIANWEN");
            recommendations.put("alternative", "ZHIPU_AI");
            recommendations.put("reason", "响应速度快，成本较低");
        } else {
            recommendations.put("primary", "OPENAI");
            recommendations.put("alternative", "TONGYI_QIANWEN");
            recommendations.put("reason", "通用性强，适合大多数任务");
        }
        
        // 添加可用性检查
        List<AIModelService.ModelPerformance> availableModels = aiModelService.getAvailableModels();
        Map<String, Boolean> availability = new HashMap<>();
        for (AIModelService.ModelPerformance model : availableModels) {
            availability.put(model.getModelType().name(), model.isAvailable());
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("taskType", taskType);
        data.put("recommendations", recommendations);
        data.put("availability", availability);
        data.put("allModels", availableModels);
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取模型推荐成功"));
    }
}
