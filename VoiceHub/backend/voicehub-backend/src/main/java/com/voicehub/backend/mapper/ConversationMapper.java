package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voicehub.backend.entity.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface ConversationMapper extends BaseMapper<Conversation> {

    /**
     * 根据ID和用户查找对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE id = #{id} AND user_id = #{userId}")
    Conversation findByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查找用户的所有对话，按最后消息时间降序
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} ORDER BY last_activity_at DESC")
    List<Conversation> findByUserIdOrderByLastActivityAtDesc(@Param("userId") Long userId);

    /**
     * 分页查找用户的对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} ORDER BY last_activity_at DESC")
    IPage<Conversation> findByUserIdOrderByLastActivityAtDesc(@Param("userId") Long userId, Page<Conversation> page);

    /**
     * 根据用户和状态查找对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND status = #{status} ORDER BY last_activity_at DESC")
    List<Conversation> findByUserIdAndStatusOrderByLastActivityAtDesc(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据用户和类型查找对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND type = #{type} ORDER BY last_activity_at DESC")
    List<Conversation> findByUserIdAndTypeOrderByLastActivityAtDesc(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 查找收藏的对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND is_favorite = true ORDER BY last_activity_at DESC")
    List<Conversation> findFavoriteConversations(@Param("userId") Long userId);

    /**
     * 查找已归档的对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND is_archived = true ORDER BY last_activity_at DESC")
    List<Conversation> findArchivedConversations(@Param("userId") Long userId);

    /**
     * 搜索对话标题
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND title ILIKE CONCAT('%', #{keyword}, '%') ORDER BY last_activity_at DESC")
    List<Conversation> searchByTitle(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 查找今天的对话
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND DATE(created_at) = CURRENT_DATE ORDER BY last_activity_at DESC")
    List<Conversation> findTodayConversations(@Param("userId") Long userId);

    /**
     * 查找本周的对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND created_at >= #{weekStart} ORDER BY last_activity_at DESC")
    List<Conversation> findThisWeekConversations(@Param("userId") Long userId, @Param("weekStart") LocalDateTime weekStart);

    /**
     * 查找本月的对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND created_at >= #{monthStart} ORDER BY last_activity_at DESC")
    List<Conversation> findThisMonthConversations(@Param("userId") Long userId, @Param("monthStart") LocalDateTime monthStart);

    /**
     * 查找最近的对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND last_activity_at >= #{since} ORDER BY last_activity_at DESC")
    List<Conversation> findRecentConversations(@Param("userId") Long userId, @Param("since") LocalDateTime since);

    /**
     * 查找高情绪分数的对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND mood_score >= #{minScore} ORDER BY mood_score DESC")
    List<Conversation> findPositiveConversations(@Param("userId") Long userId, @Param("minScore") Double minScore);

    /**
     * 统计用户对话总数
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE user_id = #{userId}")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户活跃对话数
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE user_id = #{userId} AND status = 'ACTIVE'")
    long countActiveByUserId(@Param("userId") Long userId);

    /**
     * 统计用户收藏对话数
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE user_id = #{userId} AND is_favorite = true")
    long countFavoriteByUserId(@Param("userId") Long userId);

    /**
     * 分页查询用户对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} ORDER BY updated_at DESC")
    IPage<Conversation> findByUserId(@Param("userId") Long userId, Page<Conversation> page);

    /**
     * 搜索用户对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND title LIKE CONCAT('%', #{keyword}, '%')")
    List<Conversation> searchByUserIdAndKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);

    /**
     * 根据类型查询对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND type = #{type}")
    List<Conversation> findByUserIdAndType(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 查询收藏对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND is_favorite = true")
    List<Conversation> findFavoriteByUserId(@Param("userId") Long userId);

    /**
     * 查询归档对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} AND is_archived = true")
    List<Conversation> findArchivedByUserId(@Param("userId") Long userId);

    /**
     * 查询最近对话
     */
    @Select("SELECT * FROM conversations WHERE user_id = #{userId} ORDER BY last_activity_at DESC LIMIT #{limit}")
    List<Conversation> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 统计归档对话数
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE user_id = #{userId} AND is_archived = true")
    long countArchivedByUserId(@Param("userId") Long userId);
}
