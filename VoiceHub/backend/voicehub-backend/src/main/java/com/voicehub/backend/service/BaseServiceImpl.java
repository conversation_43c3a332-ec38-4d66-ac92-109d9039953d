package com.voicehub.backend.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voicehub.backend.entity.BaseEntity;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * MyBatis-Plus Service基类
 * 提供通用的CRUD操作和业务逻辑
 * 
 * @param <M> Mapper类型
 * @param <T> 实体类型
 */
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> 
        extends ServiceImpl<M, T> {

    /**
     * 根据ID查找实体（MyBatis-Plus自动处理逻辑删除）
     */
    public T findById(Serializable id) {
        return this.getById(id);
    }

    /**
     * 查找所有未删除的实体（MyBatis-Plus自动处理逻辑删除）
     */
    public List<T> findAll() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_at");
        return this.list(queryWrapper);
    }

    /**
     * 根据创建时间范围查找实体
     */
    public List<T> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("created_at", startDate, endDate);
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByDesc("created_at");
        return this.list(queryWrapper);
    }

    /**
     * 根据更新时间范围查找实体
     */
    public List<T> findByUpdatedAtBetween(LocalDateTime startDate, LocalDateTime endDate) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("updated_at", startDate, endDate);
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByDesc("updated_at");
        return this.list(queryWrapper);
    }

    /**
     * 软删除实体
     */
    @Transactional
    public boolean softDelete(Serializable id) {
        T entity = this.getById(id);
        if (entity != null) {
            entity.setDeleted(1);
            entity.setUpdatedAt(LocalDateTime.now());
            return this.updateById(entity);
        }
        return false;
    }

    /**
     * 批量软删除实体
     */
    @Transactional
    public boolean softDeleteBatch(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return false;
        }
        
        List<T> entities = this.listByIds(idList);
        if (!entities.isEmpty()) {
            entities.forEach(entity -> {
                entity.setDeleted(1);
                entity.setUpdatedAt(LocalDateTime.now());
            });
            return this.updateBatchById(entities);
        }
        return false;
    }

    /**
     * 恢复软删除的实体
     */
    @Transactional
    public boolean restore(Serializable id) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("deleted", 1);
        
        T entity = this.getOne(queryWrapper);
        if (entity != null) {
            entity.setDeleted(0);
            entity.setUpdatedAt(LocalDateTime.now());
            return this.updateById(entity);
        }
        return false;
    }

    /**
     * 统计未删除的实体数量
     */
    public long countActive() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        return this.count(queryWrapper);
    }

    /**
     * 统计已删除的实体数量
     */
    public long countDeleted() {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 1);
        return this.count(queryWrapper);
    }

    /**
     * 检查实体是否存在（未删除）
     */
    public boolean existsById(Serializable id) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("deleted", 0);
        return this.count(queryWrapper) > 0;
    }
}
