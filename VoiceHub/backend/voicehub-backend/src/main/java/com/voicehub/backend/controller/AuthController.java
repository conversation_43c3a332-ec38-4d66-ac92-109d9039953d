package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.dto.AuthResponse;
import com.voicehub.backend.dto.LoginRequest;
import com.voicehub.backend.dto.RegisterRequest;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 处理用户登录、注册等认证相关请求
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     * 创建新用户账户，包括用户名、邮箱、密码等基本信息
     *
     * @param request 注册请求参数，包含用户名、邮箱、密码等信息
     * @return 注册成功返回用户信息和JWT令牌，失败返回错误信息
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户，返回用户信息和JWT令牌")
    public ResponseEntity<ApiResponse<AuthResponse>> register(@Valid @RequestBody RegisterRequest request) {
        logger.info("用户注册请求: {}", request.getUsername());

        AuthResponse response = userService.register(request);

        logger.info("用户注册成功: {}", request.getUsername());
        return ResponseEntity.ok(ApiResponse.success(response, "注册成功"));
    }

    /**
     * 用户登录
     * 验证用户身份并返回JWT令牌
     *
     * @param request 登录请求参数，包含用户名/邮箱和密码
     * @return 登录成功返回用户信息和JWT令牌，失败返回错误信息
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "验证用户身份并返回JWT令牌")
    public ResponseEntity<ApiResponse<AuthResponse>> login(@Valid @RequestBody LoginRequest request) {
        logger.info("用户登录请求: {}", request.getUsernameOrEmail());

        AuthResponse response = userService.login(request);

        logger.info("用户登录成功: {}", request.getUsernameOrEmail());
        return ResponseEntity.ok(ApiResponse.success(response, "登录成功"));
    }

    /**
     * 检查用户名可用性
     * 验证指定用户名是否已被注册
     *
     * @param username 要检查的用户名
     * @return 返回用户名是否可用的状态
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名可用性", description = "验证指定用户名是否已被注册")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> checkUsername(@RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);

        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);

        String message = available ? "用户名可用" : "用户名已被使用";
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 检查邮箱可用性
     * 验证指定邮箱是否已被注册
     *
     * @param email 要检查的邮箱地址
     * @return 返回邮箱是否可用的状态
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱可用性", description = "验证指定邮箱是否已被注册")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> checkEmail(@RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);

        Map<String, Boolean> data = new HashMap<>();
        data.put("available", available);

        String message = available ? "邮箱可用" : "邮箱已被使用";
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 获取当前用户信息
     * 从JWT令牌中获取当前登录用户的详细信息
     *
     * @param authentication 当前认证信息
     * @return 返回当前用户的详细信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "从JWT令牌中获取当前登录用户的详细信息")
    public ResponseEntity<ApiResponse<User>> getCurrentUser(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            throw BusinessException.unauthorized("用户未登录");
        }

        String username = authentication.getName();
        User user = userService.findByUsername(username)
                .orElseThrow(() -> BusinessException.notFound("用户不存在"));

        return ResponseEntity.ok(ApiResponse.success(user, "获取用户信息成功"));
    }
}