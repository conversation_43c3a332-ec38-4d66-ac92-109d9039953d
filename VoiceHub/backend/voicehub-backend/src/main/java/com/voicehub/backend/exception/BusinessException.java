package com.voicehub.backend.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * <AUTHOR> Team
 */
public class BusinessException extends RuntimeException {

    private final int code;

    /**
     * 构造函数
     * @param message 异常消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = 400; // 默认为400 Bad Request
    }

    /**
     * 构造函数
     * @param code HTTP状态码
     * @param message 异常消息
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造函数
     * @param message 异常消息
     * @param cause 原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = 400;
    }

    /**
     * 构造函数
     * @param code HTTP状态码
     * @param message 异常消息
     * @param cause 原因异常
     */
    public BusinessException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    /**
     * 获取错误代码
     * @return 错误代码
     */
    public int getCode() {
        return code;
    }

    // 静态工厂方法

    /**
     * 创建参数验证异常
     * @param message 异常消息
     * @return 业务异常
     */
    public static BusinessException validationError(String message) {
        return new BusinessException(400, message);
    }

    /**
     * 创建未授权异常
     * @param message 异常消息
     * @return 业务异常
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(401, message);
    }

    /**
     * 创建禁止访问异常
     * @param message 异常消息
     * @return 业务异常
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(403, message);
    }

    /**
     * 创建资源未找到异常
     * @param message 异常消息
     * @return 业务异常
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(404, message);
    }

    /**
     * 创建服务器内部错误异常
     * @param message 异常消息
     * @return 业务异常
     */
    public static BusinessException internalError(String message) {
        return new BusinessException(500, message);
    }
}
