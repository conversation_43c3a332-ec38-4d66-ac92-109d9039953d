package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户详细资料实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_profiles")
public class UserProfile extends BaseEntity {

    /**
     * 关联用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 个人简介
     */
    @TableField("bio")
    private String bio;

    /**
     * 职业
     */
    @TableField("occupation")
    private String occupation;

    /**
     * 公司名称
     */
    @TableField("company")
    private String company;

    /**
     * 所在地区
     */
    @TableField("location")
    private String location;

    /**
     * 出生日期
     */
    @TableField("birth_date")
    private LocalDate birthDate;

    /**
     * 性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 兴趣爱好数组
     */
    @TableField(value = "interests", typeHandler = JacksonTypeHandler.class)
    private List<String> interests;

    /**
     * 目标数组
     */
    @TableField(value = "goals", typeHandler = JacksonTypeHandler.class)
    private List<String> goals;

    /**
     * 隐私设置（JSON格式）
     */
    @TableField(value = "privacy_settings", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> privacySettings;

    public UserProfile() {
        super();
    }

    public UserProfile(Long userId) {
        this();
        this.userId = userId;
    }
}
