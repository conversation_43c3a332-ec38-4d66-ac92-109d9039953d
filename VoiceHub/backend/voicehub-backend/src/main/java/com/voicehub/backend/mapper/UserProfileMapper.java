package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.UserProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户详细资料Mapper接口
 */
@Mapper
public interface UserProfileMapper extends BaseMapper<UserProfile> {

    /**
     * 根据用户ID查找用户资料
     */
    @Select("SELECT * FROM voicehub.user_profiles WHERE user_id = #{userId}")
    UserProfile findByUserId(@Param("userId") Long userId);

    /**
     * 根据职业查找用户资料
     */
    @Select("SELECT * FROM voicehub.user_profiles WHERE occupation = #{occupation}")
    List<UserProfile> findByOccupation(@Param("occupation") String occupation);

    /**
     * 根据公司查找用户资料
     */
    @Select("SELECT * FROM voicehub.user_profiles WHERE company = #{company}")
    List<UserProfile> findByCompany(@Param("company") String company);

    /**
     * 根据地区查找用户资料
     */
    @Select("SELECT * FROM voicehub.user_profiles WHERE location = #{location}")
    List<UserProfile> findByLocation(@Param("location") String location);

    /**
     * 根据性别查找用户资料
     */
    @Select("SELECT * FROM voicehub.user_profiles WHERE gender = #{gender}")
    List<UserProfile> findByGender(@Param("gender") String gender);

    /**
     * 检查用户是否已有资料
     */
    @Select("SELECT COUNT(*) > 0 FROM voicehub.user_profiles WHERE user_id = #{userId}")
    boolean existsByUserId(@Param("userId") Long userId);
}
