package com.voicehub.backend.service;

import com.voicehub.backend.entity.UserAchievement;
import com.voicehub.backend.mapper.UserAchievementMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户成就服务实现
 */
@Service
@Transactional
public class UserAchievementService extends BaseServiceImpl<UserAchievementMapper, UserAchievement> {

    @Autowired
    private UserAchievementMapper userAchievementMapper;

    @Autowired
    private NotificationService notificationService;

    /**
     * 解锁用户成就
     */
    public UserAchievement unlockAchievement(Long userId, String achievementType, String title, String description, Integer points) {
        // 检查是否已经获得该成就
        if (userAchievementMapper.existsByUserIdAndAchievementType(userId, achievementType)) {
            return null; // 已经获得，不重复解锁
        }
        
        UserAchievement achievement = new UserAchievement();
        achievement.setUserId(userId);
        achievement.setAchievementType(achievementType);
        achievement.setAchievementName(title);
        achievement.setDescription(description);
        achievement.setPoints(points != null ? points : 0);
        achievement.setUnlockedAt(LocalDateTime.now());
        achievement.setCreatedAt(LocalDateTime.now());
        achievement.setUpdatedAt(LocalDateTime.now());
        
        userAchievementMapper.insert(achievement);
        
        // 发送成就通知
        notificationService.createAchievementNotification(userId, title, description);
        
        return achievement;
    }

    /**
     * 根据用户ID获取成就列表
     */
    public List<UserAchievement> getByUserId(Long userId) {
        return userAchievementMapper.findByUserId(userId);
    }

    /**
     * 根据用户ID和成就类型获取成就
     */
    public List<UserAchievement> getByUserIdAndAchievementType(Long userId, String achievementType) {
        return userAchievementMapper.findByUserIdAndAchievementType(userId, achievementType);
    }

    /**
     * 检查用户是否已获得特定成就
     */
    public boolean hasAchievement(Long userId, String achievementType) {
        return userAchievementMapper.existsByUserIdAndAchievementType(userId, achievementType);
    }

    /**
     * 获取用户最近获得的成就
     */
    public List<UserAchievement> getRecentAchievements(Long userId, int limit) {
        return userAchievementMapper.findRecentByUserId(userId, limit);
    }

    /**
     * 获取用户积分最高的成就
     */
    public List<UserAchievement> getTopPointsAchievements(Long userId, int limit) {
        return userAchievementMapper.findTopPointsByUserId(userId, limit);
    }

    /**
     * 获取用户成就统计
     */
    public UserAchievementStats getUserAchievementStats(Long userId) {
        long totalAchievements = userAchievementMapper.countByUserId(userId);
        long totalPoints = userAchievementMapper.sumPointsByUserId(userId);
        
        UserAchievementStats stats = new UserAchievementStats();
        stats.setTotalAchievements(totalAchievements);
        stats.setTotalPoints(totalPoints);
        
        return stats;
    }

    /**
     * 根据时间范围获取用户成就
     */
    public List<UserAchievement> getByUserIdAndDateRange(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        return userAchievementMapper.findByUserIdAndUnlockedAtBetween(userId, startDate, endDate);
    }

    /**
     * 根据成就类型获取所有用户的成就
     */
    public List<UserAchievement> getByAchievementType(String achievementType) {
        return userAchievementMapper.findByAchievementType(achievementType);
    }

    /**
     * 统计特定成就的获得人数
     */
    public long countUsersByAchievementType(String achievementType) {
        return userAchievementMapper.countUsersByAchievementType(achievementType);
    }

    /**
     * 获取今日获得成就的用户ID列表
     */
    public List<Long> getTodayAchievedUserIds() {
        return userAchievementMapper.findTodayAchievedUserIds();
    }

    /**
     * 检查并解锁基础成就
     */
    public void checkAndUnlockBasicAchievements(Long userId) {
        // 检查首次登录成就
        if (!hasAchievement(userId, "FIRST_LOGIN")) {
            unlockAchievement(userId, "FIRST_LOGIN", "初来乍到", "欢迎来到VoiceHub！", 10);
        }
    }

    /**
     * 检查并解锁语音笔记相关成就
     */
    public void checkAndUnlockVoiceNoteAchievements(Long userId, int voiceNoteCount) {
        // 第一个语音笔记
        if (voiceNoteCount == 1 && !hasAchievement(userId, "FIRST_VOICE_NOTE")) {
            unlockAchievement(userId, "FIRST_VOICE_NOTE", "初试牛刀", "创建了第一个语音笔记", 20);
        }
        
        // 10个语音笔记
        if (voiceNoteCount >= 10 && !hasAchievement(userId, "VOICE_NOTE_10")) {
            unlockAchievement(userId, "VOICE_NOTE_10", "勤奋记录者", "创建了10个语音笔记", 50);
        }
        
        // 50个语音笔记
        if (voiceNoteCount >= 50 && !hasAchievement(userId, "VOICE_NOTE_50")) {
            unlockAchievement(userId, "VOICE_NOTE_50", "语音达人", "创建了50个语音笔记", 100);
        }
        
        // 100个语音笔记
        if (voiceNoteCount >= 100 && !hasAchievement(userId, "VOICE_NOTE_100")) {
            unlockAchievement(userId, "VOICE_NOTE_100", "语音大师", "创建了100个语音笔记", 200);
        }
    }

    /**
     * 检查并解锁连续使用成就
     */
    public void checkAndUnlockStreakAchievements(Long userId, int consecutiveDays) {
        // 连续使用7天
        if (consecutiveDays >= 7 && !hasAchievement(userId, "STREAK_7_DAYS")) {
            unlockAchievement(userId, "STREAK_7_DAYS", "坚持一周", "连续使用7天", 30);
        }
        
        // 连续使用30天
        if (consecutiveDays >= 30 && !hasAchievement(userId, "STREAK_30_DAYS")) {
            unlockAchievement(userId, "STREAK_30_DAYS", "月度坚持", "连续使用30天", 100);
        }
        
        // 连续使用100天
        if (consecutiveDays >= 100 && !hasAchievement(userId, "STREAK_100_DAYS")) {
            unlockAchievement(userId, "STREAK_100_DAYS", "百日坚持", "连续使用100天", 300);
        }
    }

    /**
     * 检查并解锁AI功能使用成就
     */
    public void checkAndUnlockAiAchievements(Long userId, int aiTaskCount) {
        // 第一次使用AI功能
        if (aiTaskCount == 1 && !hasAchievement(userId, "FIRST_AI_TASK")) {
            unlockAchievement(userId, "FIRST_AI_TASK", "AI初体验", "第一次使用AI功能", 25);
        }
        
        // 使用AI功能50次
        if (aiTaskCount >= 50 && !hasAchievement(userId, "AI_TASK_50")) {
            unlockAchievement(userId, "AI_TASK_50", "AI助手", "使用AI功能50次", 75);
        }
        
        // 使用AI功能200次
        if (aiTaskCount >= 200 && !hasAchievement(userId, "AI_TASK_200")) {
            unlockAchievement(userId, "AI_TASK_200", "AI专家", "使用AI功能200次", 150);
        }
    }

    /**
     * 删除用户成就
     */
    public boolean deleteAchievement(Long achievementId) {
        UserAchievement achievement = userAchievementMapper.selectById(achievementId);
        if (achievement != null) {
            userAchievementMapper.deleteById(achievementId);
            return true;
        }
        return false;
    }

    // 内部类：用户成就统计
    public static class UserAchievementStats {
        private long totalAchievements;
        private long totalPoints;

        // Getters and Setters
        public long getTotalAchievements() { return totalAchievements; }
        public void setTotalAchievements(long totalAchievements) { this.totalAchievements = totalAchievements; }
        public long getTotalPoints() { return totalPoints; }
        public void setTotalPoints(long totalPoints) { this.totalPoints = totalPoints; }
    }
}
