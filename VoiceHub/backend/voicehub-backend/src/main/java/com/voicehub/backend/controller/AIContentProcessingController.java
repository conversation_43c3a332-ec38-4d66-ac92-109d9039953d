package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.AIContentProcessingService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * AI内容处理控制器
 * 提供各种AI内容处理功能的REST API接口
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/ai")
@Tag(name = "AI内容处理", description = "AI内容处理相关接口，包括摘要、翻译、分析等功能")
@PreAuthorize("hasRole('USER')")
public class AIContentProcessingController {

    private static final Logger logger = LoggerFactory.getLogger(AIContentProcessingController.class);

    @Autowired
    private AIContentProcessingService aiContentProcessingService;

    @Autowired
    private UserService userService;

    /**
     * AI内容处理
     * 根据指定的处理类型对内容进行AI处理
     * 
     * @param request 处理请求，包含内容、处理类型和选项
     * @param authentication 当前用户认证信息
     * @return 返回处理结果
     */
    @PostMapping("/process")
    @Operation(summary = "AI内容处理", description = "对指定内容进行AI处理，支持摘要、翻译、分析等多种类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> processContent(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        // 提取请求参数
        String content = (String) request.get("content");
        String typeStr = (String) request.get("type");
        @SuppressWarnings("unchecked")
        Map<String, Object> options = (Map<String, Object>) request.getOrDefault("options", new HashMap<>());
        
        // 验证必需参数
        if (content == null || content.trim().isEmpty()) {
            throw BusinessException.validationError("处理内容不能为空");
        }
        
        if (typeStr == null || typeStr.trim().isEmpty()) {
            throw BusinessException.validationError("处理类型不能为空");
        }
        
        // 解析处理类型
        AIContentProcessingService.ProcessingType type;
        try {
            type = AIContentProcessingService.ProcessingType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw BusinessException.validationError("不支持的处理类型: " + typeStr);
        }
        
        // 执行AI处理
        Map<String, Object> result = aiContentProcessingService.processContent(content, type, options, user);
        
        logger.info("AI内容处理成功: type={}, userId={}", type, user.getId());
        return ResponseEntity.ok(ApiResponse.success(result, "AI内容处理完成"));
    }

    /**
     * 获取支持的处理类型
     * 返回系统支持的所有AI处理类型及其描述
     * 
     * @return 返回支持的处理类型列表
     */
    @GetMapping("/types")
    @Operation(summary = "获取处理类型", description = "获取系统支持的所有AI处理类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupportedTypes() {
        Map<String, Object> types = new HashMap<>();
        
        for (AIContentProcessingService.ProcessingType type : AIContentProcessingService.ProcessingType.values()) {
            Map<String, Object> typeInfo = new HashMap<>();
            typeInfo.put("name", type.name());
            typeInfo.put("description", type.getDescription());
            typeInfo.put("supportedOptions", getSupportedOptions(type));
            
            types.put(type.name().toLowerCase(), typeInfo);
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("supportedTypes", types);
        data.put("totalCount", types.size());
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取处理类型成功"));
    }

    /**
     * 批量内容处理
     * 对多个内容进行相同类型的AI处理
     * 
     * @param request 批量处理请求
     * @param authentication 当前用户认证信息
     * @return 返回批量处理结果
     */
    @PostMapping("/batch-process")
    @Operation(summary = "批量内容处理", description = "对多个内容进行相同类型的AI处理")
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchProcessContent(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        // 提取请求参数
        @SuppressWarnings("unchecked")
        java.util.List<String> contents = (java.util.List<String>) request.get("contents");
        String typeStr = (String) request.get("type");
        @SuppressWarnings("unchecked")
        Map<String, Object> options = (Map<String, Object>) request.getOrDefault("options", new HashMap<>());
        
        // 验证参数
        if (contents == null || contents.isEmpty()) {
            throw BusinessException.validationError("处理内容列表不能为空");
        }
        
        if (contents.size() > 10) {
            throw BusinessException.validationError("批量处理最多支持10个内容");
        }
        
        if (typeStr == null || typeStr.trim().isEmpty()) {
            throw BusinessException.validationError("处理类型不能为空");
        }
        
        // 解析处理类型
        AIContentProcessingService.ProcessingType type;
        try {
            type = AIContentProcessingService.ProcessingType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw BusinessException.validationError("不支持的处理类型: " + typeStr);
        }
        
        // 批量处理
        java.util.List<Map<String, Object>> results = new java.util.ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < contents.size(); i++) {
            String content = contents.get(i);
            try {
                Map<String, Object> result = aiContentProcessingService.processContent(content, type, options, user);
                result.put("index", i);
                results.add(result);
                successCount++;
            } catch (Exception e) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("index", i);
                errorResult.put("success", false);
                errorResult.put("error", e.getMessage());
                errorResult.put("originalContent", content);
                results.add(errorResult);
                failureCount++;
            }
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("results", results);
        data.put("totalCount", contents.size());
        data.put("successCount", successCount);
        data.put("failureCount", failureCount);
        data.put("processingType", type.name());
        
        String message = String.format("批量处理完成，成功%d个，失败%d个", successCount, failureCount);
        logger.info("批量AI内容处理完成: type={}, userId={}, success={}, failure={}", 
                   type, user.getId(), successCount, failureCount);
        
        return ResponseEntity.ok(ApiResponse.success(data, message));
    }

    /**
     * 获取处理历史
     * 获取用户的AI处理历史记录（这里是示例实现）
     * 
     * @param authentication 当前用户认证信息
     * @return 返回处理历史
     */
    @GetMapping("/history")
    @Operation(summary = "获取处理历史", description = "获取用户的AI内容处理历史记录")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProcessingHistory(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        // 这里是示例实现，实际应该从数据库查询
        Map<String, Object> data = new HashMap<>();
        data.put("history", new java.util.ArrayList<>());
        data.put("totalCount", 0);
        data.put("message", "处理历史功能开发中");
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取处理历史成功"));
    }

    /**
     * 获取指定处理类型支持的选项
     * 
     * @param type 处理类型
     * @return 支持的选项列表
     */
    private java.util.List<String> getSupportedOptions(AIContentProcessingService.ProcessingType type) {
        java.util.List<String> options = new java.util.ArrayList<>();
        
        switch (type) {
            case SUMMARY:
                options.add("length"); // short, medium, long
                options.add("style");  // professional, casual, academic
                break;
            case TRANSLATION:
                options.add("targetLanguage"); // English, Chinese, Japanese, etc.
                break;
            case KEYWORD_EXTRACTION:
                options.add("count"); // 关键词数量
                break;
            case CREATIVE_EXPANSION:
                options.add("direction"); // 扩展方向
                break;
            case STYLE_IMPROVEMENT:
                options.add("targetStyle"); // professional, casual, academic, creative
                break;
            default:
                // 其他类型暂无特殊选项
                break;
        }
        
        return options;
    }
}
