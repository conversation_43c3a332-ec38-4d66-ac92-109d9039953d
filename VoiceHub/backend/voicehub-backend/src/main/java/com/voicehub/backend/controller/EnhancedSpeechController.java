package com.voicehub.backend.controller;

import com.voicehub.backend.dto.ApiResponse;
import com.voicehub.backend.entity.User;
import com.voicehub.backend.exception.BusinessException;
import com.voicehub.backend.exception.ResourceNotFoundException;
import com.voicehub.backend.service.EnhancedSpeechRecognitionService;
import com.voicehub.backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强的语音识别控制器
 * 提供多引擎语音识别、实时识别、批量处理等功能
 * 
 * <AUTHOR> Team
 */
@RestController
@RequestMapping("/speech/enhanced")
@Tag(name = "增强语音识别", description = "多引擎语音识别和高级处理功能")
@PreAuthorize("hasRole('USER')")
public class EnhancedSpeechController {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedSpeechController.class);

    @Autowired
    private EnhancedSpeechRecognitionService speechRecognitionService;

    @Autowired
    private UserService userService;

    /**
     * 增强语音识别
     * 支持多引擎选择、音频增强、后处理等功能
     * 
     * @param file 音频文件
     * @param engine 识别引擎（可选）
     * @param enableEnhancement 是否启用音频增强
     * @param addPunctuation 是否添加标点符号
     * @param authentication 当前用户认证信息
     * @return 返回识别结果
     */
    @PostMapping(value = "/recognize", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "增强语音识别", description = "使用多引擎进行高质量语音识别")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recognizeSpeech(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "engine", required = false) String engine,
            @RequestParam(value = "enableEnhancement", defaultValue = "true") boolean enableEnhancement,
            @RequestParam(value = "addPunctuation", defaultValue = "true") boolean addPunctuation,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        if (file.isEmpty()) {
            throw BusinessException.validationError("音频文件不能为空");
        }
        
        logger.info("增强语音识别请求: userId={}, fileName={}, engine={}", 
                   user.getId(), file.getOriginalFilename(), engine);
        
        try {
            byte[] audioData = file.getBytes();
            
            Map<String, Object> options = new HashMap<>();
            if (engine != null) options.put("engine", engine);
            options.put("enableEnhancement", enableEnhancement);
            options.put("addPunctuation", addPunctuation);
            options.put("fileName", file.getOriginalFilename());
            options.put("fileSize", file.getSize());
            
            EnhancedSpeechRecognitionService.SpeechRecognitionResult result = 
                speechRecognitionService.recognizeSpeech(audioData, options, user);
            
            Map<String, Object> data = new HashMap<>();
            data.put("text", result.getText());
            data.put("confidence", result.getConfidence());
            data.put("engine", result.getEngine().name());
            data.put("engineName", result.getEngine().getDisplayName());
            data.put("processingTime", result.getProcessingTime());
            data.put("alternatives", result.getAlternatives());
            data.put("metadata", result.metadata());
            
            return ResponseEntity.ok(ApiResponse.success(data, "语音识别完成"));
            
        } catch (Exception e) {
            logger.error("语音识别失败: userId={}, error={}", user.getId(), e.getMessage());
            throw BusinessException.internalError("语音识别失败: " + e.getMessage());
        }
    }

    /**
     * 批量语音识别
     * 一次处理多个音频文件
     * 
     * @param files 音频文件数组
     * @param engine 识别引擎（可选）
     * @param authentication 当前用户认证信息
     * @return 返回批量识别结果
     */
    @PostMapping(value = "/batch-recognize", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量语音识别", description = "同时处理多个音频文件的语音识别")
    public ResponseEntity<ApiResponse<Map<String, Object>>> batchRecognizeSpeech(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "engine", required = false) String engine,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        if (files == null || files.length == 0) {
            throw BusinessException.validationError("请选择要识别的音频文件");
        }
        
        if (files.length > 10) {
            throw BusinessException.validationError("批量识别最多支持10个文件");
        }
        
        logger.info("批量语音识别请求: userId={}, fileCount={}", user.getId(), files.length);
        
        try {
            List<byte[]> audioDataList = new ArrayList<>();
            List<String> fileNames = new ArrayList<>();
            
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    audioDataList.add(file.getBytes());
                    fileNames.add(file.getOriginalFilename());
                }
            }
            
            Map<String, Object> options = new HashMap<>();
            if (engine != null) options.put("engine", engine);
            options.put("fileNames", fileNames);
            
            List<EnhancedSpeechRecognitionService.SpeechRecognitionResult> results = 
                speechRecognitionService.batchRecognizeSpeech(audioDataList, options, user);
            
            List<Map<String, Object>> resultList = new ArrayList<>();
            int successCount = 0;
            
            for (int i = 0; i < results.size(); i++) {
                EnhancedSpeechRecognitionService.SpeechRecognitionResult result = results.get(i);
                
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("index", i);
                resultData.put("fileName", i < fileNames.size() ? fileNames.get(i) : "unknown");
                resultData.put("text", result.getText());
                resultData.put("confidence", result.getConfidence());
                resultData.put("engine", result.getEngine().name());
                resultData.put("processingTime", result.getProcessingTime());
                resultData.put("success", result.getConfidence() > 0);
                
                if (result.getConfidence() > 0) {
                    successCount++;
                }
                
                resultList.add(resultData);
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("results", resultList);
            data.put("totalCount", files.length);
            data.put("successCount", successCount);
            data.put("failureCount", files.length - successCount);
            
            String message = String.format("批量识别完成，成功%d个，失败%d个", successCount, files.length - successCount);
            return ResponseEntity.ok(ApiResponse.success(data, message));
            
        } catch (Exception e) {
            logger.error("批量语音识别失败: userId={}, error={}", user.getId(), e.getMessage());
            throw BusinessException.internalError("批量语音识别失败: " + e.getMessage());
        }
    }

    /**
     * 实时语音识别
     * 用于WebSocket实时语音流处理
     * 
     * @param request 实时识别请求
     * @param authentication 当前用户认证信息
     * @return 返回实时识别结果
     */
    @PostMapping("/realtime")
    @Operation(summary = "实时语音识别", description = "处理实时语音流的识别请求")
    public ResponseEntity<ApiResponse<Map<String, Object>>> realtimeRecognition(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        String sessionId = (String) request.get("sessionId");
        String audioDataBase64 = (String) request.get("audioData");
        
        if (sessionId == null || audioDataBase64 == null) {
            throw BusinessException.validationError("sessionId和audioData不能为空");
        }
        
        try {
            byte[] audioChunk = java.util.Base64.getDecoder().decode(audioDataBase64);
            
            Map<String, Object> options = new HashMap<>();
            options.put("realtime", true);
            options.put("sessionId", sessionId);
            
            Map<String, Object> result = speechRecognitionService.realtimeRecognition(
                audioChunk, sessionId, options, user);
            
            return ResponseEntity.ok(ApiResponse.success(result, "实时识别处理完成"));
            
        } catch (Exception e) {
            logger.error("实时语音识别失败: userId={}, sessionId={}, error={}", 
                        user.getId(), sessionId, e.getMessage());
            throw BusinessException.internalError("实时语音识别失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的识别引擎
     * 返回系统支持的所有语音识别引擎信息
     * 
     * @param authentication 当前用户认证信息
     * @return 返回引擎列表
     */
    @GetMapping("/engines")
    @Operation(summary = "获取识别引擎", description = "获取系统支持的语音识别引擎列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupportedEngines(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        List<Map<String, Object>> engines = speechRecognitionService.getSupportedEngines();
        
        Map<String, Object> data = new HashMap<>();
        data.put("engines", engines);
        data.put("totalCount", engines.size());
        data.put("availableCount", engines.stream().mapToInt(e -> 
            Boolean.TRUE.equals(e.get("available")) ? 1 : 0).sum());
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取识别引擎成功"));
    }

    /**
     * 获取识别统计信息
     * 返回用户的语音识别使用统计
     * 
     * @param authentication 当前用户认证信息
     * @return 返回统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取识别统计", description = "获取用户的语音识别使用统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecognitionStats(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> stats = speechRecognitionService.getRecognitionStats(user);
        
        return ResponseEntity.ok(ApiResponse.success(stats, "获取识别统计成功"));
    }

    /**
     * 语音识别质量评估
     * 对识别结果进行质量评估和建议
     * 
     * @param request 评估请求
     * @param authentication 当前用户认证信息
     * @return 返回评估结果
     */
    @PostMapping("/quality-assessment")
    @Operation(summary = "识别质量评估", description = "评估语音识别结果的质量并提供改进建议")
    public ResponseEntity<ApiResponse<Map<String, Object>>> assessRecognitionQuality(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        String recognizedText = (String) request.get("recognizedText");
        Double confidence = (Double) request.get("confidence");
        String engine = (String) request.get("engine");
        
        if (recognizedText == null || confidence == null) {
            throw BusinessException.validationError("recognizedText和confidence不能为空");
        }
        
        Map<String, Object> assessment = new HashMap<>();
        
        // 质量评估逻辑
        String qualityLevel;
        List<String> suggestions = new ArrayList<>();
        
        if (confidence >= 0.9) {
            qualityLevel = "优秀";
            suggestions.add("识别质量很高，无需改进");
        } else if (confidence >= 0.8) {
            qualityLevel = "良好";
            suggestions.add("识别质量较好，可考虑优化音频质量");
        } else if (confidence >= 0.6) {
            qualityLevel = "一般";
            suggestions.add("建议改善录音环境，减少背景噪音");
            suggestions.add("尝试使用不同的识别引擎");
        } else {
            qualityLevel = "较差";
            suggestions.add("建议重新录制音频");
            suggestions.add("检查音频格式和质量");
            suggestions.add("考虑使用专业录音设备");
        }
        
        assessment.put("qualityLevel", qualityLevel);
        assessment.put("confidence", confidence);
        assessment.put("textLength", recognizedText.length());
        assessment.put("suggestions", suggestions);
        assessment.put("engine", engine);
        assessment.put("assessmentTime", System.currentTimeMillis());
        
        return ResponseEntity.ok(ApiResponse.success(assessment, "质量评估完成"));
    }

    /**
     * 语音识别配置
     * 获取和更新用户的语音识别偏好设置
     * 
     * @param authentication 当前用户认证信息
     * @return 返回配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取识别配置", description = "获取用户的语音识别配置和偏好设置")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecognitionConfig(
            Authentication authentication) {
        
        User user = userService.findByUsername(authentication.getName())
                .orElseThrow(() -> ResourceNotFoundException.user(authentication.getName()));
        
        Map<String, Object> config = new HashMap<>();
        
        // 这里应该从数据库获取用户的实际配置
        config.put("defaultEngine", "BAIDU");
        config.put("enableEnhancement", true);
        config.put("addPunctuation", true);
        config.put("confidenceThreshold", 0.8);
        config.put("language", "zh-CN");
        config.put("autoSave", true);
        config.put("realtimeEnabled", false);
        
        Map<String, Object> data = new HashMap<>();
        data.put("config", config);
        data.put("availableEngines", speechRecognitionService.getSupportedEngines());
        data.put("supportedLanguages", List.of("zh-CN", "en-US", "ja-JP"));
        
        return ResponseEntity.ok(ApiResponse.success(data, "获取识别配置成功"));
    }
}
