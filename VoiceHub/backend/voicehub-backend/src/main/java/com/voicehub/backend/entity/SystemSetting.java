package com.voicehub.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统设置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("system_settings")
public class SystemSetting extends BaseEntity {

    /**
     * 设置键名，唯一
     */
    @TableField("setting_key")
    private String settingKey;

    /**
     * 设置值
     */
    @TableField("setting_value")
    private String settingValue;

    /**
     * 设置类型：STRING/INTEGER/BOOLEAN/JSON
     */
    @TableField("setting_type")
    private String settingType = "STRING";

    /**
     * 设置描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否公开设置
     */
    @TableField("is_public")
    private Boolean isPublic = false;

    public SystemSetting() {
        super();
    }

    public SystemSetting(String settingKey, String settingValue, String settingType) {
        this();
        this.settingKey = settingKey;
        this.settingValue = settingValue;
        this.settingType = settingType;
    }
}
