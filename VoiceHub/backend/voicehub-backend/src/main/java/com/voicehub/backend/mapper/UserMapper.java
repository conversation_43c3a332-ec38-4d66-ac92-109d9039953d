package com.voicehub.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voicehub.backend.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Mapper接口
 * 继承MyBatis-Plus的BaseMapper，提供基础CRUD功能
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    User findByEmail(@Param("email") String email);

    /**
     * 根据用户名或邮箱查找用户
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    User findByUsernameOrEmail(@Param("username") String username, @Param("email") String email);

    /**
     * 检查用户名是否存在
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username}")
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     * MyBatis-Plus会自动添加逻辑删除条件
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email}")
    boolean existsByEmail(@Param("email") String email);

    /**
     * 更新用户最后登录时间
     */
    @Update("UPDATE users SET last_login_at = #{lastLoginAt}, updated_at = NOW() WHERE id = #{userId}")
    void updateLastLoginTime(@Param("userId") Long userId, @Param("lastLoginAt") LocalDateTime lastLoginAt);

    /**
     * 根据角色查找用户
     */
    @Select("SELECT * FROM voicehub.users WHERE role = #{role}")
    List<User> findByRole(@Param("role") String role);

    /**
     * 查找启用状态的用户
     */
    @Select("SELECT * FROM voicehub.users WHERE is_enabled = #{enabled}")
    List<User> findByEnabled(@Param("enabled") Boolean enabled);

    /**
     * 根据创建时间范围查找用户
     */
    @Select("SELECT * FROM voicehub.users WHERE created_at BETWEEN #{startDate} AND #{endDate}")
    List<User> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM voicehub.users")
    long countAllUsers();

    /**
     * 统计启用用户数
     */
    @Select("SELECT COUNT(*) FROM voicehub.users WHERE is_enabled = true")
    long countEnabledUsers();

    /**
     * 根据订阅类型查找用户
     */
    @Select("SELECT * FROM voicehub.users WHERE subscription_type = #{subscriptionType}")
    List<User> findBySubscriptionType(@Param("subscriptionType") String subscriptionType);

    /**
     * 查找已验证的用户
     */
    @Select("SELECT * FROM voicehub.users WHERE is_verified = true")
    List<User> findVerifiedUsers();

    /**
     * 查找活跃用户
     */
    @Select("SELECT * FROM voicehub.users WHERE is_active = true")
    List<User> findActiveUsers();
}
