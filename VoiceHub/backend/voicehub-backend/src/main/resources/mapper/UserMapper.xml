<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voicehub.backend.mapper.UserMapper">

    <!-- 结果映射，解决password_hash字段映射问题 -->
    <resultMap id="UserResultMap" type="com.voicehub.backend.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="email" property="email"/>
        <result column="password_hash" property="passwordHash"/>
        <result column="full_name" property="fullName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="preferred_language" property="preferredLanguage"/>
        <result column="timezone" property="timezone"/>
        <result column="voice_settings" property="voiceSettings" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="notification_settings" property="notificationSettings" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="subscription_type" property="subscriptionType"/>
        <result column="subscription_expires_at" property="subscriptionExpiresAt"/>
        <result column="is_active" property="isActive"/>
        <result column="is_verified" property="isVerified"/>
        <result column="last_login_at" property="lastLoginAt"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 重写findByUsername方法，使用自定义结果映射 -->
    <select id="findByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT * FROM users WHERE username = #{username} AND deleted = 0
    </select>

    <!-- 重写findByEmail方法，使用自定义结果映射 -->
    <select id="findByEmail" parameterType="string" resultMap="UserResultMap">
        SELECT * FROM users WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 重写findByUsernameOrEmail方法，使用自定义结果映射 -->
    <select id="findByUsernameOrEmail" resultMap="UserResultMap">
        SELECT * FROM users WHERE (username = #{username} OR email = #{email}) AND deleted = 0
    </select>

</mapper>