package com.voicehub.backend.service;

import com.voicehub.backend.entity.User;
import com.voicehub.backend.entity.Conversation;
import com.voicehub.backend.entity.VoiceNote;
import com.voicehub.backend.mapper.UserMapper;
import com.voicehub.backend.mapper.ConversationMapper;
import com.voicehub.backend.mapper.VoiceNoteMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis-Plus集成测试
 * 验证MyBatis-Plus功能是否正常工作
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MybatisPlusIntegrationTest {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private VoiceNoteMapper voiceNoteMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private VoiceNoteService voiceNoteService;

    @Test
    public void testUserMapperBasicOperations() {
        // 创建测试用户
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPassword("password123");
        user.setFullName("Test User");
        user.setRole("USER");
        user.setEnabledFlag(true);
        user.setAccountNonExpiredFlag(true);
        user.setAccountNonLockedFlag(true);
        user.setCredentialsNonExpiredFlag(true);
        user.setCreatedAt(LocalDateTime.now());

        // 插入用户
        int result = userMapper.insert(user);
        assertEquals(1, result);
        assertNotNull(user.getId());

        // 根据用户名查找
        User foundUser = userMapper.findByUsername("testuser");
        assertNotNull(foundUser);
        assertEquals("testuser", foundUser.getUsername());
        assertEquals("<EMAIL>", foundUser.getEmail());

        // 根据邮箱查找
        User foundByEmail = userMapper.findByEmail("<EMAIL>");
        assertNotNull(foundByEmail);
        assertEquals(user.getId(), foundByEmail.getId());

        // 检查用户名是否存在
        assertTrue(userMapper.existsByUsername("testuser"));
        assertFalse(userMapper.existsByUsername("nonexistent"));

        // 检查邮箱是否存在
        assertTrue(userMapper.existsByEmail("<EMAIL>"));
        assertFalse(userMapper.existsByEmail("<EMAIL>"));

        // 更新最后登录时间
        LocalDateTime loginTime = LocalDateTime.now();
        userMapper.updateLastLoginTime(user.getId(), loginTime);

        // 验证更新
        User updatedUser = userMapper.selectById(user.getId());
        assertNotNull(updatedUser.getLastLoginAt());

        // 统计用户数
        long userCount = userMapper.countAllUsers();
        assertTrue(userCount > 0);

        long enabledCount = userMapper.countEnabledUsers();
        assertTrue(enabledCount > 0);
    }

    @Test
    public void testConversationMapperBasicOperations() {
        // 先创建用户
        User user = createTestUser("convuser", "<EMAIL>");
        userMapper.insert(user);

        // 创建对话
        Conversation conversation = new Conversation();
        conversation.setUser(user);
        conversation.setTitle("Test Conversation");
        conversation.setType(Conversation.ConversationType.GENERAL);
        conversation.setStatus(Conversation.ConversationStatus.ACTIVE);
        conversation.setMessageCount(0);
        conversation.setIsFavorite(false);
        conversation.setIsArchived(false);
        conversation.setCreatedAt(LocalDateTime.now());
        conversation.setUpdatedAt(LocalDateTime.now());
        conversation.setLastActivityAt(LocalDateTime.now());

        // 插入对话
        int result = conversationMapper.insert(conversation);
        assertEquals(1, result);
        assertNotNull(conversation.getId());

        // 根据ID和用户查找
        Conversation found = conversationMapper.findByIdAndUserId(conversation.getId(), user.getId());
        assertNotNull(found);
        assertEquals("Test Conversation", found.getTitle());

        // 查找用户的所有对话
        List<Conversation> userConversations = conversationMapper.findByUserIdOrderByLastActivityAtDesc(user.getId());
        assertFalse(userConversations.isEmpty());
        assertTrue(userConversations.stream().anyMatch(c -> c.getId().equals(conversation.getId())));

        // 根据状态查找
        List<Conversation> activeConversations = conversationMapper.findByUserIdAndStatusOrderByLastActivityAtDesc(
                user.getId(), Conversation.ConversationStatus.ACTIVE.name());
        assertFalse(activeConversations.isEmpty());

        // 搜索对话标题
        List<Conversation> searchResults = conversationMapper.searchByTitle(user.getId(), "Test");
        assertFalse(searchResults.isEmpty());

        // 统计对话数
        long conversationCount = conversationMapper.countByUserId(user.getId());
        assertTrue(conversationCount > 0);

        long activeCount = conversationMapper.countActiveByUserId(user.getId());
        assertTrue(activeCount > 0);
    }

    @Test
    public void testVoiceNoteMapperBasicOperations() {
        // 先创建用户
        User user = createTestUser("voiceuser", "<EMAIL>");
        userMapper.insert(user);

        // 创建语音笔记
        VoiceNote voiceNote = new VoiceNote();
        voiceNote.setUser(user);
        voiceNote.setTitle("Test Voice Note");
        voiceNote.setDescription("Test Description");
        voiceNote.setCategory(VoiceNote.Category.GENERAL);
        voiceNote.setPriority(VoiceNote.Priority.MEDIUM);
        voiceNote.setIsFavorite(false);
        voiceNote.setIsArchived(false);
        voiceNote.setCreatedAt(LocalDateTime.now());
        voiceNote.setUpdatedAt(LocalDateTime.now());

        // 插入语音笔记
        int result = voiceNoteMapper.insert(voiceNote);
        assertEquals(1, result);
        assertNotNull(voiceNote.getId());

        // 根据ID和用户查找
        VoiceNote found = voiceNoteMapper.findByIdAndUserId(voiceNote.getId(), user.getId());
        assertNotNull(found);
        assertEquals("Test Voice Note", found.getTitle());

        // 查找用户的所有语音笔记
        List<VoiceNote> userVoiceNotes = voiceNoteMapper.findByUserIdOrderByCreatedAtDesc(user.getId());
        assertFalse(userVoiceNotes.isEmpty());

        // 根据分类查找
        List<VoiceNote> categoryNotes = voiceNoteMapper.findByUserIdAndCategoryOrderByCreatedAtDesc(
                user.getId(), VoiceNote.Category.GENERAL.name());
        assertFalse(categoryNotes.isEmpty());

        // 搜索语音笔记
        List<VoiceNote> searchResults = voiceNoteMapper.searchByKeyword(user.getId(), "Test");
        assertFalse(searchResults.isEmpty());

        // 统计语音笔记数
        long noteCount = voiceNoteMapper.countByUserId(user.getId());
        assertTrue(noteCount > 0);

        long categoryCount = voiceNoteMapper.countByUserIdAndCategory(user.getId(), VoiceNote.Category.GENERAL.name());
        assertTrue(categoryCount > 0);
    }

    @Test
    public void testUserServiceMybatisPlus() {
        // 测试用户注册
        User registeredUser = userService.registerUser("servicetest", "<EMAIL>", 
                                                       "password123", "Service Test User");
        assertNotNull(registeredUser);
        assertNotNull(registeredUser.getId());
        assertEquals("servicetest", registeredUser.getUsername());

        // 测试查找用户
        assertTrue(userService.findByUsername("servicetest").isPresent());
        assertTrue(userService.findByEmail("<EMAIL>").isPresent());

        // 测试用户名/邮箱存在性检查
        assertTrue(userService.existsByUsername("servicetest"));
        assertTrue(userService.existsByEmail("<EMAIL>"));

        // 测试更新最后登录时间
        userService.updateLastLoginTime(registeredUser.getId());

        // 测试统计功能
        long totalUsers = userService.countAllUsers();
        assertTrue(totalUsers > 0);

        long enabledUsers = userService.countEnabledUsers();
        assertTrue(enabledUsers > 0);
    }

    private User createTestUser(String username, String email) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword("password123");
        user.setFullName("Test User");
        user.setRole("USER");
        user.setEnabledFlag(true);
        user.setAccountNonExpiredFlag(true);
        user.setAccountNonLockedFlag(true);
        user.setCredentialsNonExpiredFlag(true);
        user.setCreatedAt(LocalDateTime.now());
        return user;
    }
}
