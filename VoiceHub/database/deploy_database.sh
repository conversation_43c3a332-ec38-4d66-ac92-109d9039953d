#!/bin/bash

# =====================================================
# VoiceHub 数据库部署脚本
# 用途: 自动化部署VoiceHub数据库架构
# 作者: VoiceHub Team
# 版本: 1.0.0
# =====================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_NAME="voicehub"
DB_USER="postgres"
DB_HOST="localhost"
DB_PORT="5432"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCHEMA_FILE="$SCRIPT_DIR/VoiceHub_Final_Schema.sql"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL客户端未安装，请先安装PostgreSQL"
        exit 1
    fi
    
    if ! command -v createdb &> /dev/null; then
        log_error "createdb命令未找到，请检查PostgreSQL安装"
        exit 1
    fi
    
    if [ ! -f "$SCHEMA_FILE" ]; then
        log_error "数据库架构文件未找到: $SCHEMA_FILE"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查数据库连接
check_connection() {
    log_info "检查数据库连接..."
    
    if ! psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT 1;" &> /dev/null; then
        log_error "无法连接到PostgreSQL服务器"
        log_error "请检查:"
        log_error "1. PostgreSQL服务是否运行"
        log_error "2. 连接参数是否正确"
        log_error "3. 用户权限是否足够"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 检查数据库是否存在
check_database_exists() {
    local db_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME';")
    
    if [ "$db_exists" = "1" ]; then
        return 0  # 数据库存在
    else
        return 1  # 数据库不存在
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库: $DB_NAME"
    
    if check_database_exists; then
        log_warning "数据库 $DB_NAME 已存在"
        read -p "是否要删除现有数据库并重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "删除现有数据库..."
            dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
            log_success "数据库已删除"
        else
            log_info "跳过数据库创建"
            return 0
        fi
    fi
    
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
    log_success "数据库创建成功"
}

# 执行数据库架构脚本
deploy_schema() {
    log_info "部署数据库架构..."
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$SCHEMA_FILE"; then
        log_success "数据库架构部署成功"
    else
        log_error "数据库架构部署失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查表数量
    local table_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';")
    log_info "创建的表数量: $table_count"
    
    if [ "$table_count" -ne 16 ]; then
        log_warning "预期表数量为16，实际为$table_count"
    fi
    
    # 检查索引数量
    local index_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT count(*) FROM pg_indexes WHERE schemaname = 'public';")
    log_info "创建的索引数量: $index_count"
    
    # 检查触发器数量
    local trigger_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT count(*) FROM information_schema.triggers WHERE trigger_schema = 'public';")
    log_info "创建的触发器数量: $trigger_count"
    
    # 检查系统设置
    local settings_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT count(*) FROM system_settings;")
    log_info "系统设置数量: $settings_count"
    
    # 检查视图
    local view_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -tAc "SELECT count(*) FROM information_schema.views WHERE table_schema = 'public';")
    log_info "创建的视图数量: $view_count"
    
    log_success "部署验证完成"
}

# 显示连接信息
show_connection_info() {
    log_info "数据库连接信息:"
    echo "  主机: $DB_HOST"
    echo "  端口: $DB_PORT"
    echo "  数据库: $DB_NAME"
    echo "  用户: $DB_USER"
    echo ""
    echo "连接命令:"
    echo "  psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
}

# 显示帮助信息
show_help() {
    echo "VoiceHub 数据库部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --user     数据库用户 (默认: postgres)"
    echo "  -H, --host     数据库主机 (默认: localhost)"
    echo "  -p, --port     数据库端口 (默认: 5432)"
    echo "  -d, --database 数据库名称 (默认: voicehub)"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -u myuser -H myhost -p 5433       # 自定义连接参数"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -H|--host)
                DB_HOST="$2"
                shift 2
                ;;
            -p|--port)
                DB_PORT="$2"
                shift 2
                ;;
            -d|--database)
                DB_NAME="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    echo "========================================"
    echo "    VoiceHub 数据库部署脚本 v1.0.0"
    echo "========================================"
    echo ""
    
    parse_args "$@"
    
    check_dependencies
    check_connection
    create_database
    deploy_schema
    verify_deployment
    
    echo ""
    echo "========================================"
    log_success "数据库部署完成!"
    echo "========================================"
    echo ""
    
    show_connection_info
}

# 执行主函数
main "$@"
