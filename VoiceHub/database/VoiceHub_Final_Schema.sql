-- =====================================================
-- VoiceHub 智能语音助手平台 - 最终数据库架构
-- PostgreSQL 14+ 版本
-- 创建时间: 2025年
-- 描述: 完整的生产就绪数据库架构，支持所有前端功能
-- =====================================================

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- 核心用户表
-- =====================================================

-- 用户主表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,                                    -- 用户唯一标识ID
    username VARCHAR(50) UNIQUE NOT NULL,                        -- 用户名，唯一
    email VARCHAR(100) UNIQUE NOT NULL,                          -- 邮箱地址，唯一
    password_hash VARCHAR(255) NOT NULL,                         -- 密码哈希值
    full_name VARCHAR(100),                                      -- 用户全名
    avatar_url VARCHAR(500),                                     -- 头像URL地址
    phone_number VARCHAR(20),                                    -- 手机号码
    preferred_language VARCHAR(10) DEFAULT 'zh-CN',             -- 首选语言，默认中文
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',               -- 时区设置，默认上海
    voice_settings JSONB DEFAULT '{}',                          -- 语音相关设置（JSON格式）
    notification_settings JSONB DEFAULT '{}',                   -- 通知设置（JSON格式）
    subscription_type VARCHAR(20) DEFAULT 'FREE',               -- 订阅类型：FREE/PREMIUM/ENTERPRISE
    subscription_expires_at TIMESTAMP,                          -- 订阅到期时间
    is_active BOOLEAN DEFAULT true,                             -- 账户是否激活
    is_verified BOOLEAN DEFAULT false,                          -- 邮箱是否已验证
    last_login_at TIMESTAMP,                                    -- 最后登录时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,             -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 用户详细资料表
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,                                    -- 资料记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    bio TEXT,                                                    -- 个人简介
    occupation VARCHAR(100),                                     -- 职业
    company VARCHAR(100),                                        -- 公司名称
    location VARCHAR(100),                                       -- 所在地区
    birth_date DATE,                                            -- 出生日期
    gender VARCHAR(10),                                         -- 性别
    interests TEXT[],                                           -- 兴趣爱好数组
    goals TEXT[],                                               -- 目标数组
    privacy_settings JSONB DEFAULT '{}',                       -- 隐私设置（JSON格式）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 用户设置表
CREATE TABLE user_settings (
    id BIGSERIAL PRIMARY KEY,                                    -- 设置记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    ai_preferences JSONB DEFAULT '{}',                          -- AI模型偏好设置（JSON格式）
    speech_settings JSONB DEFAULT '{}',                         -- 语音识别设置（JSON格式）
    notification_settings JSONB DEFAULT '{}',                   -- 通知设置（JSON格式）
    ui_settings JSONB DEFAULT '{}',                             -- 界面设置（JSON格式）
    privacy_settings JSONB DEFAULT '{}',                        -- 隐私设置（JSON格式）
    extended_settings JSONB DEFAULT '{}',                       -- 扩展设置（JSON格式）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,             -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0,                                  -- 逻辑删除标记（0：未删除，1：已删除）
    UNIQUE(user_id)                                             -- 每个用户只能有一条设置记录
);

-- =====================================================
-- 对话和消息表
-- =====================================================

-- 对话会话表
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,                                    -- 对话会话唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    title VARCHAR(200) NOT NULL,                                -- 对话标题
    type VARCHAR(50) DEFAULT 'GENERAL',                         -- 对话类型：GENERAL/VOICE_ASSISTANT/SCHEDULE_PLANNING/EMOTIONAL_SUPPORT
    status VARCHAR(20) DEFAULT 'ACTIVE',                        -- 对话状态：ACTIVE/ARCHIVED/DELETED
    mood_score DECIMAL(3,2),                                    -- 情绪评分（0.00-10.00）
    emotion_analysis JSONB DEFAULT '{}',                        -- 情绪分析结果（JSON格式）
    context_data JSONB DEFAULT '{}',                            -- 上下文数据（JSON格式）
    message_count INTEGER DEFAULT 0,                            -- 消息总数
    total_tokens INTEGER DEFAULT 0,                             -- 消耗的总token数
    ai_model VARCHAR(50) DEFAULT 'gpt-3.5-turbo',              -- 使用的AI模型
    model_settings JSONB DEFAULT '{}',                          -- 模型参数设置（JSON格式）
    total_cost DECIMAL(10,6) DEFAULT 0,                        -- 对话总成本（美元）
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,       -- 最后活动时间
    is_favorite BOOLEAN DEFAULT false,                          -- 是否收藏
    is_archived BOOLEAN DEFAULT false,                          -- 是否归档
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,             -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,                                    -- 消息唯一标识ID
    conversation_id BIGINT NOT NULL REFERENCES conversations(id) ON DELETE CASCADE, -- 关联对话ID，级联删除
    role VARCHAR(20) NOT NULL,                                   -- 消息角色：USER/ASSISTANT/SYSTEM
    content TEXT NOT NULL,                                       -- 消息内容
    message_type VARCHAR(20) DEFAULT 'TEXT',                     -- 消息类型：TEXT/VOICE/SYSTEM/ERROR
    voice_file_path VARCHAR(500),                               -- 语音文件路径（如果是语音消息）
    transcription_confidence DECIMAL(5,4),                      -- 语音转文字置信度（0.0000-1.0000）
    emotion VARCHAR(50),                                        -- 检测到的情绪
    emotion_confidence DECIMAL(5,4),                            -- 情绪检测置信度（0.0000-1.0000）
    response_time_ms INTEGER,                                   -- AI响应时间（毫秒）
    token_count INTEGER,                                        -- 消息token数量
    ai_model VARCHAR(50),                                       -- 生成消息的AI模型
    cost_tokens INTEGER,                                        -- 消耗的token数量
    cost_usd DECIMAL(10,6),                                     -- 消息成本（美元）
    metadata JSONB DEFAULT '{}',                                -- 元数据（JSON格式）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- 语音功能表
-- =====================================================

-- 语音笔记表
CREATE TABLE voice_notes (
    id BIGSERIAL PRIMARY KEY,                                    -- 语音笔记唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    title VARCHAR(200) NOT NULL,                                -- 笔记标题
    transcription TEXT,                                         -- 语音转文字结果
    file_path VARCHAR(500) NOT NULL,                            -- 音频文件路径
    file_size BIGINT,                                          -- 文件大小（字节）
    duration_seconds INTEGER,                                   -- 录音时长（秒）
    audio_format VARCHAR(20),                                   -- 音频格式：mp3/wav/m4a/ogg
    sample_rate INTEGER,                                        -- 采样率（Hz）
    bit_rate INTEGER,                                          -- 比特率（kbps）
    category VARCHAR(50) DEFAULT 'GENERAL',                    -- 笔记分类：GENERAL/MEETING/PERSONAL/STUDY/OTHER
    tags TEXT[],                                               -- 标签数组
    transcription_confidence DECIMAL(5,4),                     -- 转写置信度（0.0000-1.0000）
    language VARCHAR(10) DEFAULT 'zh-CN',                      -- 语言代码
    ai_summary TEXT,                                           -- AI生成的摘要
    ai_keywords TEXT[],                                        -- AI提取的关键词数组
    ai_category VARCHAR(50),                                   -- AI自动分类结果
    ai_processing_status VARCHAR(20) DEFAULT 'pending',        -- AI处理状态：pending/processing/completed/error
    is_favorite BOOLEAN DEFAULT false,                         -- 是否收藏
    is_archived BOOLEAN DEFAULT false,                         -- 是否归档
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 音频文件管理表
CREATE TABLE audio_files (
    id BIGSERIAL PRIMARY KEY,                                    -- 音频文件唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    file_name VARCHAR(255) NOT NULL,                            -- 存储文件名
    original_name VARCHAR(255),                                 -- 原始文件名
    file_path VARCHAR(500) NOT NULL,                            -- 文件存储路径
    file_size BIGINT NOT NULL,                                  -- 文件大小（字节）
    mime_type VARCHAR(100),                                     -- MIME类型
    duration_seconds INTEGER,                                   -- 音频时长（秒）
    audio_format VARCHAR(20),                                   -- 音频格式
    sample_rate INTEGER,                                        -- 采样率（Hz）
    bit_rate INTEGER,                                          -- 比特率（kbps）
    channels INTEGER DEFAULT 1,                                -- 声道数，默认单声道
    file_hash VARCHAR(64),                                      -- SHA-256文件哈希值，用于去重
    upload_source VARCHAR(50),                                  -- 上传来源：VOICE_NOTE/CONVERSATION/UPLOAD
    processing_status VARCHAR(20) DEFAULT 'PENDING',           -- 处理状态：PENDING/PROCESSING/COMPLETED/FAILED
    transcription_status VARCHAR(20) DEFAULT 'PENDING',        -- 转写状态：PENDING/PROCESSING/COMPLETED/FAILED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- 日程管理表
-- =====================================================

-- 日程安排表
CREATE TABLE schedules (
    id BIGSERIAL PRIMARY KEY,                                    -- 日程唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    title VARCHAR(200) NOT NULL,                                -- 日程标题
    description TEXT,                                           -- 日程描述
    start_time TIMESTAMP NOT NULL,                              -- 开始时间
    end_time TIMESTAMP,                                         -- 结束时间
    timezone VARCHAR(50),                                       -- 时区
    location VARCHAR(200),                                      -- 地点
    attendees TEXT[],                                          -- 参与者数组
    priority VARCHAR(20) DEFAULT 'MEDIUM',                     -- 优先级：LOW/MEDIUM/HIGH/URGENT
    status VARCHAR(20) DEFAULT 'SCHEDULED',                    -- 状态：SCHEDULED/COMPLETED/CANCELLED/IN_PROGRESS
    reminder_settings JSONB DEFAULT '{}',                      -- 提醒设置（JSON格式）
    recurrence_rule VARCHAR(200),                              -- 重复规则（RRULE格式）
    voice_created BOOLEAN DEFAULT false,                       -- 是否通过语音创建
    voice_command TEXT,                                         -- 原始语音指令
    nlp_confidence DECIMAL(5,4),                               -- 自然语言处理置信度（0.0000-1.0000）
    calendar_sync_id VARCHAR(100),                             -- 外部日历同步ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- AI功能表
-- =====================================================

-- AI任务处理表
CREATE TABLE ai_tasks (
    id BIGSERIAL PRIMARY KEY,                                    -- AI任务唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    task_type VARCHAR(50) NOT NULL,                             -- 任务类型：summary/translate/format/analyze/creative
    input_content TEXT NOT NULL,                                -- 输入内容
    output_content TEXT,                                        -- 输出结果
    status VARCHAR(20) DEFAULT 'pending',                       -- 任务状态：pending/processing/completed/error
    ai_model VARCHAR(50) NOT NULL,                              -- 使用的AI模型：gpt-4/gpt-3.5-turbo/qwen-turbo等
    processing_time_ms INTEGER,                                 -- 处理时间（毫秒）
    tokens_used INTEGER,                                        -- 消耗的token数量
    cost_estimate DECIMAL(10,6),                                -- 预估成本（美元）
    error_message TEXT,                                         -- 错误信息
    metadata JSONB DEFAULT '{}',                                -- 元数据（JSON格式）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- API使用追踪表
CREATE TABLE api_usage_logs (
    id BIGSERIAL PRIMARY KEY,                                    -- API调用记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    api_provider VARCHAR(50) NOT NULL,                          -- API提供商：openai/qianwen/zhipu/baidu
    api_endpoint VARCHAR(100),                                  -- API端点URL
    model_name VARCHAR(50),                                     -- 模型名称
    request_type VARCHAR(50),                                   -- 请求类型：chat/speech_recognition/text_processing
    tokens_input INTEGER,                                       -- 输入token数量
    tokens_output INTEGER,                                      -- 输出token数量
    cost_usd DECIMAL(10,6),                                     -- 调用成本（美元）
    response_time_ms INTEGER,                                   -- 响应时间（毫秒）
    status_code INTEGER,                                        -- HTTP状态码
    error_message TEXT,                                         -- 错误信息
    request_id VARCHAR(100),                                    -- 请求唯一标识
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- 分析和统计表
-- =====================================================

-- 语音分析表
CREATE TABLE voice_analytics (
    id BIGSERIAL PRIMARY KEY,                                    -- 语音分析记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    session_id UUID DEFAULT uuid_generate_v4(),                 -- 会话唯一标识
    recording_type VARCHAR(50),                                 -- 录音类型：NOTE/CONVERSATION/SCHEDULE
    recording_id BIGINT,                                        -- 关联录音记录ID
    duration_seconds INTEGER NOT NULL,                          -- 录音时长（秒）
    audio_quality_score DECIMAL(4,2),                          -- 音频质量评分（0.00-10.00）
    speech_rate_wpm INTEGER,                                    -- 语速（每分钟词数）
    pause_frequency DECIMAL(5,2),                              -- 停顿频率
    volume_consistency DECIMAL(5,2),                           -- 音量一致性
    clarity_score DECIMAL(4,2),                                -- 清晰度评分（0.00-10.00）
    pitch_analysis JSONB DEFAULT '{}',                         -- 音调分析结果（JSON格式）
    frequency_analysis JSONB DEFAULT '{}',                     -- 频率分析结果（JSON格式）
    noise_level DECIMAL(5,2),                                  -- 噪音水平
    voice_activity_ratio DECIMAL(5,4),                         -- 语音活动比例（0.0000-1.0000）
    emotion_detected VARCHAR(50),                              -- 检测到的情绪
    emotion_confidence DECIMAL(5,4),                           -- 情绪检测置信度（0.0000-1.0000）
    language_detected VARCHAR(10),                             -- 检测到的语言
    language_confidence DECIMAL(5,4),                          -- 语言检测置信度（0.0000-1.0000）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 情绪追踪表
CREATE TABLE mood_entries (
    id BIGSERIAL PRIMARY KEY,                                    -- 情绪记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    mood VARCHAR(50) NOT NULL,                                  -- 情绪类型：HAPPY/SAD/ANGRY/SURPRISED/NEUTRAL/EXCITED
    intensity INTEGER NOT NULL CHECK (intensity >= 1 AND intensity <= 10), -- 情绪强度（1-10级）
    note TEXT,                                                  -- 情绪备注
    triggers TEXT[],                                           -- 情绪触发因素数组
    context VARCHAR(100),                                      -- 记录上下文：VOICE_SESSION/MANUAL/CONVERSATION
    related_session_id UUID,                                   -- 关联会话ID
    location VARCHAR(100),                                     -- 地理位置
    weather VARCHAR(50),                                       -- 天气情况
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 用户会话表
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,                                    -- 会话记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    session_id UUID DEFAULT uuid_generate_v4(),                 -- 会话唯一标识
    session_type VARCHAR(50),                                   -- 会话类型：VOICE_RECORDING/CONVERSATION/SCHEDULE_MANAGEMENT
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,             -- 会话开始时间
    end_time TIMESTAMP,                                         -- 会话结束时间
    duration_seconds INTEGER,                                   -- 会话时长（秒）
    activities_count INTEGER DEFAULT 0,                        -- 活动次数
    device_info JSONB DEFAULT '{}',                            -- 设备信息（JSON格式）
    ip_address INET,                                           -- IP地址
    user_agent TEXT,                                           -- 用户代理字符串
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 搜索历史表
CREATE TABLE search_history (
    id BIGSERIAL PRIMARY KEY,                                    -- 搜索记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    search_query VARCHAR(500) NOT NULL,                         -- 搜索查询内容
    search_type VARCHAR(50),                                    -- 搜索类型：voice_notes/conversations/schedules/global
    results_count INTEGER DEFAULT 0,                           -- 搜索结果数量
    clicked_result_id BIGINT,                                  -- 点击的结果ID
    clicked_result_type VARCHAR(50),                           -- 点击的结果类型
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- 系统功能表
-- =====================================================

-- 通知表
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,                                    -- 通知唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    type VARCHAR(50) NOT NULL,                                  -- 通知类型：REMINDER/SYSTEM/ACHIEVEMENT/MOOD_CHECK
    title VARCHAR(200) NOT NULL,                               -- 通知标题
    message TEXT NOT NULL,                                      -- 通知内容
    data JSONB DEFAULT '{}',                                   -- 通知数据（JSON格式）
    is_read BOOLEAN DEFAULT false,                             -- 是否已读
    priority VARCHAR(20) DEFAULT 'NORMAL',                    -- 优先级：LOW/NORMAL/HIGH/URGENT
    scheduled_for TIMESTAMP,                                   -- 计划发送时间
    expires_at TIMESTAMP,                                      -- 过期时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 用户成就表
CREATE TABLE user_achievements (
    id BIGSERIAL PRIMARY KEY,                                    -- 成就记录唯一标识ID
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 关联用户ID，级联删除
    achievement_type VARCHAR(50) NOT NULL,                      -- 成就类型：FIRST_RECORDING/STREAK_7_DAYS/QUALITY_IMPROVEMENT
    achievement_name VARCHAR(100) NOT NULL,                     -- 成就名称
    description TEXT,                                           -- 成就描述
    points INTEGER DEFAULT 0,                                   -- 获得积分
    badge_icon VARCHAR(100),                                    -- 徽章图标
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,           -- 解锁时间
    data JSONB DEFAULT '{}',                                    -- 成就相关数据（JSON格式）
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- 系统设置表
CREATE TABLE system_settings (
    id BIGSERIAL PRIMARY KEY,                                    -- 设置记录唯一标识ID
    setting_key VARCHAR(100) UNIQUE NOT NULL,                   -- 设置键名，唯一
    setting_value TEXT,                                         -- 设置值
    setting_type VARCHAR(20) DEFAULT 'STRING',                 -- 设置类型：STRING/INTEGER/BOOLEAN/JSON
    description TEXT,                                           -- 设置描述
    is_public BOOLEAN DEFAULT false,                           -- 是否公开设置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    deleted INTEGER DEFAULT 0                                   -- 逻辑删除标记（0：未删除，1：已删除）
);

-- =====================================================
-- 索引创建
-- =====================================================

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- 用户设置表索引
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_settings_updated_at ON user_settings(updated_at);

-- 对话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_last_activity ON conversations(last_activity_at);
CREATE INDEX idx_conversations_favorite ON conversations(is_favorite);
CREATE INDEX idx_conversations_ai_model ON conversations(ai_model);

-- 消息表索引
CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_type ON chat_messages(message_type);
CREATE INDEX idx_chat_messages_ai_model ON chat_messages(ai_model);

-- 语音笔记索引
CREATE INDEX idx_voice_notes_user_id ON voice_notes(user_id);
CREATE INDEX idx_voice_notes_category ON voice_notes(category);
CREATE INDEX idx_voice_notes_created_at ON voice_notes(created_at);
CREATE INDEX idx_voice_notes_tags ON voice_notes USING GIN(tags);
CREATE INDEX idx_voice_notes_favorite ON voice_notes(is_favorite);
CREATE INDEX idx_voice_notes_ai_processing_status ON voice_notes(ai_processing_status);
CREATE INDEX idx_voice_notes_ai_keywords ON voice_notes USING GIN(ai_keywords);

-- 日程表索引
CREATE INDEX idx_schedules_user_id ON schedules(user_id);
CREATE INDEX idx_schedules_start_time ON schedules(start_time);
CREATE INDEX idx_schedules_status ON schedules(status);
CREATE INDEX idx_schedules_created_at ON schedules(created_at);
CREATE INDEX idx_schedules_priority ON schedules(priority);

-- AI任务索引
CREATE INDEX idx_ai_tasks_user_id ON ai_tasks(user_id);
CREATE INDEX idx_ai_tasks_task_type ON ai_tasks(task_type);
CREATE INDEX idx_ai_tasks_status ON ai_tasks(status);
CREATE INDEX idx_ai_tasks_created_at ON ai_tasks(created_at);
CREATE INDEX idx_ai_tasks_ai_model ON ai_tasks(ai_model);

-- API使用日志索引
CREATE INDEX idx_api_usage_logs_user_id ON api_usage_logs(user_id);
CREATE INDEX idx_api_usage_logs_api_provider ON api_usage_logs(api_provider);
CREATE INDEX idx_api_usage_logs_created_at ON api_usage_logs(created_at);
CREATE INDEX idx_api_usage_logs_request_type ON api_usage_logs(request_type);

-- 语音分析索引
CREATE INDEX idx_voice_analytics_user_id ON voice_analytics(user_id);
CREATE INDEX idx_voice_analytics_session_id ON voice_analytics(session_id);
CREATE INDEX idx_voice_analytics_created_at ON voice_analytics(created_at);
CREATE INDEX idx_voice_analytics_recording_type ON voice_analytics(recording_type);

-- 情绪追踪索引
CREATE INDEX idx_mood_entries_user_id ON mood_entries(user_id);
CREATE INDEX idx_mood_entries_mood ON mood_entries(mood);
CREATE INDEX idx_mood_entries_created_at ON mood_entries(created_at);

-- 用户会话索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_created_at ON user_sessions(created_at);
CREATE INDEX idx_user_sessions_type ON user_sessions(session_type);

-- 音频文件索引
CREATE INDEX idx_audio_files_user_id ON audio_files(user_id);
CREATE INDEX idx_audio_files_file_hash ON audio_files(file_hash);
CREATE INDEX idx_audio_files_processing_status ON audio_files(processing_status);
CREATE INDEX idx_audio_files_created_at ON audio_files(created_at);

-- 搜索历史索引
CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_search_history_search_type ON search_history(search_type);
CREATE INDEX idx_search_history_created_at ON search_history(created_at);

-- 通知索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- 全文搜索索引
CREATE INDEX idx_voice_notes_transcription_fts ON voice_notes USING GIN(to_tsvector('chinese', transcription));
CREATE INDEX idx_voice_notes_ai_summary_fts ON voice_notes USING GIN(to_tsvector('chinese', ai_summary));
CREATE INDEX idx_ai_tasks_input_fts ON ai_tasks USING GIN(to_tsvector('chinese', input_content));
CREATE INDEX idx_ai_tasks_output_fts ON ai_tasks USING GIN(to_tsvector('chinese', output_content));

-- =====================================================
-- 触发器创建
-- =====================================================

-- 自动更新 updated_at 字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_voice_notes_updated_at BEFORE UPDATE ON voice_notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schedules_updated_at BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audio_files_updated_at BEFORE UPDATE ON audio_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_tasks_updated_at BEFORE UPDATE ON ai_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 初始系统设置数据
-- =====================================================

-- 插入默认系统设置
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_version', '1.0.0', 'STRING', 'Current application version', true),
('maintenance_mode', 'false', 'BOOLEAN', 'Enable maintenance mode', false),
('max_file_size_mb', '50', 'INTEGER', 'Maximum file upload size in MB', true),
('supported_audio_formats', '["mp3", "wav", "m4a", "ogg"]', 'JSON', 'Supported audio file formats', true),
('default_language', 'zh-CN', 'STRING', 'Default system language', true),
('voice_quality_threshold', '0.8', 'STRING', 'Minimum voice quality threshold', false),
('max_conversation_history', '1000', 'INTEGER', 'Maximum conversation history per user', false),
('session_timeout_minutes', '30', 'INTEGER', 'User session timeout in minutes', false),
('ai_default_model', 'gpt-3.5-turbo', 'STRING', 'Default AI model for conversations', true),
('ai_max_tokens', '4000', 'INTEGER', 'Maximum tokens per AI request', false),
('ai_temperature', '0.7', 'STRING', 'Default AI temperature setting', false),
('cost_tracking_enabled', 'true', 'BOOLEAN', 'Enable API cost tracking', false),
('max_daily_api_calls', '1000', 'INTEGER', 'Maximum API calls per user per day', false),
('auto_ai_processing', 'true', 'BOOLEAN', 'Enable automatic AI processing for voice notes', true);

-- =====================================================
-- 视图创建
-- =====================================================

-- 用户统计视图
CREATE VIEW user_stats AS
SELECT
    u.id,
    u.username,
    u.email,
    u.full_name,
    u.created_at as user_since,
    u.last_login_at,
    COUNT(DISTINCT vn.id) as total_voice_notes,
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT s.id) as total_schedules,
    AVG(va.audio_quality_score) as avg_voice_quality,
    COUNT(DISTINCT us.id) as total_sessions,
    SUM(us.duration_seconds) as total_session_time,
    COUNT(DISTINCT ua.id) as total_achievements
FROM users u
LEFT JOIN voice_notes vn ON u.id = vn.user_id AND vn.is_archived = false
LEFT JOIN conversations c ON u.id = c.user_id AND c.is_archived = false
LEFT JOIN schedules s ON u.id = s.user_id
LEFT JOIN voice_analytics va ON u.id = va.user_id
LEFT JOIN user_sessions us ON u.id = us.user_id
LEFT JOIN user_achievements ua ON u.id = ua.user_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.email, u.full_name, u.created_at, u.last_login_at;

-- 最近活动视图
CREATE VIEW recent_activity AS
SELECT
    'voice_note' as activity_type,
    vn.id as activity_id,
    vn.user_id,
    vn.title as activity_title,
    vn.created_at as activity_time
FROM voice_notes vn
WHERE vn.created_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT
    'conversation' as activity_type,
    c.id as activity_id,
    c.user_id,
    c.title as activity_title,
    c.last_activity_at as activity_time
FROM conversations c
WHERE c.last_activity_at >= CURRENT_DATE - INTERVAL '7 days'

UNION ALL

SELECT
    'schedule' as activity_type,
    s.id as activity_id,
    s.user_id,
    s.title as activity_title,
    s.created_at as activity_time
FROM schedules s
WHERE s.created_at >= CURRENT_DATE - INTERVAL '7 days'

ORDER BY activity_time DESC;

-- =====================================================
-- 表注释
-- =====================================================

-- =====================================================
-- 表和字段中文注释
-- =====================================================

-- 用户主表注释
COMMENT ON TABLE users IS '用户主表 - 存储用户基本信息和认证数据';
COMMENT ON COLUMN users.id IS '用户唯一标识ID';
COMMENT ON COLUMN users.username IS '用户名，唯一';
COMMENT ON COLUMN users.email IS '邮箱地址，唯一';
COMMENT ON COLUMN users.password_hash IS '密码哈希值';
COMMENT ON COLUMN users.full_name IS '用户全名';
COMMENT ON COLUMN users.avatar_url IS '头像URL地址';
COMMENT ON COLUMN users.phone_number IS '手机号码';
COMMENT ON COLUMN users.preferred_language IS '首选语言，默认中文';
COMMENT ON COLUMN users.timezone IS '时区设置，默认上海';
COMMENT ON COLUMN users.voice_settings IS '语音相关设置（JSON格式）';
COMMENT ON COLUMN users.notification_settings IS '通知设置（JSON格式）';
COMMENT ON COLUMN users.subscription_type IS '订阅类型：FREE/PREMIUM/ENTERPRISE';
COMMENT ON COLUMN users.subscription_expires_at IS '订阅到期时间';
COMMENT ON COLUMN users.is_active IS '账户是否激活';
COMMENT ON COLUMN users.is_verified IS '邮箱是否已验证';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';
COMMENT ON COLUMN users.deleted IS '逻辑删除标记（0：未删除，1：已删除）';

-- 用户详细资料表注释
COMMENT ON TABLE user_profiles IS '用户详细资料表 - 存储扩展的用户信息';
COMMENT ON COLUMN user_profiles.id IS '资料记录唯一标识ID';
COMMENT ON COLUMN user_profiles.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN user_profiles.bio IS '个人简介';
COMMENT ON COLUMN user_profiles.occupation IS '职业';
COMMENT ON COLUMN user_profiles.company IS '公司名称';
COMMENT ON COLUMN user_profiles.location IS '所在地区';
COMMENT ON COLUMN user_profiles.birth_date IS '出生日期';
COMMENT ON COLUMN user_profiles.gender IS '性别';
COMMENT ON COLUMN user_profiles.interests IS '兴趣爱好数组';
COMMENT ON COLUMN user_profiles.goals IS '目标数组';
COMMENT ON COLUMN user_profiles.privacy_settings IS '隐私设置（JSON格式）';
COMMENT ON COLUMN user_profiles.created_at IS '创建时间';
COMMENT ON COLUMN user_profiles.updated_at IS '更新时间';

-- 对话会话表注释
COMMENT ON TABLE conversations IS 'AI对话会话表 - 存储用户与AI的对话会话';
COMMENT ON COLUMN conversations.id IS '对话会话唯一标识ID';
COMMENT ON COLUMN conversations.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN conversations.title IS '对话标题';
COMMENT ON COLUMN conversations.type IS '对话类型：GENERAL/VOICE_ASSISTANT/SCHEDULE_PLANNING/EMOTIONAL_SUPPORT';
COMMENT ON COLUMN conversations.status IS '对话状态：ACTIVE/ARCHIVED/DELETED';
COMMENT ON COLUMN conversations.mood_score IS '情绪评分（0.00-10.00）';
COMMENT ON COLUMN conversations.emotion_analysis IS '情绪分析结果（JSON格式）';
COMMENT ON COLUMN conversations.context_data IS '上下文数据（JSON格式）';
COMMENT ON COLUMN conversations.message_count IS '消息总数';
COMMENT ON COLUMN conversations.total_tokens IS '消耗的总token数';
COMMENT ON COLUMN conversations.ai_model IS '使用的AI模型';
COMMENT ON COLUMN conversations.model_settings IS '模型参数设置（JSON格式）';
COMMENT ON COLUMN conversations.total_cost IS '对话总成本（美元）';
COMMENT ON COLUMN conversations.last_activity_at IS '最后活动时间';
COMMENT ON COLUMN conversations.is_favorite IS '是否收藏';
COMMENT ON COLUMN conversations.is_archived IS '是否归档';
COMMENT ON COLUMN conversations.created_at IS '创建时间';
COMMENT ON COLUMN conversations.updated_at IS '更新时间';

-- 聊天消息表注释
COMMENT ON TABLE chat_messages IS '聊天消息表 - 存储对话中的具体消息';
COMMENT ON COLUMN chat_messages.id IS '消息唯一标识ID';
COMMENT ON COLUMN chat_messages.conversation_id IS '关联对话ID，级联删除';
COMMENT ON COLUMN chat_messages.role IS '消息角色：USER/ASSISTANT/SYSTEM';
COMMENT ON COLUMN chat_messages.content IS '消息内容';
COMMENT ON COLUMN chat_messages.message_type IS '消息类型：TEXT/VOICE/SYSTEM/ERROR';
COMMENT ON COLUMN chat_messages.voice_file_path IS '语音文件路径（如果是语音消息）';
COMMENT ON COLUMN chat_messages.transcription_confidence IS '语音转文字置信度（0.0000-1.0000）';
COMMENT ON COLUMN chat_messages.emotion IS '检测到的情绪';
COMMENT ON COLUMN chat_messages.emotion_confidence IS '情绪检测置信度（0.0000-1.0000）';
COMMENT ON COLUMN chat_messages.response_time_ms IS 'AI响应时间（毫秒）';
COMMENT ON COLUMN chat_messages.token_count IS '消息token数量';
COMMENT ON COLUMN chat_messages.ai_model IS '生成消息的AI模型';
COMMENT ON COLUMN chat_messages.cost_tokens IS '消耗的token数量';
COMMENT ON COLUMN chat_messages.cost_usd IS '消息成本（美元）';
COMMENT ON COLUMN chat_messages.metadata IS '元数据（JSON格式）';
COMMENT ON COLUMN chat_messages.created_at IS '创建时间';

-- 语音笔记表注释
COMMENT ON TABLE voice_notes IS '语音笔记表 - 存储用户的语音录音和转写';
COMMENT ON COLUMN voice_notes.id IS '语音笔记唯一标识ID';
COMMENT ON COLUMN voice_notes.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN voice_notes.title IS '笔记标题';
COMMENT ON COLUMN voice_notes.transcription IS '语音转文字结果';
COMMENT ON COLUMN voice_notes.file_path IS '音频文件路径';
COMMENT ON COLUMN voice_notes.file_size IS '文件大小（字节）';
COMMENT ON COLUMN voice_notes.duration_seconds IS '录音时长（秒）';
COMMENT ON COLUMN voice_notes.audio_format IS '音频格式：mp3/wav/m4a/ogg';
COMMENT ON COLUMN voice_notes.sample_rate IS '采样率（Hz）';
COMMENT ON COLUMN voice_notes.bit_rate IS '比特率（kbps）';
COMMENT ON COLUMN voice_notes.category IS '笔记分类：GENERAL/MEETING/PERSONAL/STUDY/OTHER';
COMMENT ON COLUMN voice_notes.tags IS '标签数组';
COMMENT ON COLUMN voice_notes.transcription_confidence IS '转写置信度（0.0000-1.0000）';
COMMENT ON COLUMN voice_notes.language IS '语言代码';
COMMENT ON COLUMN voice_notes.ai_summary IS 'AI生成的摘要';
COMMENT ON COLUMN voice_notes.ai_keywords IS 'AI提取的关键词数组';
COMMENT ON COLUMN voice_notes.ai_category IS 'AI自动分类结果';
COMMENT ON COLUMN voice_notes.ai_processing_status IS 'AI处理状态：pending/processing/completed/error';
COMMENT ON COLUMN voice_notes.is_favorite IS '是否收藏';
COMMENT ON COLUMN voice_notes.is_archived IS '是否归档';
COMMENT ON COLUMN voice_notes.created_at IS '创建时间';
COMMENT ON COLUMN voice_notes.updated_at IS '更新时间';

-- 音频文件管理表注释
COMMENT ON TABLE audio_files IS '音频文件表 - 集中管理音频文件';
COMMENT ON COLUMN audio_files.id IS '音频文件唯一标识ID';
COMMENT ON COLUMN audio_files.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN audio_files.file_name IS '存储文件名';
COMMENT ON COLUMN audio_files.original_name IS '原始文件名';
COMMENT ON COLUMN audio_files.file_path IS '文件存储路径';
COMMENT ON COLUMN audio_files.file_size IS '文件大小（字节）';
COMMENT ON COLUMN audio_files.mime_type IS 'MIME类型';
COMMENT ON COLUMN audio_files.duration_seconds IS '音频时长（秒）';
COMMENT ON COLUMN audio_files.audio_format IS '音频格式';
COMMENT ON COLUMN audio_files.sample_rate IS '采样率（Hz）';
COMMENT ON COLUMN audio_files.bit_rate IS '比特率（kbps）';
COMMENT ON COLUMN audio_files.channels IS '声道数，默认单声道';
COMMENT ON COLUMN audio_files.file_hash IS 'SHA-256文件哈希值，用于去重';
COMMENT ON COLUMN audio_files.upload_source IS '上传来源：VOICE_NOTE/CONVERSATION/UPLOAD';
COMMENT ON COLUMN audio_files.processing_status IS '处理状态：PENDING/PROCESSING/COMPLETED/FAILED';
COMMENT ON COLUMN audio_files.transcription_status IS '转写状态：PENDING/PROCESSING/COMPLETED/FAILED';
COMMENT ON COLUMN audio_files.created_at IS '创建时间';
COMMENT ON COLUMN audio_files.updated_at IS '更新时间';

-- 日程安排表注释
COMMENT ON TABLE schedules IS '日程安排表 - 存储用户的日历事件和约会';
COMMENT ON COLUMN schedules.id IS '日程唯一标识ID';
COMMENT ON COLUMN schedules.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN schedules.title IS '日程标题';
COMMENT ON COLUMN schedules.description IS '日程描述';
COMMENT ON COLUMN schedules.start_time IS '开始时间';
COMMENT ON COLUMN schedules.end_time IS '结束时间';
COMMENT ON COLUMN schedules.timezone IS '时区';
COMMENT ON COLUMN schedules.location IS '地点';
COMMENT ON COLUMN schedules.attendees IS '参与者数组';
COMMENT ON COLUMN schedules.priority IS '优先级：LOW/MEDIUM/HIGH/URGENT';
COMMENT ON COLUMN schedules.status IS '状态：SCHEDULED/COMPLETED/CANCELLED/IN_PROGRESS';
COMMENT ON COLUMN schedules.reminder_settings IS '提醒设置（JSON格式）';
COMMENT ON COLUMN schedules.recurrence_rule IS '重复规则（RRULE格式）';
COMMENT ON COLUMN schedules.voice_created IS '是否通过语音创建';
COMMENT ON COLUMN schedules.voice_command IS '原始语音指令';
COMMENT ON COLUMN schedules.nlp_confidence IS '自然语言处理置信度（0.0000-1.0000）';
COMMENT ON COLUMN schedules.calendar_sync_id IS '外部日历同步ID';
COMMENT ON COLUMN schedules.created_at IS '创建时间';
COMMENT ON COLUMN schedules.updated_at IS '更新时间';

-- AI任务处理表注释
COMMENT ON TABLE ai_tasks IS 'AI任务处理表 - 存储AI任务处理记录和结果';
COMMENT ON COLUMN ai_tasks.id IS 'AI任务唯一标识ID';
COMMENT ON COLUMN ai_tasks.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN ai_tasks.task_type IS '任务类型：summary/translate/format/analyze/creative';
COMMENT ON COLUMN ai_tasks.input_content IS '输入内容';
COMMENT ON COLUMN ai_tasks.output_content IS '输出结果';
COMMENT ON COLUMN ai_tasks.status IS '任务状态：pending/processing/completed/error';
COMMENT ON COLUMN ai_tasks.ai_model IS '使用的AI模型：gpt-4/gpt-3.5-turbo/qwen-turbo等';
COMMENT ON COLUMN ai_tasks.processing_time_ms IS '处理时间（毫秒）';
COMMENT ON COLUMN ai_tasks.tokens_used IS '消耗的token数量';
COMMENT ON COLUMN ai_tasks.cost_estimate IS '预估成本（美元）';
COMMENT ON COLUMN ai_tasks.error_message IS '错误信息';
COMMENT ON COLUMN ai_tasks.metadata IS '元数据（JSON格式）';
COMMENT ON COLUMN ai_tasks.created_at IS '创建时间';
COMMENT ON COLUMN ai_tasks.updated_at IS '更新时间';

-- API使用追踪表注释
COMMENT ON TABLE api_usage_logs IS 'API使用日志表 - 追踪API调用和成本';
COMMENT ON COLUMN api_usage_logs.id IS 'API调用记录唯一标识ID';
COMMENT ON COLUMN api_usage_logs.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN api_usage_logs.api_provider IS 'API提供商：openai/qianwen/zhipu/baidu';
COMMENT ON COLUMN api_usage_logs.api_endpoint IS 'API端点URL';
COMMENT ON COLUMN api_usage_logs.model_name IS '模型名称';
COMMENT ON COLUMN api_usage_logs.request_type IS '请求类型：chat/speech_recognition/text_processing';
COMMENT ON COLUMN api_usage_logs.tokens_input IS '输入token数量';
COMMENT ON COLUMN api_usage_logs.tokens_output IS '输出token数量';
COMMENT ON COLUMN api_usage_logs.cost_usd IS '调用成本（美元）';
COMMENT ON COLUMN api_usage_logs.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN api_usage_logs.status_code IS 'HTTP状态码';
COMMENT ON COLUMN api_usage_logs.error_message IS '错误信息';
COMMENT ON COLUMN api_usage_logs.request_id IS '请求唯一标识';
COMMENT ON COLUMN api_usage_logs.created_at IS '创建时间';

-- 语音分析表注释
COMMENT ON TABLE voice_analytics IS '语音分析表 - 存储语音质量和性能分析数据';
COMMENT ON COLUMN voice_analytics.id IS '语音分析记录唯一标识ID';
COMMENT ON COLUMN voice_analytics.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN voice_analytics.session_id IS '会话唯一标识';
COMMENT ON COLUMN voice_analytics.recording_type IS '录音类型：NOTE/CONVERSATION/SCHEDULE';
COMMENT ON COLUMN voice_analytics.recording_id IS '关联录音记录ID';
COMMENT ON COLUMN voice_analytics.duration_seconds IS '录音时长（秒）';
COMMENT ON COLUMN voice_analytics.audio_quality_score IS '音频质量评分（0.00-10.00）';
COMMENT ON COLUMN voice_analytics.speech_rate_wpm IS '语速（每分钟词数）';
COMMENT ON COLUMN voice_analytics.pause_frequency IS '停顿频率';
COMMENT ON COLUMN voice_analytics.volume_consistency IS '音量一致性';
COMMENT ON COLUMN voice_analytics.clarity_score IS '清晰度评分（0.00-10.00）';
COMMENT ON COLUMN voice_analytics.pitch_analysis IS '音调分析结果（JSON格式）';
COMMENT ON COLUMN voice_analytics.frequency_analysis IS '频率分析结果（JSON格式）';
COMMENT ON COLUMN voice_analytics.noise_level IS '噪音水平';
COMMENT ON COLUMN voice_analytics.voice_activity_ratio IS '语音活动比例（0.0000-1.0000）';
COMMENT ON COLUMN voice_analytics.emotion_detected IS '检测到的情绪';
COMMENT ON COLUMN voice_analytics.emotion_confidence IS '情绪检测置信度（0.0000-1.0000）';
COMMENT ON COLUMN voice_analytics.language_detected IS '检测到的语言';
COMMENT ON COLUMN voice_analytics.language_confidence IS '语言检测置信度（0.0000-1.0000）';
COMMENT ON COLUMN voice_analytics.created_at IS '创建时间';

-- 情绪追踪表注释
COMMENT ON TABLE mood_entries IS '情绪追踪表 - 存储用户情绪状态和心理健康数据';
COMMENT ON COLUMN mood_entries.id IS '情绪记录唯一标识ID';
COMMENT ON COLUMN mood_entries.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN mood_entries.mood IS '情绪类型：HAPPY/SAD/ANGRY/SURPRISED/NEUTRAL/EXCITED';
COMMENT ON COLUMN mood_entries.intensity IS '情绪强度（1-10级）';
COMMENT ON COLUMN mood_entries.note IS '情绪备注';
COMMENT ON COLUMN mood_entries.triggers IS '情绪触发因素数组';
COMMENT ON COLUMN mood_entries.context IS '记录上下文：VOICE_SESSION/MANUAL/CONVERSATION';
COMMENT ON COLUMN mood_entries.related_session_id IS '关联会话ID';
COMMENT ON COLUMN mood_entries.location IS '地理位置';
COMMENT ON COLUMN mood_entries.weather IS '天气情况';
COMMENT ON COLUMN mood_entries.created_at IS '创建时间';

-- 用户会话表注释
COMMENT ON TABLE user_sessions IS '用户会话表 - 存储用户活动会话和使用追踪';
COMMENT ON COLUMN user_sessions.id IS '会话记录唯一标识ID';
COMMENT ON COLUMN user_sessions.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN user_sessions.session_id IS '会话唯一标识';
COMMENT ON COLUMN user_sessions.session_type IS '会话类型：VOICE_RECORDING/CONVERSATION/SCHEDULE_MANAGEMENT';
COMMENT ON COLUMN user_sessions.start_time IS '会话开始时间';
COMMENT ON COLUMN user_sessions.end_time IS '会话结束时间';
COMMENT ON COLUMN user_sessions.duration_seconds IS '会话时长（秒）';
COMMENT ON COLUMN user_sessions.activities_count IS '活动次数';
COMMENT ON COLUMN user_sessions.device_info IS '设备信息（JSON格式）';
COMMENT ON COLUMN user_sessions.ip_address IS 'IP地址';
COMMENT ON COLUMN user_sessions.user_agent IS '用户代理字符串';
COMMENT ON COLUMN user_sessions.created_at IS '创建时间';

-- 搜索历史表注释
COMMENT ON TABLE search_history IS '搜索历史表 - 存储用户搜索记录';
COMMENT ON COLUMN search_history.id IS '搜索记录唯一标识ID';
COMMENT ON COLUMN search_history.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN search_history.search_query IS '搜索查询内容';
COMMENT ON COLUMN search_history.search_type IS '搜索类型：voice_notes/conversations/schedules/global';
COMMENT ON COLUMN search_history.results_count IS '搜索结果数量';
COMMENT ON COLUMN search_history.clicked_result_id IS '点击的结果ID';
COMMENT ON COLUMN search_history.clicked_result_type IS '点击的结果类型';
COMMENT ON COLUMN search_history.created_at IS '创建时间';

-- 通知表注释
COMMENT ON TABLE notifications IS '通知表 - 存储系统和用户通知';
COMMENT ON COLUMN notifications.id IS '通知唯一标识ID';
COMMENT ON COLUMN notifications.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN notifications.type IS '通知类型：REMINDER/SYSTEM/ACHIEVEMENT/MOOD_CHECK';
COMMENT ON COLUMN notifications.title IS '通知标题';
COMMENT ON COLUMN notifications.message IS '通知内容';
COMMENT ON COLUMN notifications.data IS '通知数据（JSON格式）';
COMMENT ON COLUMN notifications.is_read IS '是否已读';
COMMENT ON COLUMN notifications.priority IS '优先级：LOW/NORMAL/HIGH/URGENT';
COMMENT ON COLUMN notifications.scheduled_for IS '计划发送时间';
COMMENT ON COLUMN notifications.expires_at IS '过期时间';
COMMENT ON COLUMN notifications.created_at IS '创建时间';

-- 用户成就表注释
COMMENT ON TABLE user_achievements IS '用户成就表 - 存储用户成就和游戏化元素';
COMMENT ON COLUMN user_achievements.id IS '成就记录唯一标识ID';
COMMENT ON COLUMN user_achievements.user_id IS '关联用户ID，级联删除';
COMMENT ON COLUMN user_achievements.achievement_type IS '成就类型：FIRST_RECORDING/STREAK_7_DAYS/QUALITY_IMPROVEMENT';
COMMENT ON COLUMN user_achievements.achievement_name IS '成就名称';
COMMENT ON COLUMN user_achievements.description IS '成就描述';
COMMENT ON COLUMN user_achievements.points IS '获得积分';
COMMENT ON COLUMN user_achievements.badge_icon IS '徽章图标';
COMMENT ON COLUMN user_achievements.unlocked_at IS '解锁时间';
COMMENT ON COLUMN user_achievements.data IS '成就相关数据（JSON格式）';

-- 系统设置表注释
COMMENT ON TABLE system_settings IS '系统设置表 - 存储应用配置设置';
COMMENT ON COLUMN system_settings.id IS '设置记录唯一标识ID';
COMMENT ON COLUMN system_settings.setting_key IS '设置键名，唯一';
COMMENT ON COLUMN system_settings.setting_value IS '设置值';
COMMENT ON COLUMN system_settings.setting_type IS '设置类型：STRING/INTEGER/BOOLEAN/JSON';
COMMENT ON COLUMN system_settings.description IS '设置描述';
COMMENT ON COLUMN system_settings.is_public IS '是否公开设置';
COMMENT ON COLUMN system_settings.created_at IS '创建时间';
COMMENT ON COLUMN system_settings.updated_at IS '更新时间';
