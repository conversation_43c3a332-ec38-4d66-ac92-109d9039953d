## 目标：
 现在需要你对 /Users/<USER>/IdeaProjects/buddyWork/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/NotificationController.java 类的所有接口进行测试，
 给出测试报告，并针对有问题的接口进行修复，最终汇报测试与修复结果。

## 当前项目的开发环境如下：
   1. Java 17 (路径：/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home)
   2. Spring Boot 3.2.0 + MyBatis-Plus
   3. PostgreSQL （使用docker容器启动，配置信息在application.yml文件查看）
   4. Redis缓存（使用docker容器启动，配置信息在application.yml文件查看）
   5. 现在后台项目 @voicehub-backend 是能够正常启动的（需要以jdk17启动，通过export JAVA_HOME=/Users/<USER>/Library/Java/JavaVirtualMachines/ms-17.0.15/Contents/Home && cd VoiceHub/backend/voicehub-backend && mvn spring-boot:run 尽心启动）

   
##  实现目标前须知：
   1. 参考接口格式，使用curl进行测试,可以编写类似 /Users/<USER>/IdeaProjects/buddyWork/VoiceHub/test_conversation_apis.sh 这样的文件进行统一测试
   2. 每次启动或重新启动项目之后，需要调用/Users/<USER>/IdeaProjects/buddyWork/VoiceHub/backend/voicehub-backend/src/main/java/com/voicehub/backend/controller/AuthController.java 的
      /auth/login接口进行登录鉴权，才可以调用后台其他接口，建议统一使用 账号:testuser2025 密码:Test@123456 进行登录
   3. 每次修改后台代码之后需要重新启动项目
   4. 在修复问题时，需要了解系统的功能，不要随意删除代码完成修复，建议阅读 /Users/<USER>/IdeaProjects/buddyWork/VoiceHub/README.md 系统的说明文档，在明白要实现什么功能的基础上进行修复接口
   5. 系统的数据库全部可以在 /Users/<USER>/IdeaProjects/buddyWork/VoiceHub/database/VoiceHub_Final_Schema.sql 该文件中查看，如果需要修改数据库要这里进行更新，同时在/Users/<USER>/IdeaProjects/buddyWork/VoiceHub/database/VoiceHub_数据库架构总结.md中记录

## 计划并执行
   接下来请你实现目标，制定计划并执行。   
